<script lang="ts">

export type OnProcessQueuePetSelectionData = {
  id_pet: number,
  petData: Partial<PetType>,
  type_purpose_list: number[],
  type_doctor_list: number[],
  isCurrentPet: boolean,
  inCurrentStep: number
}

export type OnProcessQueuePetSelection = Array<OnProcessQueuePetSelectionData>

export const getPetName = (pet: PetType) => {
  return pet.name_pet ?? `ペット${pet.id_pet}`
}
</script>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, ComputedRef, nextTick } from 'vue'
import PetPlaceholder from '@/assets/img/petdetail/types/other.png'
import {
  PetType,
  QueueTicketType,
  CustomerType,
  EmployeeType,
  ClinicType
} from '@/types/types'
import { storeToRefs } from 'pinia'
import {
  getSelectPurposeByEmployee,
} from './checkInUtils'
import _ from 'lodash'

import useCustomerStore from '@/stores/customers'
import useCliCommonStore from '@/stores/cli-common'
import useClinicStore from '@/stores/clinics'
import useQueueTicketStore, { ToBeCreatedTicketType } from '@/stores/queue_ticket'
import useEmployeeStore from '@/stores/employees'

const customerStore = useCustomerStore()
const cliCommonStore = useCliCommonStore()
const clinicStore = useClinicStore()
const queueTicketStore = useQueueTicketStore()
const employeeStore = useEmployeeStore()

const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getEmployees } = storeToRefs(employeeStore)
const { getClinic } = storeToRefs(clinicStore)

const emits = defineEmits(['close'])

enum CHECKIN_TYPE_STEP_ENUM {
  PURPOSE_SELECTION = 1,
  DOCTOR_SELECTION = 2,
  CONFIRMATION = 3,
  FINAL_CONFIRMATION = 4 // THIS IS FOR CHECKIN_4(new pet for existing customer)
}

interface Props {
  customerInfo: CustomerType,
  selectedPetsIds: number[],
  isEdit: boolean,
  queueTicket: QueueTicketType,
  onProcessQueuePetSelection: OnProcessQueuePetSelection,
  closeCallback: (action: 'none' | 'reset' | 'submit' | 'another', data: ToBeCreatedTicketType | null | Array<any>) => Promise<void>
}

const props = withDefaults(defineProps<Props>(), {
  customerInfo: () => ({} as CustomerType),
  selectedPetsIds: () => [],
  isEdit: false,
  queueTicket: () => ({} as QueueTicketType),
  onProcessQueuePetSelection: () => [] as OnProcessQueuePetSelection,
  closeCallback: (action: 'none' | 'reset' | 'submit' | 'another', data: ToBeCreatedTicketType | null | Array<any>) => Promise.resolve()
})

const { isEdit } = props

const purposeByPetMap = ref<Map<number, Set<number>>>(new Map())
const processingQueuePetSelection = ref<OnProcessQueuePetSelection>(props.onProcessQueuePetSelection)
console.log(processingQueuePetSelection.value)
const action = ref<'none' | 'reset' | 'submit' | 'another'>('none')
const forceRender = ref<boolean>(true)
const isClosing = ref<boolean>(false)
const isSubmitting = ref<boolean>(false)

const allowedInCurrentSteps = computed(() => {
  const clinic = getClinic.value as ClinicType & { type_checkin_qt: number } | null | undefined
  return !clinic || clinic.type_checkin_qt === 1 ? 2 : 1
})

const currentPet = computed(() => {
  return processingQueuePetSelection.value.find(process => process.isCurrentPet)
})

const availableEmployees = computed(() => {
  const clinic = getClinic.value as ClinicType & { type_checkin_qt: number } | null | undefined
  const employees = getEmployees.value.filter((employee: EmployeeType) => employee.flg_calendar)
  let filteredEmployees = []
  if (clinic?.type_checkin_qt === 3) {
    filteredEmployees = employees.filter((employee: EmployeeType) => [1, 6].includes(employee.type_occupation))
  }else{
    filteredEmployees = employees.filter((employee: EmployeeType) => getSelectPurposeByEmployee(currentPet.value?.id_pet ?? 0, currentPet.value?.type_purpose_list ?? [], getCliCommonQTVisitPurposeList.value)?.includes(employee.type_occupation ? employee.type_occupation?.toString() : ''))
  }
  return [{ label: '指名なし（最短）',id_employee: -1}, ...filteredEmployees]
})

const inCurrentStepType = computed(() => {
  const clinic = getClinic.value as ClinicType & { type_checkin_qt: number } | null | undefined
  if ((!clinic?.type_checkin_qt || clinic?.type_checkin_qt === 1)) {
    switch(currentPet.value?.inCurrentStep) {
      case 1:
        return CHECKIN_TYPE_STEP_ENUM.PURPOSE_SELECTION
      case 2:
        return CHECKIN_TYPE_STEP_ENUM.DOCTOR_SELECTION
      case 3:
        return CHECKIN_TYPE_STEP_ENUM.CONFIRMATION
      default:
        return CHECKIN_TYPE_STEP_ENUM.FINAL_CONFIRMATION
      }
  }else if(clinic?.type_checkin_qt === 2) {
    return (currentPet.value?.inCurrentStep! - ( allowedInCurrentSteps.value === 1 ? 1 : 0 )) <= allowedInCurrentSteps.value - 1 ? CHECKIN_TYPE_STEP_ENUM.PURPOSE_SELECTION : CHECKIN_TYPE_STEP_ENUM.CONFIRMATION
  }else if(clinic?.type_checkin_qt === 3) {
    return (currentPet.value?.inCurrentStep! - ( allowedInCurrentSteps.value === 1 ? 1 : 0 )) <= allowedInCurrentSteps.value - 1 ? CHECKIN_TYPE_STEP_ENUM.DOCTOR_SELECTION : CHECKIN_TYPE_STEP_ENUM.CONFIRMATION
  }
})

const inCurrentStepLabel = computed(() => {
  switch(inCurrentStepType.value) {
    case CHECKIN_TYPE_STEP_ENUM.PURPOSE_SELECTION:
      return '来院目的の選択'
    case CHECKIN_TYPE_STEP_ENUM.DOCTOR_SELECTION:
      return '担当の選択'
    case CHECKIN_TYPE_STEP_ENUM.CONFIRMATION:
      return '以下の内容で新規受付を作成します。よろしいですか？'
  }
})

const disableNextButton = computed(() => {
  if(inCurrentStepType.value === CHECKIN_TYPE_STEP_ENUM.PURPOSE_SELECTION) {
    return currentPet.value?.type_purpose_list.length === 0
  }else if(inCurrentStepType.value === CHECKIN_TYPE_STEP_ENUM.DOCTOR_SELECTION) {
    return currentPet.value?.type_doctor_list.length === 0
  }
  return false
})

const onFinalConfirmation = computed(() => {
  if(props.customerInfo?.customer_tel) {
    const flgNewPet = processingQueuePetSelection.value.some((pet) => !pet?.petData?.code_pet)
    if(flgNewPet) return !!!currentPet.value ? false : currentPet.value.inCurrentStep > (allowedInCurrentSteps.value + 1)
  }
  return !!!currentPet.value ? false : currentPet.value.inCurrentStep > (allowedInCurrentSteps.value) // add 1 for overview before final confirmation
})

const allProcessingQueuePetSelection = computed(() => {
  return isClosing.value ? [...queueTicketStore.getTicketsToBeCreated.map((ticket: any) => ticket.pet_name_ui.map((pet: any) => pet.data) as OnProcessQueuePetSelection),] : [
    ...queueTicketStore.getTicketsToBeCreated.map((ticket: any) => ticket.pet_name_ui.map((pet: any) => pet.data) as OnProcessQueuePetSelection),
     processingQueuePetSelection.value
  ]
})

const isPurposeSelected = (purposeId: number) => {
  return currentPet.value?.type_purpose_list.includes(purposeId) ?? false
}

const isEmployeeSelected = (employeeId: number) => {
  return currentPet.value?.type_doctor_list.includes(employeeId) ?? false
}

const createQueueTicketData = (): ToBeCreatedTicketType => {
  let queueTicketData = {
    ...(props.queueTicket ? props.queueTicket : {}),
    queue_detail: {} as any,
    petList: [] as any,
    id_pet: [] as any,
    pet_name_ui: [] as any
  }
  processingQueuePetSelection.value.forEach((pet) => {
    queueTicketData.queue_detail[pet.id_pet] = {
      type_purpose_list: pet.type_purpose_list,
      type_doctor_list: pet.type_doctor_list
    }
    queueTicketData.petList.push(pet.petData)
    queueTicketData.id_pet.push(pet.id_pet)
    queueTicketData.pet_name_ui.push({
      id_pet: pet.id_pet,
      name_pet: pet.petData.name_pet,
      data: pet
    })
  })
  return queueTicketData
}

const getPurposeLabel = (purpose: number[], joinBy?: string) => {
  console.log(joinBy)
  const purposeLabels = purpose.map((p: any) => getCliCommonQTVisitPurposeList.value.find((p2: any) => p2.id_cli_common == p)?.label)
  return joinBy ? purposeLabels.join(joinBy) : purposeLabels
}

const getEmployeeLabel = (employee: number[], joinBy?: string) => {
  const employeeLabels = employee.map((e: any) => {
    const employee = getEmployees.value.find((e2: any) => e2.id_employee == e)
    return !employee ? '指名なし（最短）' : `${employee?.name_family} ${employee?.name_first}`
  })
  return joinBy ? employeeLabels.join(joinBy) : employeeLabels
}

const handleTogglePurpose = (purpose: number) => {
  if (!currentPet.value) return

  if(currentPet.value.type_purpose_list.includes(purpose)) {
    currentPet.value.type_purpose_list = currentPet.value.type_purpose_list.filter((p: any) => p !== purpose)
  } else {
    currentPet.value.type_purpose_list.push(purpose)
  }
}

const handleToggleEmployee = (employeeId: number) => {
  if (!currentPet.value) return
  if(currentPet.value.type_doctor_list.includes(employeeId)) {
    currentPet.value.type_doctor_list = currentPet.value.type_doctor_list.filter((p: any) => p !== employeeId)
  } else {
    // currentPet.value.type_doctor_list = [] // only one is allowed
    currentPet.value.type_doctor_list.push(employeeId)
  }
}

const handleNext = (pet?: OnProcessQueuePetSelectionData) => {
  console.log(pet)
  nextTick(() => {
    const clonedProcessingQueuePetSelection = _.cloneDeep(processingQueuePetSelection.value)
    if (pet) {
      let petFound = false
      clonedProcessingQueuePetSelection.forEach((process, index) => {
        clonedProcessingQueuePetSelection[index].isCurrentPet = false
        if(process.id_pet === pet.id_pet && !petFound) {
          clonedProcessingQueuePetSelection[index].isCurrentPet = true
          clonedProcessingQueuePetSelection[index].inCurrentStep = 1
          petFound = true
        }
        if(petFound) {
          clonedProcessingQueuePetSelection[index].inCurrentStep = 1
        }
      })
      processingQueuePetSelection.value = clonedProcessingQueuePetSelection
      return
    }
    const currentPetIndex = clonedProcessingQueuePetSelection.findIndex(process => process.isCurrentPet)
    if (currentPetIndex !== -1 && currentPetIndex !== undefined) {
      const foundCurrentPet = clonedProcessingQueuePetSelection[currentPetIndex]
      if ((currentPetIndex + 1) < clonedProcessingQueuePetSelection.length ) { 
        if (foundCurrentPet.inCurrentStep === allowedInCurrentSteps.value) {
          clonedProcessingQueuePetSelection[currentPetIndex].isCurrentPet = false
          clonedProcessingQueuePetSelection[currentPetIndex + 1].isCurrentPet = true
          clonedProcessingQueuePetSelection[currentPetIndex + 1].inCurrentStep = 1
        } else {
          clonedProcessingQueuePetSelection[currentPetIndex].inCurrentStep++
        }
      } else {
        // show last step
        clonedProcessingQueuePetSelection[currentPetIndex].inCurrentStep++
      }
    }
    processingQueuePetSelection.value = [
      ..._.cloneDeep(clonedProcessingQueuePetSelection)
    ]
    forceRender.value = false
  }).then(() => {
    forceRender.value = true
  })
}

const handlePrevious = () => {
  const currentPetIndex = processingQueuePetSelection.value.findIndex(process => process.isCurrentPet)
  let beforeSettingInCurrentStep = false
  if (currentPetIndex !== -1 && currentPetIndex !== undefined) {
    const foundCurrentPet = processingQueuePetSelection.value[currentPetIndex]
    if(processingQueuePetSelection.value[currentPetIndex].inCurrentStep > allowedInCurrentSteps.value) {
      processingQueuePetSelection.value[currentPetIndex].inCurrentStep = allowedInCurrentSteps.value
      beforeSettingInCurrentStep = allowedInCurrentSteps.value === 1
    }
    if (currentPetIndex > 0) {
      if ((foundCurrentPet.inCurrentStep - (allowedInCurrentSteps.value === 1 ? 1 : 0)) === allowedInCurrentSteps.value - 1) {
        if (!beforeSettingInCurrentStep) { 
          processingQueuePetSelection.value[currentPetIndex - 1].isCurrentPet = true
          processingQueuePetSelection.value[currentPetIndex].isCurrentPet = false
        }
        processingQueuePetSelection.value[currentPetIndex].inCurrentStep = allowedInCurrentSteps.value
      } else {
        processingQueuePetSelection.value[currentPetIndex].inCurrentStep--
      }
    } else {
      // show pet selection
      if((foundCurrentPet.inCurrentStep - ( allowedInCurrentSteps.value === 1 ? 1 : 0 )) === allowedInCurrentSteps.value - 1) {
        closeModal()
      } else {
        processingQueuePetSelection.value[currentPetIndex].inCurrentStep--
      }
    }
  }
}

const handleReset = () => {
  action.value = 'reset'
  closeModal()
}

const handleSubmit = () => {
  action.value = 'submit'
  closeModal()
}

onMounted(() => {

  if(isEdit && props.queueTicket) {
    const { queueTicket } = props
    for(let petId in (queueTicket as any).queue_detail) {
      const purposeList = (queueTicket as any).queue_detail[petId].type_purpose_list
      purposeByPetMap.value.set(Number(petId), new Set([...purposeList]))
    }
  }
})

// onUnmounted(async () => {
//   if(props.closeCallback && _.isFunction(props.closeCallback)) {
//     await props.closeCallback(action.value, action.value === 'submit' || action.value === 'another' ? createQueueTicketData() : action.value === 'reset' ? [] : processingQueuePetSelection.value)
//   }
// })

const closeModal = async () => {
  isClosing.value = true
  isSubmitting.value = true
  if(props.closeCallback && _.isFunction(props.closeCallback)) {
    await props.closeCallback(action.value, action.value === 'submit' || action.value === 'another' ? createQueueTicketData() : action.value === 'reset' ? [] : processingQueuePetSelection.value)
  }
  isSubmitting.value = false
  emits('close')
}


</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
      <div class="header row items-center justify-between" v-if="onFinalConfirmation">
        <div class="row gap-4" style="align-items: baseline;">
          <template v-if="customerInfo.id_customer">
            <span class="text-regular weighted">
              {{customerInfo.name_family}}&nbsp;&nbsp;{{customerInfo.name_first}}
            </span>
            <span class="text-medium">{{ customerStore.getCustomerHonorific(customerInfo) }}</span>
          </template>
          <template v-else>
            <span class="text-regular weighted">
              新規オーナー さま
            </span>
          </template>
        </div>
        <div>
          <span class="text-small normal text-red cursor-pointer" @click="handleReset">
            やり直す
          </span>
        </div>
      </div>
      <q-card-section class="qt-wrapper" :style="{ paddingTop: onFinalConfirmation ? '0px !important' : '40px' }" v-if="currentPet">
        <div class="flex justify-between items-center info-content">
          <div class="info row gap-4 items-center full-width">
            <template v-if="inCurrentStepType !== CHECKIN_TYPE_STEP_ENUM.CONFIRMATION && inCurrentStepType !== CHECKIN_TYPE_STEP_ENUM.FINAL_CONFIRMATION">
              <div class="pet-avatar no-border">
                <img :src="(currentPet.petData as any).thumbnail_path1 || (currentPet.petData as any).thumbnail_path2 ? (currentPet.petData as any).thumbnail_path1 || (currentPet.petData as any).thumbnail_path2 : PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder" />
              </div>
              <div class="gap-4 row nowrap" style="align-items: baseline;">
                <span class="text-regular weighted"> {{ getPetName(currentPet.petData as PetType) }} </span>
                <span class="text-medium normal"> {{ customerStore.getPetHonorific(currentPet.petData as PetType) }} </span>
              </div>
              <span class="text-regular normal q-pl-md"> {{ inCurrentStepLabel }} </span>
            </template>
            <template v-else-if="inCurrentStepType === CHECKIN_TYPE_STEP_ENUM.CONFIRMATION">
              <div class="row full-width items-center justify-between">
                <div>
                  <span class="text-small normal text-grey-7">
                    {{ inCurrentStepLabel }} 
                  </span>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="q-mt-md flex gap-8 q-pt-xs info-selection">
          <template v-if="inCurrentStepType === CHECKIN_TYPE_STEP_ENUM.PURPOSE_SELECTION">
            <div class="flex gap-4" style="flex-direction: row; row-gap: 40px; column-gap: 30px;">
              <q-btn
                v-for="purpose in _.cloneDeep(getCliCommonQTVisitPurposeList).filter((v:any) => v.id_clinic == getClinic?.id_clinic)"
                class="content-selection-btn"
                :class="isPurposeSelected(purpose.id_cli_common) ? 'selected' : 'outline-btn'"
                :key="purpose.id_cli_common"
                @click="handleTogglePurpose(purpose.id_cli_common)"
                flat
                :ripple="false"
              >
                <div class="content-selection-btn-content">
                  <span class="text-regular normal"> {{ purpose.label }} </span>
                </div>
              </q-btn>
            </div>
          </template>
          <template v-else-if="inCurrentStepType === CHECKIN_TYPE_STEP_ENUM.DOCTOR_SELECTION">
            <div class="flex gap-4" style="flex-direction: row; row-gap: 40px; column-gap: 30px;">
              <q-btn
                v-for="employee in availableEmployees"
                class="content-selection-btn"
                :class="isEmployeeSelected(Number(employee.id_employee)) ? 'selected' : 'outline-btn'"
                :key="employee.id_employee"
                @click="handleToggleEmployee(Number(employee.id_employee))"
                flat
                :ripple="false"
              >
                <div class="content-selection-btn-content">
                  <span class="text-regular normal"> 
                    {{ employee.id_employee == -1 ? (employee as any).label : `${(employee as any).name_family} ${(employee as any).name_first}` }}
                  </span>
                </div>
              </q-btn>
            </div>
          </template>
          <template v-else-if="inCurrentStepType === CHECKIN_TYPE_STEP_ENUM.CONFIRMATION">
            <template v-if="allProcessingQueuePetSelection.length > 1 && onFinalConfirmation">
              <div class="flex gap-4 full-width" style="flex-direction: column; row-gap: 40px;">
                <div v-for="(ticket, index) in allProcessingQueuePetSelection.reverse()" :key="'ticket-' + index.toString()">
                  <div class="row full-width no-wrap q-pb-md">
                    <span class="text-medium weighted">受付 &nbsp;&nbsp;<span class="text-medium">{{ allProcessingQueuePetSelection.length - index }}</span></span>
                  </div>
                  <div class="pet-overview-row row gap-4 full-width q-pl-lg" v-for="pet in ticket" :key="pet.id_pet">
                    <div class="row no-wrap justify-between items-center full-width">
                    <div class="row gap-4 items-center">
                      <div class="pet-avatar no-border">
                        <img :src="(pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 ? (pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 : PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder" />
                      </div>
                      <div class="gap-4 row nowrap" style="align-items: baseline;">
                        <span class="text-regular weighted"> {{ getPetName((pet as any).petData) }} </span>
                        <span class="text-medium normal"> {{ customerStore.getPetHonorific((pet as any).petData) }} </span>
                      </div>
                    </div>
                    <div v-if="!onFinalConfirmation">
                      <span class="text-medium normal text-blue cursor-pointer" @click="() => handleNext(pet)" >
                        編集
                      </span>
                    </div>
                  </div>
                  <div class="gap-6 row" style="align-items: baseline; padding-top: 5px;">
                    <div class="pet-selection-item" v-if="(pet as any).type_purpose_list.length > 0">
                      <span class="text-medium normal"> {{ getPurposeLabel((pet as any).type_purpose_list, ', ') }} </span>
                    </div>
                    <div class="pet-selection-item" v-if="(pet as any).type_doctor_list.length > 0">
                      <span class="text-medium normal"> {{ getEmployeeLabel((pet as any).type_doctor_list, ',') }} </span>
                    </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="flex gap-4 full-width" style="flex-direction: column; row-gap: 40px;">
                <div class="pet-overview-row row gap-4 full-width" v-for="pet in processingQueuePetSelection" :key="pet.id_pet">
                  <div class="row no-wrap justify-between items-center full-width">
                    <div class="row gap-4 items-center">
                      <div class="pet-avatar no-border">
                        <img :src="(pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 ? (pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 : PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder" />
                      </div>
                      <div class="gap-4 row nowrap" style="align-items: baseline;">
                        <span class="text-regular weighted"> {{ getPetName((pet as any).petData) }} </span>
                        <span class="text-medium normal"> {{ customerStore.getPetHonorific((pet as any).petData) }} </span>
                      </div>
                    </div>
                    <div v-if="!onFinalConfirmation">
                      <span class="text-medium normal text-blue cursor-pointer" @click="() => handleNext(pet)" >
                        編集
                      </span>
                    </div>
                  </div>
                  <div class="gap-6 row" style="align-items: baseline; padding-top: 5px;">
                    <div class="pet-selection-item" v-if="(pet as any).type_purpose_list.length > 0">
                      <span class="text-medium normal"> {{ getPurposeLabel((pet as any).type_purpose_list, ',\u00A0') }} </span>
                    </div>
                    <div class="pet-selection-item" v-if="(pet as any).type_doctor_list.length > 0">
                      <span class="text-medium normal"> {{ getEmployeeLabel((pet as any).type_doctor_list, ',\u00A0') }} </span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>
          <template v-else-if="inCurrentStepType === CHECKIN_TYPE_STEP_ENUM.FINAL_CONFIRMATION">
            <div class="flex gap-4 full-width final-confirmation-new-pet" style="flex-direction: column; row-gap: 40px;">
              <div class="pet-overview-row row gap-4 full-width" v-for="queueTicket in queueTicketStore.getQueueTicketLists" :key="queueTicket.id_queue_ticket">
                <div class="row no-wrap items-center gap-5 full-width">
                  <div class="flex column justify-center items-center gap-5 col-2">
                    <div class="bg-grey-300 heading">受付番号</div>
                    <div class="text-body1 text-weight-bold ticket-number">{{ queueTicket.number_queue_ticket }}</div>
                  </div>
                  <div class="col-10 flex column gap-5">
                    <div class="row gap-4 items-center" v-for="pet in queueTicket.petList" :key="pet.id_pet">
                      <div class="pet-avatar no-border">
                        <img :src="pet?.thumbnail_path1 || pet?.thumbnail_path2 || PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder" />
                      </div>
                      <div class="gap-4 row nowrap" style="align-items: baseline;">
                        <span class="text-regular weighted"> {{ getPetName(pet) }} </span>
                        <span class="text-medium normal"> {{ customerStore.getPetHonorific(pet) }} </span>
                      </div>
                      <div class="gap-6 row" style="align-items: baseline; padding-top: 5px;">
                        <div class="pet-selection-item" v-if="queueTicket.queue_detail[pet.id_pet].type_purpose_list > 0">
                          <span class="text-medium normal"> {{ getPurposeLabel(queueTicket.queue_detail[pet.id_pet].type_purpose_list, ',\u00A0') }} </span>
                        </div>
                        <div class="pet-selection-item" v-if="queueTicket.queue_detail[pet.id_pet].type_doctor_list > 0">
                          <span class="text-medium normal"> {{ getEmployeeLabel(queueTicket.queue_detail[pet.id_pet].type_doctor_list, ',\u00A0') }} </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="pet-overview-row row gap-4 full-width" v-for="pet in processingQueuePetSelection" :key="pet.id_pet">
                <div class="row no-wrap items-center gap-5 full-width">
                  <div class="flex column justify-center items-center gap-5 col-2">
                    <div class="bg-grey-300 heading">受付番号</div>
                    <div class="text-body1 text-weight-bold ticket-number">-</div>
                  </div>
                  <div class="row gap-4 items-center col-10">
                    <div class="pet-avatar no-border">
                      <img :src="(pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 ? (pet as any).petData.thumbnail_path1 || (pet as any).petData.thumbnail_path2 : PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder" />
                    </div>
                    <div class="gap-4 row nowrap" style="align-items: baseline;">
                      <span class="text-regular weighted"> {{ getPetName((pet as any).petData) }} </span>
                      <span class="text-medium normal"> {{ customerStore.getPetHonorific((pet as any).petData) }} </span>
                    </div>
                    <div class="gap-6 row" style="align-items: baseline; padding-top: 5px;">
                      <div class="pet-selection-item" v-if="(pet as any).type_purpose_list.length > 0">
                        <span class="text-medium normal"> {{ getPurposeLabel((pet as any).type_purpose_list, ',\u00A0') }} </span>
                      </div>
                      <div class="pet-selection-item" v-if="(pet as any).type_doctor_list.length > 0">
                        <span class="text-medium normal"> {{ getEmployeeLabel((pet as any).type_doctor_list, ',\u00A0') }} </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="full-width text-blue text-center" style="font-size: 32px;">※内容に誤りがあった場合は受付スタッフまでお知らせください。</div>
          </template>
        </div>
      </q-card-section>
      <q-card-section class="bg-white q-bt action-btns row">
        <div class="flex justify-between row full-width" v-if="onFinalConfirmation">
          <div class="row" style="width: 40%;">
            <q-btn 
              outline 
              class="cancel outline-btn full-width" 
              @click="handlePrevious"
            >
              <span>戻る</span>
            </q-btn>
            <!-- <q-btn 
              outline 
              class="cancel outline-btn full-width" 
              @click="handleAddAnotherTicket"
            >
              <span>別の受付を追加</span>
            </q-btn> -->
          </div>
          <div class="row" style="width: 50%; padding-left: 20px;">
            <q-btn 
              class="next text-white full-width"
              @click="handleSubmit"
              :loading="isSubmitting"
            >
              <span>院内受付をする</span>
            </q-btn>
          </div>
        </div>
        <div class="flex justify-between row full-width" v-else>
          <q-btn 
            outline 
            class="cancel outline-btn" 
            @click="handlePrevious"
          >
            <span>戻る</span>
          </q-btn>
          <q-btn 
            class="next text-white"
            @click="() => handleNext()"
            :disable="disableNextButton"
          >
            <span>次へ</span>
          </q-btn>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.final-confirmation-new-pet {
  .heading {
    border-radius: 4px;
    padding: 5px 10px;
  }
  .ticket-number {
    font-size: 36px;
  }
}
</style>