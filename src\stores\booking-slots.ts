import { defineStore } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

// Types
export interface BookingSlotRequest {
  id_clinic: number
  id_booking_item: number
  id_item_service: number
  date_booking: string
  time_bookable_start: string
  time_bookable_end: string
  flg_unavailable?: boolean
  slot_max?: number
}

export interface BookingSlotResponse {
  id_booking_slot: number
  id_clinic: number
  id_booking_item: number
  date_booking: string
  time_bookable_start: string
  time_bookable_end: string
  flg_unavailable: boolean
  slot_max: number
}

export interface UpdateBookingSlotRequest {
  time_bookable_start?: string
  time_bookable_end?: string
  flg_unavailable?: boolean
  slot_max?: number
}

export interface ApiResponse<T> {
  code: number
  success: boolean
  message: string
  data: T
}

export const useBookingSlotStore = defineStore('bookingSlots', {
  state: () => ({
    currentBookingSlot: null as BookingSlotResponse | null,
    bookingSlots: [] as BookingSlotResponse[],
    loading: false,
    error: null as string | null
  }),

  getters: {
    getCurrentBookingSlot: (state) => state.currentBookingSlot,
    getBookingSlots: (state) => state.bookingSlots,
    isLoading: (state) => state.loading,
    getError: (state) => state.error
  },

  actions: {
    /**
     * Create a new booking slot
     */
    async createBookingSlot(data: BookingSlotRequest): Promise<BookingSlotResponse | null> {
      this.loading = true
      this.error = null

      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST,
          '/booking/tx-slots/',
          data
        ) as ApiResponse<BookingSlotResponse>

        if (response.success) {
          this.currentBookingSlot = response.data
          return response.data
        }

        throw new Error(response.message)
      } catch (error: any) {
        this.error = error.message || '予約枠の作成に失敗しました。'
        mtUtils.autoCloseAlert(this.error)
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * Update an existing booking slot
     */
    async updateBookingSlot(
      id: number,
      data: UpdateBookingSlotRequest
    ): Promise<BookingSlotResponse | null> {
      this.loading = true
      this.error = null

      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.PUT,
          `/booking/tx-slots/?id_booking_slot=${id}`,
          data
        ) as ApiResponse<BookingSlotResponse>

        if (response.success) {
          this.currentBookingSlot = response.data
          return response.data
        }

        throw new Error(response.message)
      } catch (error: any) {
        this.error = error.message || '予約枠の更新に失敗しました。'
        mtUtils.autoCloseAlert(this.error)
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * Reset store state
     */
    resetState() {
      this.currentBookingSlot = null
      this.bookingSlots = []
      this.error = null
    }
  }
})

export default useBookingSlotStore 