<script setup lang="ts">
import { onMounted, ref, defineAsyncComponent, computed, reactive } from 'vue'
import { Loading } from 'quasar'
import dayjs from 'dayjs'

import MtHeader from '@/components/layouts/MtHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtTable2 from '@/components/MtTable2.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'

import { storeToRefs } from 'pinia'
import useCommonStore from '@/stores/common'
import useEmployeeStore from '@/stores/employees'
import useCustomerStore from '@/stores/customers'
import useIllnessHistoryStore from '@/stores/illness-history'
import useClinicalFilesStore from '@/stores/clinical-files'

/** -------------- Utility imports ----------- **/
import mtUtils from '@/utils/mtUtils'
import { typeFile } from '@/utils/enum'
import usePetStore from '@/stores/pets'
import fileLogo from '@/assets/img/clinicalFiles/file.png'
import UpdateClinicalFileModal from '@/pages/petInfo/diagnostic/UpdateClinicalFileModal.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import { getFullPetNameWithoutHonorific } from '@/utils/aahUtils'

/* ---------------------- Async Page Imports ---------------------- */
const ViewPetDetailModal = defineAsyncComponent(
  () => import('@/pages/master/customerPet/ViewPetDetailModal.vue')
)

const commonStore = useCommonStore()
const employeeStore = useEmployeeStore()
const customerStore = useCustomerStore()
const petStore = usePetStore()
const { getCustomerListOptions } = storeToRefs(customerStore)
const illnessHistoryStore = useIllnessHistoryStore()
const clinicalFilesStore = useClinicalFilesStore()
const { getClinicalFiles } = storeToRefs(clinicalFilesStore)


const pageTitle = ref('臨床ファイルリストを検索')
const customerList = ref([])
const customerListDefault = reactive([])

const moveNext = (e: any) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    ;(inputs[index + 1] as HTMLElement).focus()
  } else {
    ;(inputs[1] as HTMLElement).blur()
    search()
  }
}

const searchData = ref({
  date_start: dayjs().format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD'),
  notes_search: '',
  file_type: '',
})

const getTypeAnimal = (id_cm_animal: number) => {
  return commonStore.getCommonTypeAnimalOptionList.find(
    (v: any) => v.id_common == id_cm_animal
  )
}

let pagination = {
  page: 1,
  page_size: 20
}
const scrollAreaRef = ref()
const search = async () => {
  Loading.show({
    backgroundColor: 'transparent',
    spinnerColor: 'black',
    message: 'データ取得中...',
    messageColor: 'black'
  })
  pagination.page = 1
  scrollAreaRef.value?.setScrollPercentage('vertical', 0, 300)
  await clinicalFilesStore.fetchClinicalFiles({...searchData.value, ...pagination})
    .finally(() => {
      Loading.hide()
    })
}

onMounted(async () => {
  search()
})

const showFilePreview = async (clinicalFile) =>{
  
  customerStore.customer = clinicalFile.customer_details
  if(!customerStore?.customer) {
    await mtUtils.autoCloseAlert('このファイルに関連する顧客はいません。');
    return
  }
  customerStore.pet = clinicalFile.pet_details
  const data = JSON.parse(JSON.stringify(clinicalFile))
  await mtUtils.popup(UpdateClinicalFileModal, {
    data: data,
    allData: [],
    enableTitleClickFlg: true,
    enableFileCompare: false,
    refreshFlg: false,
    onTitleClick: () => petDetailModal(
      clinicalFile.id_customer, 
      customerStore.pet.id_pet, 
      customerStore.customer.code_customer,
      customerStore.pet.code_pet
    ),
  }, true)
}

const petDetailModal = async (id_customer, id_pet, code_customer, code_pet) => {
    await mtUtils.popup(ViewPetDetailModal, {
      id_customer,
      id_pet,
      code_customer,
      code_pet,
      tab: 1,
      fromPage: 'オーナー・ペット検索',
      pageTitle:'pageTitle'
    })
}

const scrollingParent = ({verticalPercentage}) => {
  if(verticalPercentage < 1) {
    return
  }
  pagination.page += 1
  loadMore()
}

const loadMore = async () => {
  Loading.show({
    backgroundColor: 'transparent',
    spinnerColor: 'black',
    message: 'データ取得中...',
    messageColor: 'black'
  })
  await clinicalFilesStore.fetchClinicalFiles({...searchData.value, ...pagination}, true)
    .finally(() => {
      Loading.hide()
    })
}
const replaceByDefaultImg = (e) => {
  e.target.src = fileLogo
}

const getFileName = (file) => {
  if(!file?.pet_details) {
    return ''
  }
  
  const {code_pet} = file.pet_details
  
  
  return [
    code_pet || '',
    getFullPetNameWithoutHonorific(file.pet_details, file.customer_details) || '', 
    file?.datetime_receive ? dayjs(file?.datetime_receive).format('YYYY/MM/DD') : ''
  ].filter(name => name)
    .join(' ')
}
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }" ref="scrollContainer">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          {{ pageTitle }}
        </q-toolbar-title>
        <!-- For Desktop View -->
        <div class="row mobile-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate
                v-model:date="searchData.date_start"
                :tabindex="1"
                label="開始日：Start"
                outlined
                type="date"
                style="width: 168px"
                @keydown.enter="moveNext"
                @update:date="
                  () => {
                    searchData.date_end = searchData.date_start
                  }
                "
              />
              <MtFormInputDate
                v-model:date="searchData.date_end"
                :tabindex="2"
                class="q-mx-sm"
                label="開始日：End"
                outlined
                type="date"
                style="width: 168px"
                @keydown.enter="moveNext"
              />

              <MtFormPullDown
                label="ファイルタイプ"
                class="q-mr-sm"
                style="width: 168px"
                v-model:selected="searchData.file_type"
                :options="typeFile"
                outlined
              />
              <MtInputForm
                type="text"
                label="ノート検索"
                outlined
                v-model="searchData.notes_search"
                class="search-field q-mr-sm"
                @keydown.enter="moveNext"
              />

              <!-- <q-btn outline @click="openSearchModal">
                詳細検索
                <q-badge color="red" rounded floating />
              </q-btn> -->

              <q-btn
                color="grey-800"
                text-color="white"
                tabindex="4"
                unelevated
                @click="search()"
              >
                <q-icon size="20px" name="search" />検索
              </q-btn>
            </div>
          </div>
        </div>
        <!-- For Mobile/Tablet View -->
        <div class="row desktop-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate
                v-model:date="searchData.date_start"
                class="q-mr-sm ipad-field-size-md"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <MtFormInputDate
                v-model:date="searchData.date_end"
                class="ipad-field-size-md"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <MtFormPullDown
                label="ファイルタイプ"
                class="q-mr-sm"
                style="width: 168px"
                v-model:selected="searchData.file_type"
                :options="typeFile"
                outlined
              />  
              <MtInputForm
                type="text"
                label="ノート検索"
                outlined
                v-model="searchData.notes_search"
                class="search-field q-mr-sm"
                @keydown.enter="moveNext"
              />
              <q-btn
                color="grey-800"
                text-color="white"
                unelevated
                class="q-mx-sm"
                @click="search()"
              >
                <q-icon size="20px" name="search" />
              </q-btn>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>
    <div class="">
      <q-scroll-area ref="scrollAreaRef" @scroll="scrollingParent" class="image-scroll-container" style="" >
        <div  class="flex justify-center items-center" style="width: 100%; padding-left: 16px">
      
          <div class="row  wrap justify-start  q-mt-lg image-container" style="gap: 16px;">
          <div  class=" grid-item flex items-center justify-center" v-for="(file,index) in getClinicalFiles" :key="index">
            <q-card style="width: 100%; height: 100%">
                <q-img
                  v-if="file.type_file == 1"
                  @click="showFilePreview(file)"
                  loading="lazy"
                  class=" rounded-borders"
                  fit="cover"
                  img-class="fetch-list-img"
                  aspect-ratio="1"
                  @error="replaceByDefaultImg"
                  
                  :src="file.thumbnail_path" alt="Item Image"   >
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-negative text-white">
                      Cannot load image
                    </div>
                  </template>
                  <div class="text-subtitle2 image-title text-center flex items-center justify-center" style="background-color: transparent;">
                    <div class="image-name">
                      {{getFileName(file)}}
                    </div>
                  </div>
                </q-img>
                <div class="video-container" v-else-if="file.type_file == 2">
                  <div class="text-subtitle2 text-center video-title flex q-pa-none items-center justify-center z-10" style="">
                    <div class="image-name">
                      {{getFileName(file)}}
                    </div>
                  </div>
                    <video
                      class="fetch-list-img z-1"
                      @click="showFilePreview(file)"
                      :src="file.file_path" alt="Item Image">
                      <source :src="file.file_path" type="video/mp4" />
                    </video>
                </div>
                <q-img
                  v-else-if="file.file_path?.includes('.pdf')"
                  :src="file.thumbnail_path"
                  class=" rounded-borders"
                  fit="fill"
                  img-class="fetch-list-img"
                  
                  @error="replaceByDefaultImg"
                >
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-negative text-white">
                      Cannot load image
                    </div>
                  </template>
                  <div class="text-subtitle2 image-title text-center flex items-center justify-center" style="background-color: transparent;">
                    <div class="image-name">
                      {{getFileName(file)}}
                    </div>
                  </div>
                </q-img>
                <q-img
                  v-else-if="file.type_file == 3"
                  :src="file.thumbnail_path"
                  class=" rounded-borders"
                  @error="replaceByDefaultImg"
                  fit="cover"
                  
                  img-class="fetch-list-img"
                >
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-negative text-white">
                      Cannot load image
                    </div>
                  </template>
                  <div class="text-subtitle2 image-title text-center flex items-center justify-center" style="background-color: transparent;">
                    <div class="image-name">
                      {{getFileName(file)}}
                    </div>
                  </div>
                </q-img>
                <q-img
                  v-else-if="
                            file.file_path?.includes('.mp3') ||
                            file.file_path?.includes('.wav')
                          "
                  src="@/assets/img/clinicalFiles/audio.png"
                  class="  rounded-borders"
                  fit="cover"
                  
                  @error="replaceByDefaultImg"
                  img-class="fetch-list-img"
                >
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-negative text-white">
                      Cannot load image
                    </div>
                  </template>
                  <div class="text-subtitle2 image-title text-center flex items-center justify-center" style="background-color: transparent;">
                    <div class="image-name">
                      {{getFileName(file)}}
                    </div>
                  </div>
                </q-img>
                <q-img 
                  v-else 
                  :src="file.file_path" 
                  @error="replaceByDefaultImg" 
                  fit="cover"
                  
                  img-class="fetch-list-img"
                  class="  rounded-borders "
                 >
                    <template v-slot:error>
                      <div class="absolute-full flex flex-center bg-negative text-white">
                        Cannot load image
                      </div>
                    </template>
                    <div class="text-subtitle2 image-title text-center flex items-center justify-center" style="background-color: transparent;">
                      <div class="image-name">
                        {{getFileName(file)}}
                      </div>
                    </div>
                </q-img>
            </q-card>
          </div>
        </div>
      </div>
    </q-scroll-area>
    </div>
  </q-page>
</template>
<style lang="scss" scoped>
.image-scroll-container {
  margin-top: 60px;
  width: 100%;
  height: calc(100vh - 50px);
}
.video-title, .image-title {
  bottom: 4px;
  right: 4px;
  position: absolute;
  z-index: 10;
  padding: 0;
}

.image-container {
  width: 100%;
  margin: auto;
  height: fit-content;
}
.fetch-list-img {
  max-height: 258px;
  height: 100%;
}
.grid-item {
  flex-basis: calc(100% - 16px);
  cursor: pointer;
  max-height: 280px;
  max-width: 280px;
  aspect-ratio: 300/300;
  @media screen and (min-width: 500px) {
    flex-basis: calc(50% - 16px);
  }
  @media screen and (min-width: 950px) {
    flex-basis: calc(300px - 16px);
  }
  @media screen and (min-width: 1260px) {
    flex-basis: calc(300px - 16px);
  }
  .q-img {
    width: 100%;
    height: 100%;
  }
  .video-container {
    width: 100%; 
    position: relative;
  }
  img,
  .video-container, 
  video {
    width: 100%;
    border-radius: 8px;
    height: 100%;
    object-fit: cover;
    max-height: 300px;
    aspect-ratio: 300/300;
  }
}

.image-name {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 2px 5px;
  font-size: 12px;
  border-radius: 8px;
}
.item-image {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.item-title {
  margin-top: 8px;
  font-size: 16px;
  font-weight: bold;
}
.tableRow {
  width: 100%;
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
