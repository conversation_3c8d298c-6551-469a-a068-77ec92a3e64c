<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { formattedPrice } from '@/utils/helper'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import MtTable2 from '@/components/MtTable2.vue'
import useCartStore from '@/stores/carts'
import useClinicStore from '@/stores/clinics'
import dayjs from 'dayjs'
import useCustomerStore from '@/stores/customers'
import mtUtils from '@/utils/mtUtils'
import CustomerSalesReportPdf from '@/pages/cart/export/pdf/CustomerSalesReportPdf.vue'

const UpdateCustomerModal = defineAsyncComponent(
  () => import('@/pages/master/customerPet/UpdateCustomerModal.vue')
)
const cartStore = useCartStore()
const clinicStore = useClinicStore()

const emits = defineEmits(['close'])
const props = defineProps({ params: {} })

const searchData = ref({
  date_start: dayjs().format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD')
})
const flgCompleted = ref(1)
const disableExport = ref(true)

const rows = ref([]);
const summary = ref({})
const formattedRows = ref([])
const sortOrder = ref<'asc' | 'desc'>('desc')

const columns = [
  { name: 'group_label', label: 'オーナー', field: 'group_label', align: 'left' },
  { name: 'name_pet', label: 'ペット', field: 'name_pet', align: 'left' },
  { name: 'invoice_amount', label: '請求額', field: 'invoice_amount', align: 'right' },
  { name: 'unpaid_amount', label: '未収金', field: 'unpaid_amount', align: 'right' }
]

const toggleSort = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  fetchSalesSummary()
}

const fetchSalesSummary = async () => {
  const params = {
    ...props.params,
    date_start: searchData.value.date_start,
    date_end: searchData.value.date_end,
    flg_completed: 1,
    sort_order: sortOrder.value 
  }

  const res = await cartStore.fetchCustomerSalesSummary(params)
  const data = res.data?.data || {}

  summary.value = data.summary || {}

  const flatRows = []

  for (const customerData of data.customer_summary || []) {
    flatRows.push({
      row_type: 'customer',
      customerName: customerData.customer_name,
      id_customer: customerData.id_customer,
      summary: customerData.summary,
    })

    for (const pet of customerData.pets || []) {
      flatRows.push({
        row_type: 'pet',
        pet,
      })
    }
  }

  formattedRows.value = flatRows
  disableExport.value = false
}

const closeModal = () => emits('close')

async function openCustomerModal(id_customer: string) {
  await useCustomerStore().selectCustomer(id_customer)
  await mtUtils.popup(UpdateCustomerModal, {
    data: useCustomerStore().getCustomer
  })
}

const exportCSV = () => {
  const csvContent = [
    columns.map(col => col.label).join(','),  // Header row
    ...formattedRows.value
      .filter(row => row.row_type === 'pet')
      .map(row => {
        const customer = formattedRows.value
          .slice(0, formattedRows.value.indexOf(row))
          .reverse()
          .find(r => r.row_type === 'customer')
        return columns.map(col => {
          if (col.name === 'group_label') return customer?.customerName || ''
          return row.pet[col.field] ?? ''
        }).join(',')
      })
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const fileName = `顧客別売上_${searchData.value.date_start.replaceAll('/', '')}-${searchData.value.date_end.replaceAll('/', '')}_${clinicStore.getClinic.name_clinic_display}.csv`

  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.setAttribute('download', fileName)
  link.click()
  URL.revokeObjectURL(link.href)
}

const exportPDF = () => {
  mtUtils.pdfRender(CustomerSalesReportPdf, {
    resultList: [...formattedRows.value],
    dateParams: {
      date_start: searchData.value.date_start,
      date_end: searchData.value.date_end
    },
    columns: columns
  })
}

const copytoclipboard = () => {
  const tsvContent = [
    columns.map(col => col.label).join('\t'),
    ...formattedRows.value
      .filter(row => row.row_type === 'pet')
      .map(row => {
        const customer = formattedRows.value
          .slice(0, formattedRows.value.indexOf(row))
          .reverse()
          .find(r => r.row_type === 'customer')
        return columns.map(col => {
          if (col.name === 'group_label') return customer?.customerName || ''
          return row.pet[col.field] ?? ''
        }).join('\t')
      })
  ].join('\n')

  navigator.clipboard.writeText(tsvContent)
    .then(() => mtUtils.autoCloseAlert('コピーしました！'))
    .catch(err => console.error('コピーに失敗しました', err))
}

onMounted(() => fetchSalesSummary())
</script>

<template>
  <div style="width: calc(100vw - 50px); overflow-x: hidden;">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          集計 : オーナー別
        </q-toolbar-title>
        <div class="flex items-center">
          <MtFormInputDate v-model:date="searchData.date_start" outlined label="会計日：Start" type="date"
            @update:date="fetchSalesSummary" autofocus />
          <MtFormInputDate v-model:date="searchData.date_end" outlined class="q-mx-sm" type="date" label="会計日：End"
            @update:date="fetchSalesSummary" />
          <!-- <MtFormRadiobtn v-model="flgCompleted" label="完了会計のみ" :val="1" @update:modelValue="fetchSalesSummary" />
          <MtFormRadiobtn v-model="flgCompleted" label="未完のみ" :val="0" class="q-mr-md" @update:modelValue="fetchSalesSummary" /> -->
        </div>
      </q-toolbar>
    </MtModalHeader>

    <q-card-section class="q-px-lg content" style="overflow-x: auto;">
      <div class="header-info">
        <div class="left">
          <span class="title">集計期間</span>
          <span class="q-ml-md">{{ searchData.date_start + ' ~ ' + searchData.date_end }}</span>
        </div>
        <div class="right">
          <span class="title">{{ clinicStore.getClinic.code_clinic }} {{ clinicStore.getClinic.name_clinic_display
            }}</span>
          <span class="caption1 regular">
            {{ '出力日: ' + new Date().toLocaleString('ja-JP', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
            }) }}
          </span>
        </div>
      </div>
      <q-separator color="dark" size="2px" class="q-my-sm" />

      <!-- <div class="text-right q-mb-md">
        <div>顧客別 請求合計: {{ formattedPrice(summary.total_invoice_amount) }}円</div>
        <div>顧客別 未収合計: {{ formattedPrice(summary.total_unpaid_amount) }}円</div>
      </div> -->

      <MtTable2
        :columns="columns"
        :rows="formattedRows"
        :rowsBg="true"
        flat
        no-data-message="登録がありません。"
        no-result-message="該当のデータが見つかりませんでした">

        <template v-slot:thead="{ columns }">
          <th
            v-for="(col, index) in columns"
            :key="col.name"
            :class="['text-' + (col.align || 'left')]">
            <div style="display: inline-flex; align-items: center; white-space: nowrap;">
              <span>{{ col.label }}</span>

              <q-icon
                v-if="col.tooltip"
                name="info"
                size="14px"
                color="primary"
                class="q-ml-xs cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <div v-html="col.tooltip" style="padding: 15px; white-space: normal;" />
                </q-popup-proxy>
              </q-icon>

              <!-- Sort icon for 請求額 -->
              <div
                v-if="col.name === 'invoice_amount'"
                class="q-ml-xs cursor-pointer"
                @click="toggleSort">
                <q-icon
                  :name="sortOrder === 'desc' ? 'arrow_downward' : 'arrow_upward'"
                  size="16px"
                  color="primary"
                  class="cursor-pointer">
                  <q-tooltip>
                    {{ sortOrder === 'desc' ? '請求額が多い順に並び替え' : '請求額が少ない順に並び替え' }}
                  </q-tooltip>
                </q-icon>
              </div>
            </div>
          </th>
        </template>

        <template #body="{ row }">
          <tr v-if="row.row_type === 'customer'" class="heading2">
            <td>
              <div class="q-ml-sm text-blue cursor-pointer" @click="openCustomerModal(row.id_customer)">
                {{ row.customerName }}
              </div>
            </td>
            <td></td>
            <td class="text-right">{{ formattedPrice(row.summary?.total_invoice_amount || 0) }}</td>
            <td class="text-right">
              <span v-if="row.summary?.total_unpaid_amount > 0">
                {{ formattedPrice(row.summary?.total_unpaid_amount) }}
              </span>
            </td>
          </tr>

          <tr v-else-if="row.row_type === 'pet'" class="heading3">
            <td></td>
            <td>{{ row.pet.name_pet }}</td>
            <td class="text-right">{{ formattedPrice(row.pet.invoice_amount) }}</td>
            <td class="text-right">
              <span v-if="row.pet.unpaid_amount > 0">
                {{ formattedPrice(row.pet.unpaid_amount) }}
              </span>
            </td>
          </tr>
        </template>
      </MtTable2>
    </q-card-section>

    <q-card-section class="q-bt bg-white">
      <div class="text-center">
        <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn class="q-ml-md" color="primary" unelevated @click="exportCSV()">
          <q-icon name="description" class="q-mr-sm" />
          CSVダウンロード
        </q-btn>
        <q-btn :disable="disableExport" class="q-ml-md" color="primary" unelevated @click="exportPDF()">
          <q-icon name="picture_as_pdf" class="q-mr-sm" />
          PDFダウンロード
        </q-btn>
        <q-btn :disable="disableExport" class="q-ml-md" outline @click="copytoclipboard()">
          <q-icon name="content_copy" class="q-mr-sm" />
          コピー
        </q-btn>
      </div>
    </q-card-section>
  </div>
</template>

<style scoped>
:deep(.q-table thead tr) {
  height: 36px;
  padding: 2px 7px;
  /* color: #000; */
}

:deep(.q-table tbody td) {
  height: auto;
  padding: 2px 7px;
  /* color: #000 */
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.left {
  display: flex;
  gap: 8px;
}

.right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.heading1 {
  font-size: 1.2em;
  padding: 4px;
  background: #757575;

  td {
    color: #fff;
  }
}

.heading2 {
  font-size: 1em;
  padding: 4x;
  background: #eee;

  td {
    color: #000;
    font-weight: bold
  }
}

.heading3 {
  font-weight: normal;
}

.indent-2 {
  margin-left: 2rem;
}

.indent-3 {
  margin-left: 2rem;
}
</style>
