import { defineStore } from 'pinia'
import { type Component, markRaw } from 'vue'

interface KoekaruModalStoreState {
  recordingModalShow: boolean
  notificationModalShow: boolean
  recordingModalData: any
  notificationModalData: any
}

export const useKoekaruModalStore = defineStore('koekaru-modal', {
  state: (): KoekaruModalStoreState => ({
    recordingModalShow: false,
    notificationModalShow: false,
    recordingModalData: null,
    notificationModalData: null
  }),
  persist: true,
  getters: {
    // Recording modal getters
    getRecordingModalShowStatus: (state) => state.recordingModalShow,
    getRecordingModalData: (state) => state.recordingModalData,
    
    // Notification modal getters
    getNotificationModalShowStatus: (state) => state.notificationModalShow,
    getNotificationModalData: (state) => state.notificationModalData,
    
    // Legacy getters for backward compatibility
    getKoekaruModalShowStatus: (state) => state.recordingModalShow,
    getKoekaruModalData: (state) => state.recordingModalData
  },
  actions: {
    // Recording modal actions
    openRecordingModal (data: any = null) {
      this.recordingModalShow = true
      this.recordingModalData = data
    },
    closeRecordingModal () {
      this.recordingModalShow = false
      this.recordingModalData = null
    },
    showRecordingModal () {
      this.recordingModalShow = true
    },
    hideRecordingModal () {
      this.recordingModalShow = false
    },
    setRecordingModalData (data: any) {
      this.recordingModalData = data
    },
    clearRecordingModalData () {
      this.recordingModalData = null
    },
    
    // Notification modal actions
    openNotificationModal (data: any = null) {
      this.notificationModalShow = true
      this.notificationModalData = data
    },
    closeNotificationModal () {
      this.notificationModalShow = false
      this.notificationModalData = null
    },
    showNotificationModal () {
      this.notificationModalShow = true
    },
    hideNotificationModal () {
      this.notificationModalShow = false
    },
    setNotificationModalData (data: any) {
      this.notificationModalData = data
    },
    clearNotificationModalData () {
      this.notificationModalData = null
    },
    
    // Legacy actions for backward compatibility
    openKoekaruModal (data: any = null) {
      this.openRecordingModal(data)
    },
    closeKoekaruModal () {
      this.closeRecordingModal()
    },
    showKoekaruModal () {
      this.showRecordingModal()
    },
    hideKoekaruModal () {
      this.hideRecordingModal()
    },
    setKoekaruModalData (data: any) {
      this.setRecordingModalData(data)
    },
    clearKoekaruModalData () {
      this.clearRecordingModalData()
    }
  }
}) 