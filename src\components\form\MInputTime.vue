<script setup lang="ts">
import { computed } from 'vue';
import { QInputProps } from 'quasar';

type ValidationRule = NonNullable<QInputProps['rules']>[number];

defineOptions({
  name: 'MInputTime'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: 'Time'
  },
  rules: {
    type: Array as () => ValidationRule[],
    default: () => ['time'] as ValidationRule[]
  },
  dense: {
    type: Boolean,
    default: true
  },
  outlined: {
    type: Boolean,
    default: true
  },
  filled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disable: {
    type: Boolean,
    default: false
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  minuteOptions: {
    type: Array as () => number[],
    default: undefined
  }
});

const emit = defineEmits(['update:modelValue']);

const time = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});
</script>

<template>
  <q-input
    :filled="filled"
    v-model="time"
    mask="time"
    :label="label"
    :rules="rules"
    :dense="dense"
    :outlined="outlined"
    :clearable="clearable"
    :disable="disable"
    :readOnly="readOnly"
    v-bind="$attrs">
    <template v-slot:append>
      <q-icon name="access_time" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-time v-model="time" format24h :minute-options="minuteOptions">
            <div class="justify-end items-center row">
              <q-btn v-close-popup label="Close" color="primary" flat />
            </div>
          </q-time>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template> 