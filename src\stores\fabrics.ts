import mtUtils from '@/utils/mtUtils';
import selectOptions from '@/utils/selectOptions';
import { defineStore } from 'pinia'

type FabricOptionType = {
  file_index: number,
  file_path: File,
  id_clinic: number,
  id_customer: number,
  id_pet: number
}

export const useFabricStore = defineStore('fabrics', {
  state: () => ({
    fabric: '',
    additionalMemoCarte: [],
    fabricOption: [] as FabricOptionType[],
  }),
  getters: {
    getFabric: (state) => state.fabric,
    getAdditionalMemoCarte: (state) => state.additionalMemoCarte,
    getFabricOption: (state) => state.fabricOption,
  },
  actions: {
    setFabric(value: string) {
      this.fabric = value
    },
    setFabricOption(value: FabricOptionType) {
      this.fabricOption.push(value)
    },
    setAdditionalMemoCarte(value: any) {
      this.additionalMemoCarte = value
    },
    async createFabric(data: any) {
      let res : any = await mtUtils.callApi(selectOptions.reqMethod.POST,'mst/files', data)
      if (res && res){
        this.fabric = res;
      }
    },
    resetFabric() {
      this.fabric = ''
    },
    resetFabricOption() {
      this.fabricOption = []
    },
    resetAdditionalMemoCarte() {
      this.additionalMemoCarte = []
    },
  }
})

export default useFabricStore
