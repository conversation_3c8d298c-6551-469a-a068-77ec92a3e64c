<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import useCallHistory from '@/stores/call-history'
import { dateFormat, getCustomerName, searchWithHighlight } from '@/utils/aahUtils'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import mtUtils from '@/utils/mtUtils'
import UpdateCallHistoryModal from '@/pages/callHistory/UpdateCallHistoryModal.vue'
import dayjs from 'dayjs'

const callHistoryStore = useCallHistory()
const { getCallHistoriesList } = storeToRefs(callHistoryStore)
const pageTitle = ref('臨床ファイルリストを検索')

const searchData = ref({
  date_start: dayjs().format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD'),
})

const columns = [
  // incoming call time
  {
    name: 'datetime_call_start',
    label: '着信時刻',
    field: 'datetime_call_start',
    align: 'left',
    style: 'width: 15%'
  },
  //  Talk Time
  {
    name: 'call_duration',
    label: '通話時間',
    field: 'call_duration',
    align: 'left',
    style: 'width: 8%'
  },
  // Target number
  {
    name: 'phone',
    label: '対象番号',
    field: 'phone',
    align: 'left',
    style: 'width: 11.7%'
  },
  // Owner Name
  {
    name: 'name_customer',
    label: 'オーナー',
    field: 'name_customer',
    align: 'left',
    style: 'width: 12.8%'
  },
  // pet name
  {
    name: 'name_pet',
    label: 'ペット名',
    field: 'name_pet',
    align: 'left',
    style: 'width: 11.7%'
  },
  // memo chart
  {
    name: 'memo',
    label: 'ペット名', //combine all memo-data
    // field: 'memo',
    align: 'center',
    style: 'width: 7%'
  },
  // note
  {
    name: 'memo_chart',
    label: 'メモカルテ化', //combine all memo-data
    // field: 'memo',
    align: 'center',
    style: 'width: 9%'
  },
  {
    name: 'memo',
    label: 'メモ', //combine all memo-data
    field: 'memo',
    align: 'center',
    style: 'width: 22.5%'
  }
]

const moveNext = (e: any) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    ;(inputs[index + 1] as HTMLElement).focus()
  } else {
    ;(inputs[1] as HTMLElement).blur()
    search()
  }
}
const init = async () => {
  // await search()
  console.log("getCallHistoryList ",getCallHistoriesList.value)
}

onMounted(() => {
  init()
})

const search = async() => {
  await callHistoryStore.fetchCallHistories(searchData.value)
}

const loadMore = async (e) => {
  
}

const createCallHistory = async () => {
  await mtUtils.popup(UpdateCallHistoryModal, {
    is_edit: true
  })
}
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          通話履歴
        </q-toolbar-title>
        <div class="row mobile-hide">
          <div class="col-12">
            <div class="flex items-center q-gutter-x-sm">
              <MtFormInputDate
                v-model:date="searchData.date_start as String"
                :tabindex="1"
                autofocus
                label="開始日：Start"
                outlined
                type="date"
                @keydown.enter="moveNext"
                @update:date="
                  () => {
                    searchData.date_end = searchData.date_start
                  }
                "
              />
              <MtFormInputDate
                v-model:date="searchData.date_end as String"
                :tabindex="2"
                class="q-mx-sm"
                label="開始日：End"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <q-btn
                outline
                :tabindex="3"
                unelevated
              >
                詳細検索
              </q-btn>
              <q-btn
                outline
                :tabindex="4"
                unelevated
              >
                クリア
              </q-btn>
              <q-btn
                color="grey-800"
                text-color="white"
                :tabindex="5"
                unelevated
                @click="search()"
              >
                <q-icon size="20px" name="search" />検索
              </q-btn>
            </div>
          </div>
        </div>
        <div class="row desktop-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate
                v-model:date="searchData.date_start as String"
                :tabindex="1"
                autofocus
                label="開始日：Start"
                outlined
                type="date"
                @keydown.enter="moveNext"
                @update:date="
                  () => {
                    searchData.date_end = searchData.date_start
                  }
                "
              />
              <MtFormInputDate
                v-model:date="searchData.date_end as String"
                :tabindex="2"
                class="q-mx-sm"
                label="開始日：End"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <q-btn
                color="grey-800"
                text-color="white"
                unelevated
                class="q-mx-sm"
                @click="search()"
              >
                <q-icon size="20px" name="search" />
              </q-btn>
            </div>
          </div>
        </div>
      </q-toolbar>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 text-grey-700 q-ml-none">
          <div class="flex justify-start items-center">
            <span>検索結果：</span>
            <span>8件</span>
          </div>
        </q-toolbar-title>
        <q-btn color="primary" icon="add" label="通話履歴" @click="createCallHistory" />
      </q-toolbar>
    </MtHeader>
    <MtTable2
      class="q-pt-sm"
      :columns="columns"
      :rows="[]"
      :rowsBg="true"
      :flat="true"
      @virtual-scroll="(e) => loadMore(e)"
      :style="{ height: 'calc(100dvh - 90px)' }"
    >
    </MtTable2>
  </q-page>
</template>

<style scoped lang="scss">

</style>