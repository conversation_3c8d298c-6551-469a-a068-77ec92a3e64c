<template>
  <div class="countdown-content flex column items-center justify-center">
    <div class="text-white countdown-number-parent">
     <div class="countdown-number">{{ countdown }}</div>
      <div class="text-center countdown-label">
        <q-btn flat dense label="Skip"  @click="skipCountdown">
        </q-btn>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  popup: {
    type: Object,
    required: true
  }
})

const emits = defineEmits(['close'])
const countdown = ref(3)
let timer: NodeJS.Timer | null = null

const skipCountdown = () => {
  if (timer) {
    clearInterval(timer)
  }
  countdown.value = 0
  props.popup.isConfirmed = true
  emits('close')
}

onMounted(() => {
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value === 0) {
      if (timer) {
        clearInterval(timer)
      }
      setTimeout(() => {
        props.popup.isConfirmed = true
        emits('close')
      }, 500)
    }
  }, 1000)
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.countdown-content {
  height: 100%;
  width: 100%;
}

.countdown-number-parent {
  font-weight: 700;
  line-height: 0.5;
  margin-top: 10px;
}

.countdown-number {
  /* 70% of 70px */
  font-size: 49px;   
  font-weight: 700;
  margin-top: 10px;
  text-align: center;
}

.countdown-label {
  font-size: 120px;
  font-weight: 700;
  margin-top: -25px;
}

.q-btn.btn--no-hover  {
  display: none;
}
</style>
