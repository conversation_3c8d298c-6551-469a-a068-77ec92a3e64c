<script setup lang="ts">
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
} from 'chart.js';
import { Bar } from 'vue-chartjs';
import { computed } from 'vue';

// Chart.jsに必要なコンポーネントを登録
ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

// チャートエリアのみ背景を塗りつぶすためのカスタムプラグイン
const chartAreaBackgroundPlugin = {
  id: 'chartAreaBackground',
  beforeDraw(chart: any, _args: any, options: any) {
    const { ctx, chartArea: { left, top, width, height } } = chart;
    ctx.save();
    ctx.fillStyle = options?.color || '#ffffff'; // デフォルトは白
    ctx.fillRect(left, top, width, height);
    ctx.restore();
  },
};

// ChartData インターフェース
interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string;
    yAxisID?: string;
  }[];
  weekendIndices?: number[];
}

// コンポーネントのProps定義
const props = defineProps<{
  chartData: ChartData;
  yAxisLeftLabel?: string;  // 左Y軸のタイトル
  yAxisRightLabel?: string; // 右Y軸のタイトル
  options?: object;
}>();

// 数値を「10のべき乗」単位で切り上げる関数
const roundToNearestPowerOfTen = (value: number) => {
  if (value === 0) return 10;
  const power = Math.pow(10, Math.floor(Math.log10(value)));
  return Math.ceil(value / power) * power;
};

const resolvedOptions = computed(() => {
  const isStacked = props.chartData.datasets.some(ds => !!ds.stack)

  const tooltipConfig = isStacked
    ? {
      mode: 'index',
      intersect: false,
      callbacks: {
        footer: (tooltipItems) => {
          const total = tooltipItems.reduce((sum, item) => sum + Number(item.raw || 0), 0)
          return `合計: ${total} 件`
        }
      }
    }
    : {
      mode: 'index',
      intersect: false
    }

  const base = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: isStacked,
        ticks: {
          color: (context) =>
            props.chartData.weekendIndices?.includes(context.index) ? 'red' : '#666',
          callback: (value, index) => props.chartData.labels[index]
        }
      }
    },
    plugins: {
      chartAreaBackground: { color: '#ffffff' },
      legend: { display: true },
      tooltip: tooltipConfig
    }
  }

  if (isStacked) {
    base.scales['y'] = {
      stacked: true,
      beginAtZero: true,
      title: {
        display: true,
        text: props.yAxisLeftLabel || '件数'
      },
      ticks: { precision: 0 }
    }
  } else {
    if (props.yAxisLeftLabel) {
      base.scales['yAxisLeft'] = {
        type: 'linear',
        position: 'left',
        title: { display: true, text: props.yAxisLeftLabel },
        grid: { drawBorder: false, drawTicks: false },
        ticks: { beginAtZero: true }
      }
    }

    if (props.yAxisRightLabel) {
      base.scales['yAxisRight'] = {
        type: 'linear',
        position: 'right',
        title: { display: true, text: props.yAxisRightLabel },
        grid: { drawBorder: false, drawOnChartArea: false },
        ticks: { beginAtZero: true, stepSize: 1, maxTicksLimit: 6 }
      }
    }
  }

  return base
})

</script>

<template>
  <div class="chart-container">
    <Bar
      :data="chartData"
      :options="props.options || resolvedOptions"
      :plugins="[chartAreaBackgroundPlugin]"
    />
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}
</style>
