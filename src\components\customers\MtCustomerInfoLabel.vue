<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import useCustomerStore from '@/stores/customers'

import { getCustomerLabelColor } from '@/utils/aahUtils'

const props = defineProps<{
  customer: {
    code?: number
    fullKanaName?: string
    fullName?: string
    nameCustomerEx?: string
    colorType?: number
  }
  fullnameClass?: string
  showCustomerCode?: boolean
  isClickable?: boolean
}>()

const customerStore = useCustomerStore()

const { all_customers } = storeToRefs(customerStore)
const customerTypeColorByCode = all_customers.value?.find((customer) => {
  return customer.code_customer == props.customer.code
})
const customerTypeColor = computed(() => {
  if (props.customer.colorType) {
    return props.customer.colorType  
  }
  return customerTypeColorByCode?.type_customer_color
})
</script>

<template>
  <span v-if="showCustomerCode" class="caption1 bold text-grey-900" >
    <slot name="customerCode">
      {{ props.customer.code }}
    </slot>
  </span>
  <div class="caption2 regular text-grey-700">
    <slot name="kanaFullname">
      {{ props.customer.fullKanaName }}
    </slot>
  </div>
  <div>
    <span :class="[{ 'text-blue cursor-pointer': isClickable }, fullnameClass]">
      <slot name="fullname">
        {{ props.customer.fullName }}
      </slot>
    </span>
    <slot name="customerColorIcon">
      <q-icon
        v-if="customerTypeColor"
        size="12px"
        name="circle"
        class="q-ml-xs"
        :style="{
          color: getCustomerLabelColor(customerTypeColor)
        }"
        :color="getCustomerLabelColor(customerTypeColor)"
      />
    </slot>
  </div>
  <div v-if="props.customer.nameCustomerEx && props.customer.nameCustomerEx != 'null'" class="caption2 regular text-grey-900">
    <slot v-if="props.customer.nameCustomerEx" name="nameCustomerEx">
      {{ props.customer.nameCustomerEx }}
    </slot>
  </div>
</template>

<style scoped></style>
