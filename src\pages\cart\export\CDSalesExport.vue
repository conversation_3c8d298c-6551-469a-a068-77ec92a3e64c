<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import dayjs from 'dayjs'

import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtTable2 from '@/components/MtTable2.vue'

import useCartStore from '@/stores/carts'
import useClinicStore from '@/stores/clinics'

import { getDateByFormat, getCurrentPetAge } from '@/utils/aahUtils'
import { formattedPrice } from '@/utils/helper'
import { typePetGender, typeItem } from '@/utils/enum'


const props = defineProps({ params: Object })
const emits = defineEmits(['close'])

const cartStore = useCartStore()
const clinicStore = useClinicStore()

const flgCompleted = ref(1)
const searchData = ref({
  date_start: dayjs().format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD')
})
const disableExport = ref(true)

const rows = ref([])

const columns = [
  { name: 'date_order_start', label: '開始日', field: 'date_order_start', align: 'left' },
  { name: 'date_order_end', label: '終了日', field: 'date_order_end', align: 'left' },
  { name: 'number_cart', label: '会計番号', field: 'number_cart', align: 'left' },
  { name: 'code_customer', label: '診察券番号', field: 'code_customer', align: 'left' },
  { name: 'name_customer_display', label: 'オーナー', field: 'name_customer_display', align: 'left' },
  { name: 'code_pet', label: 'ペットCD', field: 'code_pet', align: 'left' },
  { name: 'name_pet', label: 'ペット', field: 'name_pet', align: 'left' },
  { name: 'type_service_item', label: '商品区分', field: 'type_service_item', align: 'left' },
  { name: 'name_category1', label: '大分類', field: 'name_category1', align: 'left' },
  { name: 'name_category2', label: '中分類', field: 'name_category2', align: 'left' },
  { name: 'code_item_service', label: '商品CD', field: 'code_item_service', align: 'left' },
  { name: 'name_item_service', label: '商品名', field: 'name_item_service', align: 'left' },
  { name: 'name_service_item_unit', label: '品名包装単位名', field: 'name_service_item_unit', align: 'left' },
  { name: 'unit_sales', label: '販売単価', field: 'unit_sales', align: 'right' },
  { name: 'unit_price', label: '標準単価', field: 'unit_price', align: 'right' },
  { name: 'sales_ratio', label: '掛率', field: 'sales_ratio', align: 'right' },
  { name: 'quantity', label: '数量', field: 'quantity', align: 'right' },
  { name: 'amount_price', label: '標準合計金額', field: 'amount_price', align: 'right' },
  { name: 'amount_sales', label: '販売合計金額', field: 'amount_sales', align: 'right' },
  // { name: 'total_sales', label: '販売合計金額', field: 'total_sales', align: 'right' },
  { name: 'memo_cart_detail', label: '明細メモ', field: 'memo_cart_detail', align: 'left' },
  { name: 'name_display_employee_cart', label: '会計作成者', field: 'name_display_employee_cart', align: 'left' },
  { name: 'name_display_employee_sales', label: '売上担当者', field: 'name_display_employee_sales', align: 'left' },
  { name: 'zip_code', label: '郵便番号', field: 'zip_code', align: 'left' },
  { name: 'address_prefecture', label: '都道府県', field: 'address_prefecture', align: 'left' },
  { name: 'address_city', label: '市区町村', field: 'address_city', align: 'left' },
  { name: 'address_other', label: 'その他住所', field: 'address_other', align: 'left' },
  { name: 'name_common_animal', label: '動物種', field: 'name_common_animal', align: 'left' },
  { name: 'name_common_breed', label: '品種', field: 'name_common_breed', align: 'left' },
  { name: 'type_pet_gender', label: '性別', field: 'type_pet_gender', align: 'left' },
  { name: 'date_birth', label: '生年月日', field: 'date_birth', align: 'left' },
]

const typePetGenderName = (value) => typePetGender.find((v) => v.value === value)
const typeItemName = (value: any) => typeItem.find((v) => v.value === value)?.label


const flattenedRows = computed(() => {
  return rows.value.map(row => {
    const newRow = { ...row };

    // Format all date_ fields
    Object.keys(newRow).forEach(key => {
      if (key.startsWith('date_') && newRow[key]) {
        newRow[key] = getDateByFormat(newRow[key]);
      }
    });

    const type_service_item = row.flg_service
      ? `サービス: ${row.type_service || ''}`
      : row.type_item
        ? `商品: ${typeItemName(row.type_item)}`
        : '';

    const type_pet_gender = typePetGenderName(row.type_pet_gender)?.label || '';

    return {
      ...newRow,
      type_service_item,
      type_pet_gender,
    };
  });
});



const fetchSalesSummary = async () => {
  const queryParams = {
    ...props.params,
    date_start: searchData.value.date_start,
    date_end: searchData.value.date_end,
    flg_completed: flgCompleted.value
  }
  const res = await cartStore.fetchCDSalesSummary(queryParams)
  rows.value = res.data.data.cart_detail_list || []
  disableExport.value = false
}

const exportCSV = () => {
  const csvContent = [
    columns.map(col => col.label).join(','),
    ...flattenedRows.value.map(row =>
      columns.map(col => {
        const val = row[col.field]
        return typeof val === 'string' && val.includes(',') ? `"${val}"` : val
      }).join(',')
    )
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const fileName = `会計明細_${searchData.value.date_start.replaceAll('/', '')}_${searchData.value.date_end.replaceAll('/', '')}.csv`

  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.setAttribute('download', fileName)
  link.click()
  URL.revokeObjectURL(link.href)
}

const closeModal = () => {
  emits('close')
}

onMounted(fetchSalesSummary)
</script>

<template>
  <div style="width: calc(100vw - 50px); overflow-x: hidden;">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          会計明細単位
        </q-toolbar-title>
        <div class="flex items-center">
          <MtFormInputDate v-model:date="searchData.date_start" outlined label="会計日：Start" type="date"
            @update:date="fetchSalesSummary" autofocus />
          <MtFormInputDate v-model:date="searchData.date_end" outlined class="q-mx-sm" type="date" label="会計日：End"
            @update:date="fetchSalesSummary" />
          </div>
      </q-toolbar>
    </MtModalHeader>

    <q-card-section class="q-px-lg content" style="overflow-x: auto;">
      <div class="header-info">
        <div class="left">
          <span class="title">集計期間</span>
          <span class="q-ml-md">{{ searchData.date_start + ' ~ ' + searchData.date_end }}</span>
        </div>
        <div class="right">
          <span class="title">{{ clinicStore.getClinic.code_clinic }} {{ clinicStore.getClinic.name_clinic_display
            }}</span>
          <span class="caption1 regular">
            {{ '出力日: ' + new Date().toLocaleString('ja-JP', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
            }) }}
          </span>
        </div>
      </div>
      <q-separator color="dark" size="2px" class="q-my-sm" />

      <MtTable2
        :columns="columns"
        :rows="flattenedRows"
        :rowsBg="true"
        flat
        no-data-message="登録がありません。"
        no-result-message="該当のデータが見つかりません">

        <template v-slot:row="{ row }">
          <td
            v-for="(col, index) in columns"
            :key="index"
            class="cursor-pointer"
            :class="{
              'text-left': col.align === 'left',
              'text-right': col.align === 'right'
            }"
            style="white-space: nowrap;">

            <div
              v-if="col.field.startsWith('date_')"
              :key="col.field"
              auto-width>
              <div class="body1 row regular">
                <span>
                  {{
                  row[col.field] ? getDateByFormat(row[col.field]) : ''
                  }}
                </span>
              </div>
            </div>

            <!-- Exception columns -->
            <div v-else-if="['code_customer'].includes(col.field)">
              <div class="body1 regular">{{ row[col.field] }}</div>
            </div>

            <div v-else auto-width>
              <div
                class="body1 regular"
                :class="{
                  'text-right': typeof row[col.field] === 'number' && !col.align,
                  ['text-' + col.align]: col.align && !(typeof row[col.field] === 'number' && !col.align)
                }">
                {{
                row[col.field] === 0
                ? ''
                : typeof row[col.field] === 'number'
                ? formattedPrice(row[col.field])
                : row[col.field]
                }}
              </div>
            </div>

          </td>
        </template>
      </MtTable2>
    </q-card-section>

    <q-card-section class="q-bt bg-white text-center">
      <q-btn class="bg-grey-100 text-grey-800" outline @click="() => emits('close')">キャンセル</q-btn>
      <q-btn class="q-ml-md" color="primary" unelevated @click="exportCSV" :disable="disableExport">
        <q-icon name="description" class="q-mr-sm" />CSVダウンロード
      </q-btn>
    </q-card-section>
  </div>
</template>

<style scoped>
.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.left,
.right {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}
</style>
