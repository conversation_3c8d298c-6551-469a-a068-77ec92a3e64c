<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import MtFormInputText from '@/components/form/MtFormInputText.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import SelectTargetMedicineItem from '@/pages/master/medicine/SelectTargetMedicineItem.vue'
import { groupBy } from 'lodash'

import {
  typeMedicineCategory,
  typeMedicineRegulation,
  typeBodyWeightUnit
} from '@/utils/enum'
import mtUtils from '@/utils/mtUtils'
import {
  Common
} from '@/types/types'
import {
  roundFloat
} from '@/utils/aahUtils'

import { storeToRefs } from 'pinia'
import useManufacturerStore from '@/stores/manufacturers'
import useCommonStore from '@/stores/common'
import { type ItemServiceSearchType, Overwrites, useVettyMasterStore } from '@/stores/vetty-master'

const manufacturerStore = useManufacturerStore()
const vettyMasterStore = useVettyMasterStore()
const commonStore = useCommonStore()
const { getAllManufacturers } = storeToRefs(manufacturerStore)
const { getItemCmdServices }  = storeToRefs(vettyMasterStore)
const { getCommonTypeAnimalOptionList } = storeToRefs(commonStore)

const manufacturersList = ref([])
const manufacturersListDefault = reactive([])
const selectedMedicinesPool = ref([])
const selectedItemService = ref({})
const itemServiceList = ref([])

const flgAdditionalSearchOpen = ref(false)

const poolMedicinesSet = new Set([])

const searchData = reactive<ItemServiceSearchType>({
  name_item_service: '',
  name_short: '',
  name_service_item_unit: '',
  type_medicine_category: '',
  type_medicine_regulation: '',
  id_manufacturer: '',
  type_medicine_species: ''
})

const medicineColumns = [
  {
    name: 'type_animal',
    label: '動物種別',
    field: 'type_animal',
    align: 'left',
    style: 'width: 60px'
  },
  {
    name: 'status1',
    label: '対象',
    field: 'status1',
    align: 'left',
    style: 'width: 60px'
  },
  {
    name: 'status2',
    label: '注意',
    field: 'status2',
    align: 'left',
    style: 'width: 60px'
  },
  {
    name: 'status3',
    label: '禁忌',
    field: 'status3',
    align: 'left',
    style: 'width: 60px'
  }
]

const search = () => {
  vettyMasterStore.fetchCmdItemServiceList(searchData)
  .then(() => {
    itemServiceList.value = getItemCmdServices.value.map((itemService) => {
      return {
        ...itemService,
        medicine_category: getMedicineCategoryType(itemService.medicine.type_medicine_category),
        medicine_regulation: getMedicineCategoryRegulation(itemService.medicine.type_medicine_regulation),
        type_overwrite: Overwrites.NEW,
        isuOptions: [],
        isuOptionsDefault: [],
        flg_overwrite_dosage_table: false,
        flg_disable_medicine_select: false,
        item_service_unit_list: itemService.item_service_unit_list.map((itemServiceUnit, index) => ({ 
          ...itemServiceUnit,
          unit_price: String(parseInt(itemServiceUnit.unit_price)),
          type_overwrite: Overwrites.NEW,
          checked: false 
        }))
      }
    })
  })
}

const addToPool = () => {
  const selectedItemServices = itemServiceList.value.filter((v) => v.checked)
  if(!selectedItemServices.length) return mtUtils.alert('Please select atleast one medicine')
  selectedItemServices.forEach((itemService) => {
    if(!poolMedicinesSet.has(itemService.id_item_service)) {
      poolMedicinesSet.add(itemService.id_item_service)
      selectedMedicinesPool.value.push(itemService)
    }
  })
}

const removeMedicineFromPool = (index, itemService) => {
  selectedMedicinesPool.value.splice(index, 1)
  poolMedicinesSet.delete(itemService.id_item_service)
}

const getMedicineCategoryType = (typeCategory: number) => {
  if(!typeCategory) return ''
  return typeMedicineCategory.find((v) => v.value === typeCategory)?.label || ''
}

const getMedicineCategoryRegulation = (typeRegulation: number) => {
  if(!typeRegulation) return ''
  return typeMedicineRegulation.find((v) => v.value === typeRegulation)?.label || ''
}

const openSelectTargetMedicine = () => {
  mtUtils.popup(SelectTargetMedicineItem, {
    medicinesPool: selectedMedicinesPool.value
  })
}

const getUnitName = (id_unit) => commonStore.getCommonUnitOptionList.find((v) => v.id_common == id_unit)?.name_common
const getTypeBodyWeight = (typeBodyWeight: number) => typeBodyWeightUnit.find((v) => v.value == typeBodyWeight)?.label
const getCmUnitMedicine = (idCmUnitMedicine: number) => commonStore.getCommonOptionList.find((v: Common) => v.id_common == idCmUnitMedicine)?.name_common
const getTypeAnimals = (idCmAnimals: number[]) => {
  let animals = []
  idCmAnimals.forEach((idCmAnimal) => {
    let animal = getCommonTypeAnimalOptionList.value.find((v: Common) => v.id_common === idCmAnimal)?.name_common
    if(animal) animals.push(animal)
  })
  return animals.join(', ')
}

onMounted(async() => {
  await Promise.all([
    manufacturerStore.fetchManufacturers(),
    commonStore.fetchPreparationCommonList({ code_common: [1, 4] })
  ])

  manufacturersList.value = [...getAllManufacturers.value]
  manufacturersListDefault.push(...manufacturersList.value)
})
</script>
<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          検索＆登録プール
        </q-toolbar-title>
          <MtFormInputText
            label="医薬品名（親）"
            v-model="searchData.name_item_service"
          />
          <MtFormInputText
            label="主成分名"
            v-model="searchData.name_short"
            class="q-ml-md"
          />
          <MtFormInputText
            label="品名包装単位名"
            v-model="searchData.name_service_item_unit"
            class="q-ml-md"
          />
          <q-btn
            color="primary"
            @click="search"
            label="Search"
            class="q-ml-md"
          />
          <div class="bg-grey-200 q-ml-md additional-search-menu-btn">
            <q-icon 
              @click="flgAdditionalSearchOpen = !flgAdditionalSearchOpen"
              :name="flgAdditionalSearchOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
              size="24px"
            />
          </div>
      </q-toolbar>
      <transition name="fade-slide">
        <div class="flex items-center gap-4" v-if="flgAdditionalSearchOpen">
          <MtFormPullDown
            :options="typeMedicineCategory"
            label="製剤区分"
            v-model:selected="searchData.type_medicine_category"
            style="min-width: 200px;"
          />
          <MtFormPullDown
            :options="typeMedicineRegulation"
            label="規制区分"
            v-model:selected="searchData.type_medicine_regulation"
            style="min-width: 200px;"
          />
          <MtFilterSelect
            :options="manufacturersList"
            :options-default="manufacturersListDefault"
            v-model:selecting="searchData.id_manufacturer"
            label="製造販売業者名"
          />
          <MtFormRadiobtn
            label="すべて"
            v-model:selected="searchData.type_medicine_species"
            :val="1"
          />
          <MtFormRadiobtn
            label="人体薬"
            v-model:selected="searchData.type_medicine_species"
            :val="2"
          />
          <MtFormRadiobtn
            label="動物医薬"
            v-model:selected="searchData.type_medicine_species"
            :val="3"
          />
        </div>
      </transition>
    </MtHeader>
    <div class="q-ml-md" :style="{'margin-top': flgAdditionalSearchOpen ? '56px' : '16px'}">検索結果</div>
    <div class="flex q-px-md select-view-medicines">
      <div class="medicines">
        <div style="height: 60vh; overflow: auto;">
          <template v-for="itemService in itemServiceList" :key="itemService.id_item_service">
            <div class="flex items-start q-mt-sm">
              <MtFormCheckBox
                v-model:checked="itemService.checked"
              />
              <div class="cursor-pointer" @click="selectedItemService = itemService">
                <div class="text-body1 text-weight-bold"> 
                  <span class="inject-label caption1" v-if="itemService.medicine.flg_inject">注射薬</span>
                  <span 
                    class="q-mr-sm species-label caption1" 
                    :class="[itemService.medicine.type_medicine_species == '1' ? 'type-1' : 'type-2', itemService.medicine.flg_inject ? 'q-ml-sm' : '']"
                    v-if="itemService.medicine.type_medicine_species == '1' || itemService.medicine.type_medicine_species == '2'"
                  >
                    {{ itemService.medicine.type_medicine_species == '1' ? '人体薬' : itemService.medicine.type_medicine_species == '2' ? '動物薬' : '' }}
                  </span>
                  {{ itemService.name_item_service }}
                </div>
                <div>
                  {{ itemService.medicine_category }}
                  <span v-if="itemService.medicine_category && itemService.medicine_regulation">
                    <q-icon name="arrow_right" />
                  </span>
                  {{ itemService.medicine_regulation }}
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="flex justify-end q-mt-md">
          <q-btn
            color="primary"
            @click="addToPool"
            v-if="itemServiceList.length"
          >
           ↓ 候補に追加
          </q-btn>
        </div>
      </div>
      <div class="view-medicine" v-if="selectedItemService && selectedItemService.medicine" style="height: 60vh; overflow: auto;">
        <div class="flex items-between">
          {{ selectedItemService.code_item_service }}: {{ selectedItemService.name_item_service }}
        </div>
        <div>早見表: <q-icon v-if="selectedItemService.medicine.flg_dosage_fixed" name="check" class="text-positive text-weight-bold" size="20px" /> </div>
        <div>per キロ (可変): <q-icon v-if="selectedItemService.medicine.flg_dosage_variable" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        <div>per ヘッド (可変): <q-icon v-if="selectedItemService.medicine.flg_dosage_per_head" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        <div>数量指定: <q-icon v-if="selectedItemService.medicine.flg_dosage_quantity" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        <div>有効成分を指定しない: <q-icon v-if="selectedItemService.medicine.flg_no_efficacy_ingredient" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        <div>デフォルト注射売上量を1にする: <q-icon v-if="selectedItemService.medicine.flg_default_one_sales_quantity" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        <div>医薬品の基本情報: {{ selectedItemService.medicine.memo_package_unit }}</div>
        <div>効能効果・適用（院内）: {{ selectedItemService.medicine.memo_efficacy_clinic }}</div>
        <div>効能効果（オーナー/お薬説明書）: {{ selectedItemService.medicine.memo_efficacy }}</div>
        <div>用法用量: {{ selectedItemService.medicine.memo_dose }}</div>
        <div>使用上の注意: {{ selectedItemService.medicine.memo_alert_clinic }}</div>
        <div>有害事象・副作用・禁忌: {{ selectedItemService.medicine.memo_sideeffect }}</div>
        <div>貯蔵方法・その他: {{ selectedItemService.medicine.memo_stock }}</div>
        <div>NVALリンク: {{ selectedItemService.medicine.url_nval }}</div>
        <div>医薬品 対象動物（禁忌チェック）: {{ getTypeAnimals(selectedItemService.medicine.id_cm_animal) }}</div>

        <div>
          <MtTable2
            :columns="medicineColumns"
            :rows="selectedItemService.medicine.id_cm_animal"
            :rowsBg="true"
            class="custody-table"
            flat
          >
            <template v-slot:row="{ row }">
              <td v-for="(col, index) in medicineColumns" class="cursor-pointer">
                <div v-if="col.field == 'type_animal'" class="text-grey-900 body1 regular">
                  {{ row['label'] }}
                </div>
                <div v-if="col.field == 'status1'" class="text-grey-900 body1 regular">
                  <MtFormRadiobtn v-model:selected="row.status" :disable="true" :val="1" />
                </div>
                <div v-if="col.field == 'status2'" class="text-grey-900 body1 regular">
                  <MtFormRadiobtn v-model:selected="row.status" :disable="true" :val="20" />
                </div>
                <div v-if="col.field == 'status3'" class="text-grey-900 body1 regular">
                  <MtFormRadiobtn v-model:selected="row.status" :disable="true" :val="99" />
                </div>
              </td>
            </template>
          </MtTable2>
        </div>

        <div v-if="selectedItemService.item_service_unit_list.length">
          <div class="text-weight-bold text-body1 text-black q-mt-sm">品名（包装単位:</div>
          <template v-for="itemServiceUnit in selectedItemService.item_service_unit_list" :key="itemServiceUnit.id_item_service_unit">
            <div>{{ itemServiceUnit.code_item_service_unit }}: {{ itemServiceUnit.name_service_item_unit }}</div>
          </template>
        </div>

        <div class="q-my-md row q-col-gutter-xs" v-if="selectedItemService.medicine.flg_dosage_fixed && selectedItemService.type_item == '1'">
          <div class="col-12">
            <table
              class="table-custom-fixed q-mb-lg"
              v-for="[key, value] in Object.entries(groupBy(selectedItemService.dosage_fixed_list, 'id_common'))"
              :key="key"
            >
              <thead>
                <tr>
                  <td class="flex justify-center">
                    {{ `( ${getCommonTypeAnimalOptionList.find((cObj: Common) => cObj.id_common == key)?.label ?? '全種'} )` }}
                  </td>
                  <template v-if="selectedItemService.item_service_unit_list && selectedItemService.item_service_unit_list.length > 0">
                    <template v-for="(item, index) in selectedItemService.item_service_unit_list" :key="index">
                      <td>
                        <q-btn
                          unelevated
                          color="primary"
                          :ripple="false"
                          style="min-width: 150px"
                          class="q-mr-sm q-px-lg full-width full-height"
                          no-caps
                          type="button"
                        >
                          <span>{{ item.name_service_item_unit }}</span>
                        </q-btn>
                      </td>
                    </template>
                  </template>
                </tr>
              </thead>
              <tbody>
                <template v-if="value && value.length > 0">
                  <tr v-for="(fixedDosage, index2) in value" :key="index2">
                    <td>
                      <q-btn
                        unelevated
                        color="primary"
                        :ripple="false"
                        style="min-width: 150px"
                        class="q-mr-sm q-px-lg full-width full-height"
                        no-caps
                        type="button"
                      >
                        <span
                          >{{ roundFloat(fixedDosage.val_weight_min / 1000) }}<span class="body2">kg</span> ~
                          {{ roundFloat(fixedDosage.val_weight_max / 1000) }} <span class="body2">kg</span
                          >{{
                            '  未満 ' +
                            ` ( ${getCommonTypeAnimalOptionList.find((v: Common) => v.value == fixedDosage.id_common)?.label ?? '全種'} )`
                          }}</span
                        >
                      </q-btn>
                    </td>
                    <template v-if="selectedItemService.item_service_unit_list && selectedItemService.item_service_unit_list.length > 0">
                      <template v-for="(item2, index3) in selectedItemService.item_service_unit_list" :key="index3">
                        <td class="fixed-detail-hover text-center q-ba">
                          <div
                            class="cursor-pointer"
                            v-if="
                              fixedDosage.dosage_fixed_detail_list &&
                              fixedDosage.dosage_fixed_detail_list.length > 0 &&
                              fixedDosage.dosage_fixed_detail_list.find(
                                (v) =>
                                  v.id_item_service_unit == item2.id_item_service_unit &&
                                  v.id_dosage_fixed == fixedDosage.id_dosage_fixed
                              )
                            "
                          >
                            {{
                              fixedDosage.dosage_fixed_detail_list.find(
                                (v) =>
                                  v.id_item_service_unit == item2.id_item_service_unit &&
                                  v.id_dosage_fixed == fixedDosage.id_dosage_fixed
                              )?.quantity
                            }} <span class="body2">{{
                              getUnitName(
                                fixedDosage.dosage_fixed_detail_list.find(
                                  (v) =>
                                    v.id_item_service_unit == item2.id_item_service_unit &&
                                    v.id_dosage_fixed == fixedDosage.id_dosage_fixed
                                )?.id_common
                              )
                            }}</span>
                          </div>
                          <div
                            v-else
                            class="fixed-detail-hover-btn bg-grey-050 text-black cursor-pointer full-width full-height"
                          >
                          </div>
                        </td>
                      </template>
                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>

        <div
          v-if="(selectedItemService.medicine.flg_dosage_variable || selectedItemService.medicine.flg_dosage_per_head) && selectedItemService.type_item == '1'"
          class="q-mt-md row q-col-gutter-xs"
        >
          <div class="col-12">
            <p class="q-mb-md">有効成分量 範囲</p>
            <div
              class="flex q-mt-sm"
              v-for="[key, dosageVariables] in Object.entries(groupBy(selectedItemService.dosage_variable_list, 'type_body_weight_unit'))"
            >
              <div class="q-mb-md">
                <q-btn unelevated color="primary" class="q-mr-sm q-px-lg" no-caps type="button">
                  <span v-if="key == '1'">可変 (Kg) </span>
                  <span v-if="key == '2'">可変 (G) </span>
                  <span v-if="key == '3'">可変 </span>
                </q-btn>
              </div>
              <template v-if="dosageVariables && dosageVariables.length > 0">
                <div class="">
                  <q-btn
                    v-for="(variable, indexVar) in dosageVariables"
                    :key="indexVar"
                    unelevated
                    color="white"
                    class="q-mr-md q-mb-md q-ba"
                    no-caps
                    :ripple="false"
                    text-color="black"
                    type="button"
                    style="min-width: 300px"
                  >
                    <span
                      >{{ variable.val_dose_min }}{{ variable.medicines_label }} ~ {{ variable.val_dose_max }} 
                      <span class="body2"
                        >{{ getCmUnitMedicine(variable.id_cm_unit_medicine) }}/{{ getTypeBodyWeight(variable.type_body_weight_unit) }}
                        {{
                          ` ( ${
                            getCommonTypeAnimalOptionList.find((v: Common) => v.value == variable.id_cm_animal)?.label ?? '全種'
                          } ) `
                        }}
                      </span>
                    </span>
                  </q-btn>
                </div>
              </template>
            </div>
          </div>
        </div>

      </div>
      <div v-else></div>
    </div>
    <div class="q-mt-lg q-px-md flex gap-4" v-if="selectedMedicinesPool.length">
      <template v-for="(poolMedicine, index) in selectedMedicinesPool" :key="poolMedicine.id_item_service">
        <q-btn
          rounded
          class="bg-grey-300"
        >
          <div class="flex items-center">
            <div class="text-left">
              {{ poolMedicine.name_item_service }}
              <div>
                {{ poolMedicine.medicine_category }}
                <span v-if="poolMedicine.medicine_category && poolMedicine.medicine_regulation">
                  <q-icon name="arrow_right" />
                </span>
                {{ poolMedicine.medicine_regulation }}
              </div>
            </div>
            <q-icon name="close" class="q-ml-md" @click="removeMedicineFromPool(index, poolMedicine)" />
          </div>
        </q-btn>
      </template>
    </div>
    <div class="flex justify-end q-mt-md q-px-md q-mb-md" v-if="selectedMedicinesPool.length">
      {{ `${selectedMedicinesPool.length}件が追加対象です` }}
      <q-btn
        color="primary"
        label="追加前確認する"
        class="q-ml-md"
        @click="openSelectTargetMedicine"
      />
    </div>
  </q-page>
</template>
<style lang="scss" scoped>
.additional-search-menu-btn {
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
}
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
.fade-slide-enter-to, .fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}
.select-view-medicines {
  padding-bottom: 20px;
  border-bottom: 1px solid $grey-400;
  & > div {
    flex: 1;
    &.medicines {
      padding-right: 16px;
      border-right: 1px solid $grey-400;
    }
    &.view-medicine {
      padding-left: 16px;
    }
  }
}
.inject-label {
  background: #c7ede4;
  border-radius: 4px;
  padding: 3px 8px;
  color: #005f46;
  width: max-content;
}
.species-label {
  width: max-content;
  &.type-1 {
    background: #ede0c7;
    border-radius: 4px;
    padding: 3px 8px;
    color: #573900;
  }
  &.type-2 {
    background: #c0f1ff;
    border-radius: 4px;
    padding: 3px 8px;
    color: #003c4d;
  }
}
</style>