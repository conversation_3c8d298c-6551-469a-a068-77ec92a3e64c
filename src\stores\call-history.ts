import { defineStore } from 'pinia'
import { computed, Ref, ref } from 'vue'
import selectOptions from '@/utils/selectOptions'
import mtUtils from '@/utils/mtUtils'
import { CallHistory } from '@/types/types'

export const useCallHistory = defineStore('call-history', () => {
  
  // State
  const callHistoriesList:Ref<CallHistory> = ref([
    {
      id_call_history: 1,
      datetime_call_start: '2024-09-08',
      call_duration: 23,
      phone: '12345678901',
      id_customer_id: {
        name_customer_display: 'user 1'
      },
      id_pet_id: {
        code_pet: 'pt_123',
        name_pet: 'name of pet'
      },
      memo_call_other: 'this is some note'
    }
  ])
  
  //   Getters
  const getCallHistoriesList = computed(() => callHistoriesList.value)
  
  //   Actions
  const fetchCallHistories = async (data: {}) => {
    let res = await mtUtils.callApi(
      selectOptions.reqMethod.GET,
      'call-history',
      data
    )
    if (res) {
      console.log("response from the server ", res)
      callHistoriesList.value = res
    }
  }
  
  return {
    callHistoriesList,
    getCallHistoriesList,
    fetchCallHistories
  }
})

export default useCallHistory