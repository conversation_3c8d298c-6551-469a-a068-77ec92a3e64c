<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import MtFormInputNumber2 from '@/components/form/MtFormInputNumber2.vue'
import SelectPetModal from '@/pages/queueTicket/checkIn/SelectPetModal.vue'

import mtUtils from '@/utils/mtUtils'
import { event_bus } from '@/utils/eventBus'

import {
  openLargeModal
} from './checkInUtils'

interface Props {
  flgExistingCustomer: boolean
}

const props = withDefaults(defineProps<Props>(), {
  flgExistingCustomer: false
})

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const totalPets = ref(1)
const disableForwardButton = computed(() => totalPets.value <= 0)

const openSelectPurposeModal = () => {
  if(totalPets.value < 1 || totalPets.value > 10 ) return
  const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup
  popupFunction(SelectPetModal, {
    preselectedPetIds: new Array(totalPets.value).fill(null).map((_, idx) => idx + 1)
  }, true)
  totalPets.value = 1
}

const handleConfirmTicket = () => closeModal()

watch(totalPets, (newVal) => {
  if (newVal < 1) {
    totalPets.value = 1
  } else if (newVal > 10) {
    totalPets.value = 10
  }
})

const handleAddPets = () => {
  const { flgExistingCustomer } = props

  if(flgExistingCustomer) return selectPetsForExistingCustomer()
  return openSelectPurposeModal()
}

const selectPetsForExistingCustomer = () => {
  event_bus.emit('add-pets', totalPets.value)
  closeModal()
}

onMounted(() => {
  event_bus.on('confrimTicket', handleConfirmTicket)
})

onUnmounted(() => {
  event_bus.off('confrimTicket', handleConfirmTicket)
})

</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
      <q-card-section class="qt-wrapper">
        <div class="info-content">
          <span class="info text-medium normal">本日受診するペットの数を選択し、『次へ』ボタンを押してください。</span>
        </div>
        <div class="flex justify-center items-center gap-4 q-py-xl info-selection content-center no-wrap">
          <q-btn round class="increase-btn text-white" size="48px" @click="() => { totalPets = Math.min(10, totalPets + 1) }">
            <q-icon name="add"/>
          </q-btn>
          <MtFormInputNumber2
            v-model:value="totalPets"
            class="total-pets"
          />
          <q-btn round class="decrease-btn text-white" size="48px" @click="() => { totalPets = Math.max(1, totalPets - 1) }">
            <q-icon name="remove"/>
          </q-btn>
        </div>
      </q-card-section>
      <q-card-section class="bg-white q-bt action-btns row">
        <div class="flex justify-between row full-width">
          <q-btn 
            outline 
            class="cancel outline-btn" 
            @click="closeModal"
          >
            <span>キャンセル</span>
          </q-btn>
          <q-btn 
            class="next text-white"
            @click="handleAddPets"
            :disable="disableForwardButton"
          >
            <span>次へ</span>
          </q-btn>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<style lang="scss" scoped>
$checkin-btn-dark-blue: #033C71;
.checkin-feat {
  .increase-btn , .decrease-btn {
    width: 80px;
    height: 80px;
    background-color: $checkin-btn-dark-blue;  
  }
  .total-pets {
    :deep(.q-field__control) {
      height: 120px;
      border: 3px solid $checkin-btn-dark-blue;
      border-radius: 20px;
      input {
        color: $checkin-btn-dark-blue;
        font-size: 52px;
        font-weight: 600;
        text-align: center;
        &:focus {
          outline: none !important;
        }  
      }
      &:after{
        content: none;
      }
    }
  }
}

</style>