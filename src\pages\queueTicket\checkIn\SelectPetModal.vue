<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import SelectPurposeDoctorModal, { OnProcessQueuePetSelection, getPetName } from '@/pages/queueTicket/checkIn/SelectPurposeDoctorModal.vue'
import AddPetModal from '@/pages/queueTicket/checkIn/AddPetModal.vue'
import PetPlaceholder from '@/assets/img/petdetail/types/other.png'
import _ from 'lodash'
import { nanoid } from 'nanoid'

import {
  ClinicType,
  CustomerType,
  PetType,
  QueueTicketType,
} from '@/types/types'
import {
  openLargeModal
} from './checkInUtils'
import mtUtils from '@/utils/mtUtils'
import { storeToRefs } from 'pinia'

import { event_bus } from '@/utils/eventBus'

import useCustomerStore from '@/stores/customers'
import useClinicStore from '@/stores/clinics'
import useQueueTicketStore, { ToBeCreatedTicketType, typeCheckInCustomer } from '@/stores/queue_ticket'

const customerStore = useCustomerStore()
const queueTicketStore = useQueueTicketStore()
const clinicStore = useClinicStore()
const { getClinic }  = storeToRefs(clinicStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  customerInfo: CustomerType
  preselectedPetIds: number[]
  queueTicket: QueueTicketType
}
const props = defineProps<Props>()

const customerPets = ref<Partial<PetType>[]>(props.customerInfo?.pets ?? props.preselectedPetIds.map(id => ({id_pet: id})))
const selectedPetsIds = ref<Set<number>>(new Set([]))
const processingQueuePetSelection = ref<OnProcessQueuePetSelection>([])

const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup

const allowedInCurrentSteps = computed(() => {
  const clinic = getClinic.value as ClinicType & { type_checkin_qt: number } | null | undefined
  return !clinic || clinic.type_checkin_qt === 1 ? 2 : 1
})

const allowNewPetSelection = computed(() => {
  return getClinic.value.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_WITH_QR || 
    getClinic.value.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_WITH_NO_QR
})

const disableNextButton = computed(() => processingQueuePetSelection.value.length === 0)

const hasCurrentPet = computed(() => processingQueuePetSelection.value.find(pet => pet.isCurrentPet))

const isPetSelected = (value: PetType) => {
  const foundPet = processingQueuePetSelection.value.find(pet => pet.id_pet.toString() == value.id_pet.toString())
  return !!foundPet
}

const handleTogglePet = (value: PetType, isNewPet?: boolean, existingList?: { typePurposeList: number[], typeDoctorList: number[], isCurrentPet: boolean }) => {
  const foundPetIndex = processingQueuePetSelection.value.findIndex(pet => pet.id_pet.toString() == value.id_pet.toString())
  if(foundPetIndex !== -1) {
    processingQueuePetSelection.value.splice(foundPetIndex, 1)
    return
  }
  processingQueuePetSelection.value.push({
    id_pet: Number(value.id_pet),
    petData: value,
    type_purpose_list: existingList?.typePurposeList ?? [],
    type_doctor_list: existingList?.typeDoctorList ?? [],
    isCurrentPet: existingList ? existingList.isCurrentPet : !!!hasCurrentPet.value,
    inCurrentStep: 1
  })
  if (_.isBoolean(isNewPet)) {
    let hasCurrentPet = false
    processingQueuePetSelection.value.forEach((_pet, index) => {
      const inCurrentStepPurpose = processingQueuePetSelection.value[index].type_purpose_list.length > 0 ? 1 : 0
      const inCurrentStepDoctor = processingQueuePetSelection.value[index].type_doctor_list.length > 0 ? 1 : 0
      if (inCurrentStepPurpose + inCurrentStepDoctor === allowedInCurrentSteps.value) {
        processingQueuePetSelection.value[index].isCurrentPet = false
        processingQueuePetSelection.value[index].inCurrentStep = allowedInCurrentSteps.value
      } else {
        if(!hasCurrentPet) {
          processingQueuePetSelection.value[index].isCurrentPet = true
          processingQueuePetSelection.value[index].inCurrentStep = 1
          hasCurrentPet = true
          if (index > 0) {
            processingQueuePetSelection.value[index - 1].isCurrentPet = false
          }
        }
      }
    })
  }
}

const handleAddNewPet = (openPurposeDoctorModal:boolean = true) => {
 // if (props.preselectedPetIds.length > 0) {
    const lastPet = customerPets.value[customerPets.value.length - 1]
    const lastProcessingPet = processingQueuePetSelection.value[processingQueuePetSelection.value.length - 1]
    customerPets.value.push({
      id_pet: (Number(lastPet.id_pet) + 1).toString()
    })
    if(props?.customerInfo?.customer_tel) {
      const lastPet = customerPets.value[customerPets.value.length - 1]
      const newPets = customerPets.value.filter((pet) => !pet.code_pet)
      lastPet.name_pet = `新しいペット${newPets.length}`
      lastPet.id_pet_ui = nanoid()
    }
    handleTogglePet(customerPets.value[customerPets.value.length - 1] as PetType, true)
    if(openPurposeDoctorModal) handleOpenSelectPurposeDoctorModal()
  }
//}

const handleOpenSelectPurposeDoctorModal = () => {
  const selectedPetsIdsArr = Array.from(selectedPetsIds.value)
  popupFunction(SelectPurposeDoctorModal, {
      customerInfo: props.customerInfo,
      selectedPetsIds: selectedPetsIdsArr,
      queueTicket: props.queueTicket,
      onProcessQueuePetSelection: processingQueuePetSelection.value,
      closeCallback: async (action: 'none' | 'reset' | 'submit' | 'another', data: OnProcessQueuePetSelection | null) => {
        return new Promise((resolve) => {
          if (action === 'submit') {
            queueTicketStore.setToBeCreatedTickets(data as unknown as ToBeCreatedTicketType)
            event_bus.emit('confrimTicket', resolve)
            closeModal()
          } else if (action === 'another') {
            processingQueuePetSelection.value = []
            queueTicketStore.setToBeCreatedTickets(data as unknown as ToBeCreatedTicketType)
            if (!props.customerInfo) {
              event_bus.emit('confrimTicket', resolve, true)
              closeModal()
            } 
          } else if (action === 'reset') {
            processingQueuePetSelection.value = []
            queueTicketStore.clearToBeCreatedTickets()
            if(!props.customerInfo) {
              closeModal()
            }
            resolve(true)
          } else {
            processingQueuePetSelection.value = data as OnProcessQueuePetSelection
            resolve(true)
          }
        })
      }
    }, true)
}

const removePetFromProcessingQueue = (pet) => {
  const petIdx = processingQueuePetSelection.value.findIndex((queuePet) => queuePet.id_pet.toString() == pet.id_pet.toString())
  if(petIdx !== -1) {
    processingQueuePetSelection.value.splice(petIdx, 1)
  }
}

const openSelectPetModal = () => {
  popupFunction(AddPetModal, {
    flgExistingCustomer: true,
    popup: {
      persistent: true
    },
  }, true)
}

const addNewPets = (totalPets: number) => {
  for(let i = 1; i <= totalPets; i++) {
    handleAddNewPet(i === totalPets ? true : false)
  }
}

const onImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = PetPlaceholder
}

onMounted(() => {
  if(props.preselectedPetIds?.length > 0) {
    props.preselectedPetIds.forEach((petId, index) => {
      processingQueuePetSelection.value.push({
        id_pet: Number(petId),
        petData: {
          id_pet: petId.toString(),
        },
        type_purpose_list: [],
        type_doctor_list: [],
        isCurrentPet: index === 0,
        inCurrentStep: 1
      })
    })
    handleOpenSelectPurposeDoctorModal()
  } else if (props.queueTicket) {
    const queueDetail = (props.queueTicket as QueueTicketType & { queue_detail: any }).queue_detail
    Object.keys(queueDetail).forEach((keysAsPetId: string, index: number) => {
      if(!isNaN(keysAsPetId)) {
        handleTogglePet(
          customerPets.value.find(pet => Number(pet.id_pet) === Number(keysAsPetId)) as PetType,
          undefined,
          {
            typePurposeList: queueDetail[keysAsPetId]?.type_purpose_list ?? [],
            typeDoctorList: queueDetail[keysAsPetId]?.type_doctor_list ?? [],
            isCurrentPet: index === 0
          }
        )
      }
    })
  }
  event_bus.on('add-pets', addNewPets)
})

onUnmounted(() => {
  event_bus.off('add-pets', addNewPets)
})

</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
      <q-card-section class="qt-wrapper">
        <div class="flex justify-between items-center info-content">
          <span class="info text-regular normal">
            本日の受診ペットの選択
          </span>
        </div>
        <div class="q-mt-md flex gap-8 q-pt-lg info-selection">
          <q-btn
            v-for="(pet, idx) in customerPets"
            class="content-selection-btn position-relative"
            :class="isPetSelected(pet) ? 'selected' : 'outline-btn'"
            :key="pet.id_pet"
            @click="handleTogglePet(pet)"
            flat
            :ripple="false"
          >
            <div class="pet-avatar">
              <img :src="(pet as any).thumbnail_path1 || (pet as any).thumbnail_path2 ? (pet as any).thumbnail_path1 || (pet as any).thumbnail_path2 : PetPlaceholder" alt="pet-avatar" :placeholder-src="PetPlaceholder"
                @error="onImageError" 
              />
            </div>
            <div class="content-selection-btn-content">
              <span class="text-regular weighted"> {{ getPetName(pet) }} </span>
              <span class="text-medium normal"> {{ customerStore.getPetHonorific(pet) }} </span>
            </div>
            <q-icon 
              name="close" 
              class="remove-pet"
              v-if="!pet.code_pet"
              @click.stop="() => {
                customerPets.splice(idx, 1);
                removePetFromProcessingQueue(pet);
              }"
            />
          </q-btn>
          <q-btn
            class="content-selection-btn"
            v-if="allowNewPetSelection"
            flat
            :ripple="false"
            round
            @click="openSelectPetModal"
          >
            <div class="content-selection-btn-content">
              <span class="text-regular normal pet-add-btn">
                <q-icon name="add" />
              </span>
            </div>
          </q-btn>
        </div>
      </q-card-section>
      <q-card-section class="bg-white q-bt action-btns row">
        <div class="flex justify-between row full-width">
          <q-btn 
            outline 
            class="cancel outline-btn" 
            @click="closeModal"
          >
            <span>キャンセル</span>
          </q-btn>
          <q-btn 
            class="next text-white"
            @click="handleOpenSelectPurposeDoctorModal"
            :disable="disableNextButton"
          >
            <span>次へ</span>
          </q-btn>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<style lang="scss">
.mobile {
  &.platform-ios {
    .content.checkin-feat {
      height: calc(100dvh - 20dvh) !important;
    }
    .medium .content.checkin-feat {
      height: calc(100vh - 250px) !important;
    }
  }
}
@media screen and (max-width: 1100px) {
  .medium .content.checkin-feat {
    height: calc(100vh - 185px) !important;
  }
}
@media screen and (min-width: 1100px) {
  .medium .content.checkin-feat {
    height: calc(100vh - 185px) !important;
  }
}
$checkin-btn-dark-blue: #033C71;
$checkin-regular-text-size: 36px;
$checkin-medium-text-size: 28px;
$checkin-small-text-size: 24px;
.checkin-feat {
  overflow: hidden;
  height: 100%;
  .checkin-feat-wrapper {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .qt-wrapper {
      display: flex;
      flex-direction: column;
      padding-top: 40px;
      padding-left: 0px;
      padding-right: 0px;
      flex-grow: 1;
      overflow: hidden;
      .info-content {
        flex-shrink: 1;
        padding-left: 40px;
        padding-right: 40px;
      }
      .info-selection {
        flex-grow: 1;
        overflow-y: auto;
        padding-left: 40px;
        padding-right: 40px;
        align-content: baseline;
      }
    }
  }
  .outline-btn {
    &:before {
      border-color: $dark-blue;
      .weighted {
        border-width: 3px;
        border-radius: 20px;
      }
    }
    .q-btn__content {
      color: $dark-blue;
    }
  }
  .top-btn {
    font-size: 32px;
    padding: 18px 20px;
    font-weight: 600;
  }
  .content-selection-btn {
    border-radius: 5px;
    padding: 15px 35px;
    background-color: #E2F9FF !important;
    border: none !important;
    box-shadow: 2px 2px 2px 2px #00000029;
    height: 120px !important;
    .q-btn__content {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 15px;
    }
    &.selected {
      background-color: $checkin-btn-dark-blue !important;
      color: #fff;
    }
    .content-selection-btn-content {
      display: flex;
      flex-direction: row;
      justify-content: start;
      align-items: baseline;
      gap: 10px;
      .pet-add-btn {
        color: $checkin-btn-dark-blue;
      }
    }
    .remove-pet {
      position: absolute;
      right: -20px;
      top: -20px;
      z-index: 999;
    }
  }
  .pet-overview-row {
    .pet-selection-item {
      background-color: #FFEFAA;
      border-radius: 8px;
      padding: 8px 16px;
      border: 1px solid #00000029;
    }
  }
  .header {
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 24px;
    padding-bottom: 24px;
    align-items: center;
  }
  .header,
  .info,
  .pet-overview-row,
  .content-selection-btn {
    &.text-regular,
    .text-regular {
      font-size: $checkin-regular-text-size;
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    &.text-medium,
    .text-medium {
      font-size: $checkin-medium-text-size;
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    &.text-small,
    .text-small {
      font-size: $checkin-small-text-size;
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    .pet-avatar {
      width: 70px;
      height: 70px;
      background-color: #E2F9FF;
      border-radius: 50%;
      border: 4px solid #FFFFFF;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
      &:has(.no-border) {
        border: none !important;
      }
    }
  }
  .action-btns {
    padding-left: 40px;
    padding-right: 40px;
    padding-bottom: 24px;
    padding-top: 24px;
    .q-btn {
      width: 40%;
      max-height: 90px;
      font-size: 20px;
      border-radius: 20px;
      padding: 28px;
    }
    .cancel {
      background: #FFF !important;
    }
    .next {
      background: $checkin-btn-dark-blue;
    }
  }
}
</style>