<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import useCliCommonStore from '@/stores/cli-common'
import { storeToRefs } from 'pinia'
import { setPageTitle } from '@/utils/pageTitleHelper'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

const cliCommonStore = useCliCommonStore()
const clinicStore = useClinicStore()
const { getCliCommonPillDivisionList } = storeToRefs(cliCommonStore)
const pagination = ref({ rowsPerPage: 0 })

const columns = [
  {
    name: ' ',
    label: ' ',
    field: ' ',
    align: ' '
  },
  {
    name: 'name_cli_common ',
    label: '分割オプション ',
    field: 'name_cli_common',
    align: 'left'
  },
  {
    name: 'disable',
    label: '表示設定',
    field: 'flg_func1',
    align: 'left'
  }
]

const search = ref({
  code_cli_common: 24
})

const cliCommonOptionList: any = ref([])

const onRowClick = async (row: any) => {
  init()
}

const init = async () => {
  cliCommonOptionList.value = []
  const response = await cliCommonStore.fetchPreparationCliCommonList(
    {
      code_cli_common: [24],
      id_clinic: [search.value.id_clinic]
    },
    true
  )
  if (response) {
    cliCommonOptionList.value = getCliCommonPillDivisionList.value
  }
}

onMounted(async () => {
  await clinicStore.fetchClinics()
  await init()

  setPageTitle('錠剤分割オプション')
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          錠剤分割オプション
        </q-toolbar-title>
      </q-toolbar>
    </MtHeader>
    <div class="row items-center justify-between tableBox text-grey-700 q-px-lg q-mb-sm">
      <div class="body1 regular">
        表示：<span class="q-ml-sm"> {{ cliCommonOptionList.length }} 件</span>
      </div>
      <div>
        <q-icon name="error_outline" />
        処方箋（錠剤の医薬品）オーダーで表示する錠剤分割オプションを設定
      </div>
    </div>
    <q-table
      v-model:pagination="pagination"
      :columns="columns"
      :rows="cliCommonOptionList"
      :rows-per-page-options="[0]"
      :rowsBg="true"
      :style="{ height: 'calc(100dvh - 90px)' }"
      flat
      hide-bottom
    >
      <template v-slot:header="props">
        <q-tr :props="props" class="sticky-header">
          <q-th v-for="col in props.cols" :key="col.name" :props="props">
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props" class="cursor-pointer">
          <q-td
            v-for="(col, index) in props.cols"
            :style="col.style"
            @click="onRowClick(props.row)"
          >
            <div v-if="col.field === 'flg_func1'" class="body1 regular text-grey-900">
              <q-toggle
                v-model="props.row[col.field]"
                :label="props.row[col.field] ? '表示する' : '表示しない'"
                color="blue"
                @update:model-value="async ()=>{
                  await mtUtils.callApi(
                      selectOptions.reqMethod.PUT,
                      `mst/clinic_common/${props.row.id_cli_common}`,
                      {...props.row}
                    )
                }"
              />
            </div>
            <div v-else class="body1 regular text-grey-900">
              {{ props.row[col.field] }}
            </div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-page>
</template>

<style lang="scss" scoped>
.tableBox {
  margin-top: 20px;
}

.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}
</style>
