<script setup lang="ts">
import { computed, onMounted, ref, watch, inject } from 'vue'
import dayjs from '@/boot/dayjs'
import { storeToRefs } from 'pinia'
import useWorkScheduleStore from '@/stores/work-schedules'
import useEmployeeStore from '@/stores/employees'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import { typeBusinessDay } from '@/utils/enum'
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import BulkUpdateShiftModal from './BulkUpdateShiftModal.vue'

// State management
const selectedDate = ref(dayjs().startOf('week'))
const selectedWeekLabel = ref('今週')
const isLoading = ref(false)
const weeklyData = ref<any[]>([])
const clinicName = ref('')
const errorMessage = ref('')
// Data for bulk update
const bulkUpdateData = ref<any[]>([])

// Store instances
const workScheduleStore = useWorkScheduleStore()
const schedulingStore = workScheduleStore
const employeeStore = useEmployeeStore()
const clinicStore = useClinicStore()

// Props
const props = defineProps<{
  clinicId?: number | null
  startDate?: string
  endDate?: string
  editMode?: boolean
  displayMode?: 'slot' | 'day'
  booking_item_id?: number
}>()

// Use provided editMode and displayMode from props
const editMode = ref(false)
const displayMode = ref<'slot' | 'day'>('slot')

const clinicId = computed(() => {
  // First check if props.clinicId is available
  if (props.clinicId) {
    return props.clinicId
  }
  
  // Finally fallback to localStorage
  const storedId = localStorage.getItem('id_clinic')
  return storedId ? JSON.parse(storedId) : null
})

// Watch for clinic ID changes to refetch data
watch(clinicId, async (newClinicId) => {
  if (newClinicId) {
    await fetchClinicName()
    await fetchWeeklySchedule()
  }
})

// Interface definitions
interface EmployeeSchedule {
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  checked: boolean
}

interface EmployeeSchedules {
  [key: string]: {
    [key: number]: EmployeeSchedule
  }
}

interface TimeSlot {
  slot_number: number
  business_time: {
    start: string
    end: string
  }
  checkin_time?: {
    start: string
    end: string
  }
  ticket_issue_time?: {
    start: string
    end: string
  }
  ticket_limit?: number | null
}

interface DayData {
  display_date: string
  date?: string | null
  day_index: number
  day_of_week: number
  type_weekday: number // UI value (11-17, 18 for holiday)
  today: boolean
  business_hour_slot?: {
    id_business_hour_slot?: number
    type_business_day: number
    name_business_hour: string
    display_order?: number
    time_slots?: TimeSlot[]
  }
  employeeSchedules: EmployeeSchedules
  is_off_day: boolean
  slot_name?: string
  slot_type?: number
  employee_schedules?: Array<{
    id_employee: number
    name_display: string
    type_occupation: number
    flg_calendar: boolean
    schedules: Array<{
      id_employee_workschedule: number
      time_workschedule_start: string
      time_workschedule_end: string
      flg_whole_dayoff: boolean
    }>
  }>
  isHoliday?: boolean
}

// Interface for schedule
interface EmployeeScheduleItem {
  id_employee_workschedule: number | null;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  type_weekday?: number;
}

const sortedDoctors = ref<any[]>([])

// Q-Table columns definition for weekly view
const tableColumns = computed(() => {
  const baseColumns = [
    {
      name: 'day',
      label: '',
      field: 'day',
      align: 'center' as const,
      style: 'width: 75px;',
      headerStyle: 'width: 75px; background-color: #e0e0e0;'
    },
    {
      name: 'businessHours',
      label: '営業時間帯',
      field: 'businessHours',
      align: 'left' as const,
      style: 'width: 22.11%;',
      headerStyle: 'width: 22.11%; background-color: #e0e0e0;'
    }
  ]
  
  // Add doctor columns dynamically
  const doctorCols = sortedDoctors.value.map(doctor => ({
    name: `doctor_${doctor.id_employee}`,
    label: doctor.name_display,
    field: `doctor_${doctor.id_employee}`,
    align: 'center' as const,
    style: `width: ${doctorWidth.value}%;`,
    headerStyle: `width: ${doctorWidth.value}%; background-color: #f5f5f5;`
  }))
  
  return [...baseColumns, ...doctorCols]
})

// Transform weekly data for q-table
const tableRows = computed(() => {
  return weeklyData.value.map(day => {
    const row: any = {
      id: `day_${day.day_index}`,
      day: day.display_date,
      businessHours: day,
      dayData: day,
      // Additional properties for styling
      isOffDay: day.is_off_day,
      isToday: day.today,
      isSaturday: isSaturday(day),
      isSunday: isSunday(day),
      isHoliday: isHoliday(day)
    }
    
    // Add doctor data for each column
    sortedDoctors.value.forEach(doctor => {
      row[`doctor_${doctor.id_employee}`] = {
        doctor,
        day,
        scheduleData: day.employee_schedules ? 
          getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee) : null
      }
    })
    
    return row
  })
})

// Q-Table row styling for weekly view
const getRowClass = (row: any) => {
  const classes = []
  if (row.isOffDay) classes.push('off-day-row')
  if (row.isToday) classes.push('today-row')
  if (row.isHoliday) classes.push('holiday-row')
  if (row.isSaturday) classes.push('saturday-row')
  if (row.isSunday) classes.push('sunday-row')
  return classes.join(' ')
}

// Calculate doctor column width
const doctorWidth = computed(() => {
  const dayColumnPercent = 7.5 // ~75px out of ~1000px typical width
  const businessHoursPercent = 22.11
  const totalRemainingWidth = 100 - dayColumnPercent - businessHoursPercent
  return sortedDoctors.value.length ? totalRemainingWidth / sortedDoctors.value.length : 0
})

// Helper function to check if a day is Saturday (type_weekday = 16)
const isSaturday = (day: DayData) => day.type_weekday === 16

// Helper function to check if a day is Sunday (type_weekday = 17)
const isSunday = (day: DayData) => day.type_weekday === 17

// Helper function to check if a day is a Holiday (type_weekday = 18)
const isHoliday = (day: DayData) => day.type_weekday === 18

// Change the selected week
const changeDate = async (prefix: 'next' | 'prev') => {
  if (prefix === 'next') {
    selectedDate.value = selectedDate.value.add(1, 'week')
  } else {
    selectedDate.value = selectedDate.value.subtract(1, 'week')
  }
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
  await fetchWeeklySchedule()
}

// Fetch weekly schedule data
const fetchWeeklySchedule = async () => {
  if (!clinicId.value) return
  
  isLoading.value = true

  try {
    // Fetch scheduling data for the selected week
    const response = await workScheduleStore.fetchSchedulingData({
      clinic_id: clinicId.value,
      period_type: 'weekly',
      start_date: selectedDate.value.format('YYYY-MM-DD'),
      booking_item_id: props.booking_item_id
    })

    // sort by display_order and filter by flg_calendar = true
    sortedDoctors.value = response.employees.filter((item: any) => item.flg_calendar).sort((a: any, b: any) => a.display_order - b.display_order)

    buildWeeklyData()
  } catch (error) {
    console.error('Error fetching weekly schedule:', error)
  } finally {
    isLoading.value = false
  }
}

// Build weekly data structure from scheduling API response
const buildWeeklyData = () => {
  const schedulingData = schedulingStore.getWeeklySchedulingData()
  if (!schedulingData?.length) return

  // Check if there are any employees in the scheduling data
  const hasEmployees = schedulingData.some((day: any) => 
    day.employee_schedules && day.employee_schedules.length > 0
  )

  // if (!hasEmployees) {
  //   errorMessage.value = '予約商品に対応する従業員を選択してください'
  //   weeklyData.value = []
  //   return
  // }

  errorMessage.value = '' // Clear any previous error
  weeklyData.value = schedulingData.map((day: any) => {
    // For holiday (type_weekday: 18), we don't need to calculate a specific date
    const dayDate = day.type_weekday === 18 
      ? null 
      : selectedDate.value.add(day.day_index, 'day')

    // Create day data object
    const dayData: DayData = {
      // For holiday, use a static display name
      display_date: day.type_weekday === 18 ? '祝祭日' : (dayDate ? dayDate.format('ddd') : ''),
      date: dayDate ? dayDate.format('YYYY-MM-DD') : null,
      day_index: day.day_index,
      day_of_week: day.day_index, // 0-6 for Monday-Sunday
      type_weekday: day.type_weekday, // 11-17 for UI display, 18 for holiday
      today: dayDate ? dayjs().isSame(dayDate, 'day') : false,
      business_hour_slot: day.business_hour_slot,
      employeeSchedules: {},
      is_off_day: day.business_hour_slot?.type_business_day === 90,
      slot_name: day.slot_name,
      slot_type: day.slot_type,
      employee_schedules: day.employee_schedules,
      isHoliday: day.type_weekday === 18
    }

    // Map employee availability data
    if (day.employee_schedules) {
      day.employee_schedules.forEach((employeeSchedule: any) => {
        if (!dayData.employeeSchedules[employeeSchedule.id_employee]) {
          dayData.employeeSchedules[employeeSchedule.id_employee] = {}
        }

        // Map each schedule to a slot number
        employeeSchedule.schedules.forEach((schedule: any) => {
          // Figure out which time slot this schedule corresponds to
          const slotNumber = getSlotNumberForTime(schedule.time_workschedule_start, day.business_hour_slot?.time_slots)

          if (slotNumber) {
            dayData.employeeSchedules[employeeSchedule.id_employee][slotNumber] = {
              checked: schedule.flg_whole_dayoff,
              id_employee_workschedule: schedule.id_employee_workschedule || null,
              time_workschedule_start: schedule.time_workschedule_start,
              time_workschedule_end: schedule.time_workschedule_end,
              flg_whole_dayoff: schedule.flg_whole_dayoff
            }
          }
        })
      })
    }

    // Create slots for all doctors and all potential time slots
    sortedDoctors.value.forEach((doctor) => {
      if (!dayData.employeeSchedules[doctor.id_employee]) {
        dayData.employeeSchedules[doctor.id_employee] = {}
      }

      // Ensure all slots exist
      if (day.business_hour_slot?.time_slots) {
        day.business_hour_slot.time_slots.forEach((slot: any, index: number) => {
          const slotNumber = index + 1
          if (!dayData.employeeSchedules[doctor.id_employee][slotNumber]) {
            dayData.employeeSchedules[doctor.id_employee][slotNumber] = {
              checked: false,
              id_employee_workschedule: null,
              flg_whole_dayoff: false
            }
          }
        })
      }
    })

    return dayData
  })
}

// Helper function to match a time to a slot number
const getSlotNumberForTime = (time: string, timeSlots?: TimeSlot[]): number | null => {
  if (!timeSlots) return null

  for (let i = 0; i < timeSlots.length; i++) {
    if (timeSlots[i].business_time.start === time) {
      return i + 1
    }
  }
  return null
}

// Helper function to get business day type name
const typeBusinessDayName = (value: number) => typeBusinessDay.find((v) => v.value === value)

// Get total number of time slots for a day
const getTotalSlots = (timeSlots?: TimeSlot[]) => {
  return timeSlots?.length || 0
}

// Fetch clinic name
const fetchClinicName = async () => {
  if (clinicId.value) {
    try {
      const clinic = await clinicStore.fetchClinicById(clinicId.value)
      clinicName.value = clinic.name_clinic_display
    } catch (error) {
      console.error('Error fetching clinic name:', error)
      clinicName.value = ''
    }
  } else {
    clinicName.value = ''
  }
}

// Format time string to display format (removing :00 seconds)
const formatTimeDisplay = (time: string) => {
  return time ? time.replace(/:00$/, '') : ''
}

// Helper function to get the highest priority schedule
const getHighestPrioritySchedule = (employeeSchedules: any[], employeeId: number): EmployeeScheduleItem | null => {
  if (!employeeSchedules || !employeeSchedules.length) return null;
  
  // Filter schedules for the given employee
  const empSchedules = employeeSchedules.filter(emp => emp.id_employee === employeeId);
  if (!empSchedules.length || !empSchedules[0].schedules || !empSchedules[0].schedules.length) return null;
  
  const schedules = empSchedules[0].schedules;
  
  // Priority: Regular schedules (type_weekday=11-18)
  const regularSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday && s.type_weekday >= 11 && s.type_weekday <= 18);
  if (regularSchedules.length) {
    return {
      ...regularSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule || null
    };
  }
  
  // If no matching schedules, return the first one
  return {
    ...schedules[0],
    id_employee_workschedule: empSchedules[0].id_employee_workschedule || null
  };
};

// Function to display multiple time slots
const formatMultipleTimeSlots = (schedule: EmployeeScheduleItem | null): string => {
  if (!schedule) return '';
  
  // If it's a day off, return the "休" character
  if (schedule.flg_whole_dayoff) {
    return '<span class="text-darkred">休</span>';
  }
  
  let result = '';
  
  // Format primary time slot
  if (schedule.time_workschedule_start && schedule.time_workschedule_end) {
    result += `${formatTimeDisplay(schedule.time_workschedule_start)}~${formatTimeDisplay(schedule.time_workschedule_end)}`;
  }
  
  // Add secondary time slot if exists
  if (schedule.time_workschedule_start2 && schedule.time_workschedule_end2) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start2)}~${formatTimeDisplay(schedule.time_workschedule_end2)}`;
  }
  
  // Add tertiary time slot if exists
  if (schedule.time_workschedule_start3 && schedule.time_workschedule_end3) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start3)}~${formatTimeDisplay(schedule.time_workschedule_end3)}`;
  }
  
  return result;
}

// Helper function to prepare the bulk update data
const prepareBulkUpdateData = async (): Promise<any[]> => {
  // Prepare payload for scheduling API - directly filter to only include data with changes or existing IDs
  const payload = weeklyData.value.flatMap((day) => {
    return Object.entries(day.employeeSchedules).flatMap(([employeeId, slots]) => {
      return Object.entries(slots as Record<string, EmployeeSchedule>)
        .filter(([_, schedule]) => {
          // Only include records that have changes or existing IDs
          return schedule.id_employee_workschedule || schedule.checked
        })
        .map(([slotNumber, schedule]) => {
          // Find the corresponding time slot
          const timeSlot = day.business_hour_slot?.time_slots?.[parseInt(slotNumber) - 1]

          // Ensure type_weekday is between 11-18 (Mon-Sun, Holiday)
          const typeWeekday = day.type_weekday >= 11 && day.type_weekday <= 18 
            ? day.type_weekday 
            : 11; // Default to Monday if invalid

          // Keep track of existing schedule IDs for potential deletion
          const id_employee_workschedule = schedule.id_employee_workschedule || null;

          return {
            id_employee_workschedule: id_employee_workschedule,
            id_employee: parseInt(employeeId as string),
            type_weekday: typeWeekday, 
            time_workschedule_start: timeSlot?.business_time.start || '00:00:00',
            time_workschedule_end: timeSlot?.business_time.end || '00:00:00',
            flg_whole_dayoff: schedule.checked,
            id_clinic: clinicId.value,
            id_booking_item: props.booking_item_id
          }
        })
    })
  }).filter(item => item !== null && item !== undefined);

  return payload;
}

// Direct bulk update for day mode
const directBulkUpdate = async () => {
  try {
    isLoading.value = true

    // Collect selected employees and days with checked schedules
    const selectedEmployees = new Set<number>()
    const selectedData: any[] = []
    
    weeklyData.value.forEach((day) => {
      if (day.is_off_day) return // Skip off days

      Object.entries(day.employeeSchedules).forEach(([employeeId, slots]) => {
        const hasCheckedSlots = Object.values(slots as Record<string, EmployeeSchedule>)
          .some(schedule => schedule.checked === true)
        
        if (hasCheckedSlots) {
          selectedEmployees.add(parseInt(employeeId as string))
          selectedData.push({
            id_employee: parseInt(employeeId as string),
            type_weekday: day.type_weekday, // Use the actual weekday type (11-18)
            date_booking_special: null // No special date for weekly view
          })
        }
      })
    })

    if (selectedData.length === 0) {
      mtUtils.alert('更新する予定がありません')
      return
    }

    // Remove duplicates based on employee and weekday
    const uniqueData = selectedData.filter((item, index, self) => 
      index === self.findIndex(t => 
        t.id_employee === item.id_employee && t.type_weekday === item.type_weekday
      )
    )

    // Prepare employee work schedules for API
    const employeeList = Array.from(selectedEmployees).map((employeeId) => {
      // Filter uniqueData for current employee
      const employeeData = uniqueData.filter((item: any) => item.id_employee === employeeId)
      
      const employeeWorkscheduleList = employeeData.map((data: any) => ({
        type_weekday: data.type_weekday,
        time_workschedule_start: '00:00:00',
        time_workschedule_end: '00:00:00',
        flg_whole_dayoff: true, // Always true for day mode
        date_booking_special: null,
        min_rest: 0,
        id_booking_item: props.booking_item_id // Add booking item ID
      }))

      return {
        id_employee: employeeId,
        employee_workschedule_list: employeeWorkscheduleList
      }
    })

    // Call the API
    if (clinicId.value === null) {
      throw new Error('Clinic ID is required');
    }
    
    await workScheduleStore.createOrUpdateWorkSchedules({
      id_clinic: clinicId.value,
      employee_list: employeeList
    })

    // Show success message
    mtUtils.autoCloseAlert('休みを適用しました！')
    
    // Refresh data and exit edit mode
    await fetchWeeklySchedule()
    editMode.value = false
  } catch (error) {
    console.error('Failed to update schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの更新に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Open the bulk update modal
const openBulkUpdateModal = async () => {
  if (!bulkUpdateData.value.length) {
    mtUtils.alert('更新する予定がありません')
    return
  }

  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: bulkUpdateData.value,
    clinicId: clinicId.value,
    displayMode: displayMode.value,
    booking_item_id: props.booking_item_id, // Add booking item ID
    onSuccess: async () => {
      // Refresh data
      await fetchWeeklySchedule()
      editMode.value = false
    }
  })
}

// Check if all slots for an employee across all days in the current week are marked as off
const isAllDaysSlotChecked = (employeeId: number | string) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => !day.is_off_day)
  if (!days.length) return false
  
  return days.every(day => {
    const employeeSchedules = day.employeeSchedules[employeeId]
    if (!employeeSchedules) return false
    
    const values = Object.values(employeeSchedules) as EmployeeSchedule[]
    return values.length > 0 && values.every(item => item.checked === true)
  })
}

// Update all slots for an employee across all days in the current week
const updateAllDaysSlotChecked = (employeeId: number | string, value: boolean) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => !day.is_off_day)
  days.forEach(day => {
    updateAllSlotChecked(day, employeeId, value)
  })
}

// Add click handler for schedule items
const handleScheduleClick = async (schedule: EmployeeScheduleItem | null, employeeId: number, day: DayData) => {
  if (!schedule) return;
  
  // Open the bulk update modal with the schedule data
  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: [{
      id_employee_workschedule: schedule.id_employee_workschedule,
      id_employee: employeeId,
      type_weekday: day.type_weekday,
      time_workschedule_start: schedule.time_workschedule_start,
      time_workschedule_end: schedule.time_workschedule_end,
      flg_whole_dayoff: schedule.flg_whole_dayoff
    }],
    clinicId: clinicId.value,
    booking_item_id: props.booking_item_id,
    workScheduleId: schedule.id_employee_workschedule || undefined,
    onSuccess: async () => {
      // Refresh data
      await fetchWeeklySchedule()
    }
  })
}

// Check if all slots for an employee on a specific day are marked as off
const isAllSlotChecked = (data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return false

  const values = Object.values(employeeSchedules)
  return values.length > 0 && values.every((item) => item.checked === true)
}

// Set all slots for an employee on a specific day to checked or unchecked
const updateAllSlotChecked = (day: DayData, employeeId: number | string, value: boolean) => {
  const employeeSchedules = day.employeeSchedules[employeeId]
  if (!employeeSchedules) return

  Object.values(employeeSchedules).forEach((item) => {
    item.checked = value
  })
}

// Toggle edit mode functions
const toggleEditMode = (mode?: 'day' | 'slot') => {
  editMode.value = !editMode.value
  if (mode) {
    displayMode.value = mode
  }
}

// Cancel edit mode
const cancelEdit = () => {
  editMode.value = false
  // Reset data in child components by calling the refreshData event
  fetchWeeklySchedule()
}

// Initialize on component mount
onMounted(async () => {
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
    
  // Only fetch data if clinicId is available
  if (clinicId.value) {
    await fetchWeeklySchedule()
  }
})

// Bulk update function
const bulkUpdate = async () => {
  // If in day mode, directly update without showing modal
  if (displayMode.value === 'day') {
    await directBulkUpdate()
    return
  }

  const payload = await prepareBulkUpdateData();
  // Store payload in the ref for use by the modal
  bulkUpdateData.value = payload

  // Show modal instead of making API call directly
  await openBulkUpdateModal()
}

// Expose methods for parent component
defineExpose({
  bulkUpdate,
  fetchWeeklySchedule,
  prepareBulkUpdateData
})

// manage multiple slot time for time_workschedule_start and time_workschedule_end, time_workschedule_start2 and time_workschedule_end2, time_workschedule_start3 and time_workschedule_end3  
const manageMultipleSlotTime = (schedule: EmployeeScheduleItem | null) => {
  if (!schedule) return ''
  
  // If it's a day off, return the "休" character
  if (schedule.flg_whole_dayoff) {
    return '<span class="text-darkred">休</span>'
  }
  
  let result = ''
  
  // Format primary time slot
  if (schedule.time_workschedule_start && schedule.time_workschedule_end) {
    result += `${formatTimeDisplay(schedule.time_workschedule_start)}~${formatTimeDisplay(schedule.time_workschedule_end)}`
  }
  
  // Add secondary time slot if exists
  if (schedule.time_workschedule_start2 && schedule.time_workschedule_end2) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start2)}~${formatTimeDisplay(schedule.time_workschedule_end2)}`
  }
  
  // Add tertiary time slot if exists
  if (schedule.time_workschedule_start3 && schedule.time_workschedule_end3) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start3)}~${formatTimeDisplay(schedule.time_workschedule_end3)}`
  }
  
  return result
}
</script>

<template>
  <div class="q-pl-xl q-pr-sm">
    <!-- Add the edit mode toggle buttons -->
    <div class="row items-center justify-between q-my-md">
      <div class="row items-center">
        <div v-if="!editMode" class="flex items-center">
          <q-btn
            label="時間枠毎設定"
            @click="toggleEditMode('slot')"
            padding="4px 20px"
            flat
            unelevated
            class="bg-grey-100 q-mr-md"
            style="border: 1px solid #9e9e9e"
          />
          <q-btn
            label="終日休みの一括設定"
            @click="toggleEditMode('day')"
            padding="4px 20px"
            flat
            unelevated
            class="bg-grey-100"
            style="border: 1px solid #9e9e9e"
          />
        </div>
        <div v-else class="flex items-center">
          <div class="caption2">
            {{
              displayMode === 'day'
                ? '終日休みを設定する日にチェックを入れてください'
                : '休みを設定する時間枠にチェックを入れてください'
            }}
          </div>
          <q-btn unelevated color="primary" class="q-ml-md" type="button" @click="bulkUpdate">
            <span>決定</span>
          </q-btn>
          <q-btn outline class="bg-grey-100 text-grey-800 q-ml-sm" @click="cancelEdit">
            <span>キャンセル</span>
          </q-btn>
        </div>
      </div>
    </div>

    <div class="row items-center justify-between q-my-md">
      <div class="col-auto">
        <!-- <div class="row items-center">
          <q-btn flat dense icon="chevron_left" @click="changeDate('prev')" />
          <div class="q-mx-md">{{ selectedWeekLabel }}</div>
          <q-btn flat dense icon="chevron_right" @click="changeDate('next')" />
        </div> -->
      </div>
      <div class="col-auto">
        <div class="text-subtitle1">{{ clinicName }}</div>
      </div>
    </div>

    <!-- Weekly schedule view using q-table -->
    <div class="calendar-view" v-if="!isLoading && weeklyData.length">
      <q-table
        :columns="tableColumns"
        :rows="tableRows"
        row-key="id"
        flat
        bordered
        separator="cell"
        :rows-per-page-options="[0]"
        hide-bottom
        :row-class="getRowClass"
        class="weekly-schedule-qtable"
      >
        <!-- Custom header slot -->
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th
              v-for="col in props.cols"
              :key="col.name"
              :props="props"
              :style="col.headerStyle"
              class="text-center"
            >
              <template v-if="col.name.startsWith('doctor_')">
                <div class="doctor-header-cell">
                  <MtFormCheckBox
                    v-if="editMode"
                    type="checkbox"
                    label=""
                    :checked="isAllDaysSlotChecked(col.name.replace('doctor_', ''))"
                    class="caption1 q-mt-xs"
                    style="padding: 0; border: none"
                    @update:checked="(newVal) => updateAllDaysSlotChecked(col.name.replace('doctor_', ''), newVal)"
                  />
                  <div class="doctor-name">{{ col.label }}</div>
                </div>
              </template>
              <template v-else>
                {{ col.label }}
              </template>
            </q-th>
          </q-tr>
        </template>

        <!-- Custom body slot for complete control -->
        <template v-slot:body="props">
          <q-tr :props="props" :class="getRowClass(props.row)">
            <!-- Day column -->
            <q-td key="day" :props="props" class="day-cell text-center">
              <div 
                :class="{ 
                  'text-blue': props.row.isSaturday,
                  'text-red': props.row.isSunday || props.row.isHoliday,
                  'holiday-text': props.row.isHoliday
                }"
              >
                {{ props.row.day }}
              </div>
            </q-td>

            <!-- Business hours column -->
            <q-td key="businessHours" :props="props" class="business-hours-cell">
              <div 
                :class="{ 
                  'bg-grey-300': props.row.isOffDay
                }"
                class="q-pa-sm"
              >
                <div class="text-caption q-mb-sm">
                  {{ props.row.dayData.slot_name || typeBusinessDayName(props.row.dayData.business_hour_slot?.type_business_day || 0)?.label }} /
                  {{ props.row.dayData.business_hour_slot?.name_business_hour }}
                </div>
                <template v-if="props.row.dayData.business_hour_slot?.time_slots">
                  <div class="flex flex-wrap gap-2 text-caption">
                    <div 
                      v-for="(timeSlot, slotIdx) in props.row.dayData.business_hour_slot.time_slots" 
                      :key="slotIdx"
                      class="flex items-center gap-2"
                    >
                      枠{{ slotIdx + 1 }}
                      {{ formatTimeDisplay(timeSlot.business_time.start) }} ~
                      {{ formatTimeDisplay(timeSlot.business_time.end) }}
                    </div>
                  </div>
                </template>
              </div>
            </q-td>

            <!-- Doctor schedule columns -->
            <q-td 
              v-for="doctor in sortedDoctors"
              :key="`doctor_${doctor.id_employee}`" 
              :props="props"
              class="doctor-schedule-cell text-center"
            >
              <div 
                class="schedule-content q-pa-sm"
                :class="{ 
                  'bg-grey-300': props.row.isOffDay
                }"
              >
                <template v-if="!props.row.isOffDay">
                  <template v-if="!editMode">
                    <!-- Show only the highest priority schedule for this employee -->
                    <template v-if="props.row.dayData.employee_schedules">
                      <div 
                        v-if="!getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)" 
                        class="text-grey-700 schedule-item"
                      >
                        <!-- 予定なし -->
                      </div>
                      <div 
                        v-else
                        :class="{ 'text-red': getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                        class="schedule-item cursor-pointer"
                        @click="handleScheduleClick(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee) || {
                          id_employee_workschedule: null,
                          time_workschedule_start: '',
                          time_workschedule_end: '',
                          flg_whole_dayoff: false,
                          type_weekday: props.row.dayData.type_weekday
                        }, doctor.id_employee, props.row.dayData)"
                      >
                        <template v-if="getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                          休
                        </template>
                        <template v-else>
                          <div v-html="manageMultipleSlotTime(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee))"></div>
                        </template>
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    <div 
                      class="flex flex-col justify-center items-center cursor-pointer"
                      @click="updateAllSlotChecked(props.row.dayData, doctor.id_employee, !isAllSlotChecked(props.row.dayData, doctor.id_employee))"
                    >
                      <div class="flex items-center">
                        <MtFormCheckBox
                          type="checkbox"
                          label=""
                          :checked="isAllSlotChecked(props.row.dayData, doctor.id_employee)"
                          class="caption1 q-pa-none"
                          style="padding: 0; border: none"
                          @update:checked="(newVal) => updateAllSlotChecked(props.row.dayData, doctor.id_employee, newVal)"
                        />
                      </div>
                      <!-- Display time information alongside checkbox -->
                      <template v-if="props.row.dayData.employee_schedules">
                        <template v-if="!getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)">
                          <!-- No schedule info -->
                        </template>
                        <template v-else>
                          <div
                            :class="{ 'text-red': getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                            class="schedule-item"
                          >
                            <template v-if="getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                              休
                            </template>
                            <template v-else>
                              <div v-html="manageMultipleSlotTime(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee))"></div>
                            </template>
                          </div>
                        </template>
                      </template>
                    </div>
                  </template>
                </template>
                <template v-else>
                  <div class="text-red">休</div>
                </template>
              </div>
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>

    <!-- Loading or no data states -->
    <div v-else-if="isLoading" class="flex justify-center items-center" style="height: 300px">
      <q-spinner size="40px" color="primary" />
    </div>
    <div v-else class="flex justify-center items-center" style="height: 300px">
      <div class="text-grey-700">{{ errorMessage || 'データがありません' }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.doctor-wrapper {
  display: flex;
  flex-direction: row;
  width: calc(100% - 75px - 22.11%);
}

.doctor-wrapper .doc-name {
  flex: 1;
  text-align: center;
}

.availability {
  flex: 1;
  text-align: center;
}

.business-hours-column {
  display: flex;
  flex-direction: column;
}

.calendar-view {
  .weekly-schedule-qtable {
    .q-table__top,
    .q-table__bottom {
      display: none;
    }

    .q-table {
      border-collapse: collapse;
    }

    // Header styling
    thead tr th {
      background-color: $grey-300;
      height: 32px;
      padding: 4px 8px;
      border: 1px solid $grey-400;
      font-size: 12px;
      position: sticky;
      top: 0;
      z-index: 10;

      &:first-child {
        width: 75px;
        background-color: $grey-300;
      }

      &:nth-child(2) {
        width: 22.11%;
        background-color: $grey-300;
      }
    }

    .doctor-header-cell {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      
      .doctor-name {
        font-size: 12px;
        line-height: 1.2;
      }
    }

    // Body cell styling
    tbody tr {
      &.off-day-row {
        background-color: $grey-400;
      }

      &.holiday-row {
        .day-cell {
          color: $darkRed !important;
          font-weight: bold;
          background-color: rgba(244, 67, 54, 0.1) !important;
        }
      }

      &.saturday-row .day-cell {
        color: $blue !important;
      }

      &.sunday-row .day-cell {
        color: $darkRed !important;
      }

      td {
        height: 3.6rem; // h-95 equivalent
        vertical-align: top;
        border: 1px solid $grey-400;
        padding: 0;

        &.day-cell {
          width: 75px;
          text-align: center;
          font-size: 12px;
          
          > div {
            height: 3.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            
            &.holiday-text {
              color: $darkRed !important;
              font-weight: bold;
              background-color: rgba(244, 67, 54, 0.1) !important;
            }
          }
        }

        &.business-hours-cell {
          width: 22.11%;
          
          > div {
            height: 3.6rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
          }
        }

        &.doctor-schedule-cell {
          text-align: center;
          
          .schedule-content {
            height: 3.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: $grey-100;
          }

          .schedule-item {
            padding: 2px;
            font-size: 12px;
            min-height: 2rem; // h-30 equivalent
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  // Utility classes for backward compatibility
  .h-40 {
    height: 32px;
  }
  .h-95 {
    height: 3.6rem;
  }
  .h-30 {
    height: 2rem;
  }
  .h-45 {
    height: 3rem;
  }
  .w-75 {
    width: 75px;
  }
  .p-5 {
    padding: 5px;
  }
  .bg-yellow-100 {
    background-color: rgba(255, 235, 59, 0.3) !important;
  }
  .bg-grey-300 {
    background-color: $grey-300 !important;
  }
  .text-blue {
    color: $blue !important;
  }
  .text-red {
    color: $darkRed !important;
  }
  .truncated {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    width: 250px !important;
    word-wrap: break-word;
    white-space: normal !important;
    text-align: left;
    @media only screen and (min-width: 1500px) {
      width: 130px !important;
    }
  }
  .holiday {
    color: $darkRed !important;
    font-weight: bold;
    background-color: rgba(244, 67, 54, 0.1) !important;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style> 