<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import useQueueTicketStore from '@/stores/queue_ticket'
import useCliCommonStore from '@/stores/cli-common'
import { storeToRefs } from 'pinia'
import { orderBy, groupBy, flatten } from 'lodash'

import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import {
  CliCommon,
  RoomType
} from '@/types/types'
import { useQueueTicketUtils } from './queueTicketUtils'

const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  REFRESH_INTERVAL,
  CLEAR_TICKET_CALLING_SECONDS,
  QUEUE_TICKET_TO_CALL_SECONDS,
  QUEUE_STATUS,
  clearTimeoutAndIntervals,
  qtToBeCalledPromise,
  getRoomName,
  timeoutId,
  intervalId,
  fetchRooms
} = useQueueTicketUtils()

const queueTicketStore = useQueueTicketStore()
const cliCommonStore = useCliCommonStore()

const currentSlide = ref(0)

const PURPOSES_PER_SCREEN = window.innerWidth > 1024 ? 3 : 2

const { getQueueTicketLists } = storeToRefs(queueTicketStore)
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)

const queueTickets = computed(() => {
  return orderBy(
    getQueueTicketLists.value.filter((ticket) => {
      return (
        !ticket.flg_auto_absent_updated
      )
    }),
    'datetime_service_start'
  )
})

const absentTickets = computed(() => {
  return getQueueTicketLists.value.filter((ticket) =>  ticket.type_status_queue_ticket === QUEUE_STATUS.ABSENT)
})

const waitingQueueTickets = computed(() => {
  return orderBy(
    getQueueTicketLists.value.filter(ticket => 
      ticket.type_status_queue_ticket === QUEUE_STATUS.WAITING ||
      ticket.type_status_queue_ticket === QUEUE_STATUS.IN_PROGRESS
    ),
    'datetime_service_start'
  )
})

const totalSlides = computed(() => {
  return Math.ceil(getCliCommonQTVisitPurposeList.value.length / PURPOSES_PER_SCREEN) || 0
})

const getQueueTickets = (purpose: CliCommon) => {
  const matchedTickets =  queueTickets.value.filter((ticket) => ticket.type_status_queue_ticket === parseInt(purpose.code_func1) && ticket.process_order === 1)
  if(matchedTickets.length === 0) return '-'
  const matchedTicketsIds = matchedTickets.map((ticket) => ticket.number_queue_ticket)
  return matchedTicketsIds.slice(0, 3).join(', ')
}

const refreshData = async () => {
  currentSlide.value = (currentSlide.value + 1) % totalSlides.value
  await queueTicketStore.fetchQueueTicketList({
    today: true
  })

  let qtCallingIndex = getQueueTicketLists.value.findIndex((queueTicket) => queueTicket.queue_detail?.flg_qt_calling)
  if(qtCallingIndex !== -1) {

    isQtToBeCalled.value = true
    await qtToBeCalledPromise()

    isQtCalling.value = true
    callingQt.value = getQueueTicketLists.value[qtCallingIndex]
    
    clearTimeoutAndIntervals()

    timeoutId.value = setTimeout(async () => {
      let payload = {
        id_queue_ticket: callingQt.value.id_queue_ticket,
        flg_qt_calling: false
      }
      await mtUtils.callApi(selectOptions.reqMethod.POST, 'queue_ticket_calling/', payload)
      await queueTicketStore.fetchQueueTicketList({ today: true })

      isQtCalling.value = false
      intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
    }, CLEAR_TICKET_CALLING_SECONDS)
  }
}

onMounted(async () => {
  await queueTicketStore.fetchQueueTicketList({
    today: true
  })

  await Promise.all([
    cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [4] }, true),
    fetchRooms()
  ])
  
  intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
})

onUnmounted(() => {
  clearTimeoutAndIntervals()
})
</script>

<template>
 <transition name="fade">
    <div class="flex justify-center items-center ticket-to-call" v-if="isQtToBeCalled">
      <img src="@/assets/img/queueticketdetail/queue-ticket-to-call.png" />
    </div>
  </transition>
  <transition name="fade">
    <div class="qtcalling-screen-container q-pa-md" v-if="isQtCalling">
      <div class="text-center calling-text text-weight-bold">お呼び出し中</div>
      <div class="text-center ticket-number text-weight-bold">{{ callingQt.number_queue_ticket}}</div>
      <div class="text-center room-number text-weight-bold" v-if="callingQt.queue_detail?.id_room">
        <span class="room-text">ROOM</span> 
        {{ getRoomName(callingQt.queue_detail?.id_room) }}
      </div>
    </div>
  </transition>
  <transition name="fade">
  <div class="waiting-screen-container q-pa-md" v-if="!isQtToBeCalled && !isQtCalling">
    <div class="flex doctor-row">
      <template v-for="(slide, index) in totalSlides" :key="index">
        <div
          v-if="currentSlide === index"
          v-for="(purpose, idx) in getCliCommonQTVisitPurposeList.slice(index * PURPOSES_PER_SCREEN, (index * PURPOSES_PER_SCREEN) + PURPOSES_PER_SCREEN)"
          class="doctor flex-1"
          :key="purpose.id_cli_common"
        >
          <div class="head flex items-center text-white q-pa-xs">
            <span
              class="bg-accent-700 q-py-xs q-px-md q-ml-sm index text-weight-medium"
              >{{ (index * PURPOSES_PER_SCREEN) + (idx + 1) }}</span
            >
            <div class="text-center text-weight-medium purpose flex-1 ellipsis">
              {{ purpose.name_cli_common }}
            </div>
          </div>
          <div class="body q-py-md">
            <div class="name text-accent-800 text-center body-text text-weight-medium">
              呼出番号
            </div>
            <div class="text-weight-bold number text-center">
              {{ getQueueTickets(purpose) }}
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="q-mt-md waiting-list q-pa-md">
      <div class="flex justify-between">
        <div class="text-weight-bold heading">待合中</div>
        <div class="text-grey-700 instruction-text">
          診療内容によって診察の順番は前後する場合がございます。予めご了承ください。
        </div>
      </div>
      <div class="q-mt-md flex" style="gap: 10px">
        <q-btn
          v-for="(waitingTicket, idx) in waitingQueueTickets.slice(0, 10)"
          :key="idx"
          :label="waitingTicket.number_queue_ticket"
          class="waiting-btn text-white text-weight-bold"
        />
      </div>
    </div>
    <div class="q-mt-md absence-list q-pa-md bg-grey-200">
      <div class="flex justify-between">
        <div class="text-weight-bold heading">ご不在</div>
        <div class="text-grey-700 instruction-text">受付にお声掛けください。</div>
      </div>
      <div class="q-mt-md flex" style="gap: 10px">
        <q-btn
          v-for="(absent, idx) in absentTickets.slice(0, 10)"
          :label="absent.number_queue_ticket"
          class="absence-btn bg-white text-grey-700 text-weight-bold"
          :key="idx"
        />
      </div>
    </div>
  </div>
  </transition>
</template>
<style src="./WaitingScreens.scss" lang="scss" scoped></style>
<style lang="scss" scoped>
.waiting-screen-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .doctor-row {
    gap: 10px;
    flex: 4.5;
    .doctor {
      border: 1px solid #e79725;
      border-radius: 5px;
      overflow: hidden;
      .head {
        background: #e79725;
        .index {
          font-size: clamp(2vh, 3vh, 3vh);
          border-radius: 5px;
        }
        .purpose {
          font-size: clamp(5vh, 6vh, 8vh);
          line-height: 1;
        }
      }
      .body {
        .body-text {
          font-size: clamp(2vh, 4vh, 4vh);
        }
        .number {
          line-height: 1;
          font-size: clamp(10vh, 20vh, 25vh);
        }
      }
    }
  }
  .waiting-list {
    flex: 2.5;
    overflow: hidden;
    flex-shrink: 0;
    background: #faebd6;
    border-radius: 10px;
    .heading {
      color: #e79725;
      font-size: clamp(4vh, 3vw, 8vh);
      line-height: 1;
    }
    .instruction-text {
      font-size: clamp(2vh, 3vh, 4vh);
      margin: 0;
    }
    .waiting-btn {
      background: #e79725;
      min-width: 8vw;
      height: clamp(6vh, 10vh, 12vh);
      :deep(.q-btn__content) {
        margin-top: -5px;
        font-size: clamp(2.2vh, 4vw, 8vh);
      }
    }
  }
  .absence-list {
    flex: 2.5;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
    .heading {
      font-size: clamp(4vh, 3vw, 8vh);
      line-height: 1;
    }
    .instruction-text {
      font-size: clamp(2vh, 3vh, 4vh);
      margin: 0;
    }
    .absence-btn {
      min-width: 8vw;
      height: clamp(6vh, 10vh, 12vh);
      :deep(.q-btn__content) {
        margin-top: -5px;
        font-size: clamp(2.2vh, 5vw, 8vh);
      }
    }
  }
  .flex-1 {
    flex: 1;
  }
}

</style>
