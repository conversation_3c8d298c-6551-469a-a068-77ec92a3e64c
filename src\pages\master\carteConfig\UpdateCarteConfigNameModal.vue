<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue'
import { ref } from 'vue'

const props = defineProps({
  groupName: {
    type: String,
    required: true
  },
  callBack: {
    type: Function,
    required: false
  }
})
const emit = defineEmits(['close', 'updateGroupName'])
const groupName = ref(props.groupName)
const updateGroupName = () => {
  props.callBack(groupName.value)
  emit('close')
}
</script>
<template>
  <q-card>
    <q-form @submit="updateGroupName">
      <MtModalHeader @closeModal="emit('close')">
        <q-toolbar-title class="row no-wrap items-center text-grey-900 title2 bold">
          グループ名を変更
        </q-toolbar-title>
      </MtModalHeader>
      <q-card-section>
        <q-input v-model="groupName" label="グループ名" />
      </q-card-section>
      <q-card-section class="q-bt q-pt-xs bg-white">
        <div class="row">
          <div class="col-9">
            <div class="text-right modal-btn">
              <q-btn
                class="q-ml-md"
                color="primary"
                type="submit"
                unelevated
                >
                <span>保存</span>
              </q-btn>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-form>
  </q-card>
</template>