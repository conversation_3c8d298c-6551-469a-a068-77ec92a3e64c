<script setup lang="ts">
import { computed, onMounted, ref, watch, inject } from 'vue'
import dayjs from '@/boot/dayjs'
import { storeToRefs } from 'pinia'
import useBookingItemStore from '@/stores/booking-items'
import useWorkScheduleStore from '@/stores/work-schedules'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import { typeBusinessDay } from '@/utils/enum'
import { flatten } from 'lodash'
// Import MtFormCheckBox correctly by ignoring TypeScript errors for now
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
// Import components for the modal
// @ts-ignore - Import with ignoring TypeScript errors
import OptionModal from '@/components/OptionModal.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import BulkUpdateShiftModal from './BulkUpdateShiftModal.vue'

// State management
const selectedDate = ref(dayjs().startOf('week'))
const selectedWeekLabel = ref('今週')
const isLoading = ref(false)
const weeklyData = ref<any[]>([])
const clinicName = ref('')
// Data for bulk update
const bulkUpdateData = ref<any[]>([])
const employees = ref<Employee[]>([])

// Store instances
const bookingItemStore = useBookingItemStore()
const workScheduleStore = useWorkScheduleStore()
// Type assertion for store methods
const schedulingStore = workScheduleStore
const clinicStore = useClinicStore()

// Get the clinic ID and date range from inject (provided by parent component)
const injectedClinicId = inject<number>('clinicId')
const injectedStartDate = inject<string>('startDate')
const injectedEndDate = inject<string>('endDate')
const injectedEditMode = inject<boolean>('editMode')
const injectedDisplayMode = inject<'slot' | 'day'>('displayMode')

// Get the clinic ID and date range from props or localStorage
const props = defineProps<{
  clinicId?: number | null
  startDate?: string
  endDate?: string
  editMode?: boolean
  displayMode?: 'slot' | 'day'
}>()

// Use provided editMode and displayMode from props or inject
const editMode = computed(() => {
  return props.editMode !== undefined ? props.editMode : injectedEditMode || false
})

const displayMode = computed(() => {
  return props.displayMode !== undefined ? props.displayMode : injectedDisplayMode || 'slot'
})

const clinicId = computed(() => {
  // First check if props.clinicId is available
  if (props.clinicId) {
    return props.clinicId
  }
  
  // Then check if injected clinicId is available
  if (injectedClinicId) {
    return injectedClinicId
  }
  
  // Finally fallback to localStorage
  const storedId = localStorage.getItem('id_clinic')
  return storedId ? JSON.parse(storedId) : null
})

// Watch for clinic ID changes to refetch data
watch(clinicId, async (newClinicId) => {
  if (newClinicId) {
    await fetchClinicName()
    await fetchWeeklySchedule()
  }
})

// Interface definitions
interface EmployeeSchedule {
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  checked: boolean
}

interface Employee {
  id_employee: number
  name_display: string
  type_occupation: number
  flg_calendar: boolean
  display_order: number | null
}

interface EmployeeSchedules {
  [key: string]: {
    [key: number]: EmployeeSchedule
  }
}

interface TimeSlot {
  slot_number: number
  business_time: {
    start: string
    end: string
  }
  checkin_time?: {
    start: string
    end: string
  }
  ticket_issue_time?: {
    start: string
    end: string
  }
  ticket_limit?: number | null
}

interface DayData {
  display_date: string
  date?: string | null
  day_index: number
  day_of_week: number
  type_weekday: number // UI value (11-17, 18 for holiday)
  today: boolean
  business_hour_slot?: {
    id_business_hour_slot?: number
    type_business_day: number
    name_business_hour: string
    display_order?: number
    time_slots?: TimeSlot[]
  }
  employeeSchedules: EmployeeSchedules
  is_off_day: boolean
  slot_name?: string
  slot_type?: number
  employee_schedules?: Array<{
    id_employee: number
    name_display: string
    type_occupation: number
    flg_calendar: boolean
    schedules: Array<{
      id_employee_workschedule: number
      time_workschedule_start: string
      time_workschedule_end: string
      flg_whole_dayoff: boolean
    }>
  }>
  isHoliday?: boolean
}

// Interface for schedule
interface EmployeeScheduleItem {
  id_employee_workschedule: number | null;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  type_weekday?: number;
  checked: boolean;
}

// Sort doctors by display order
const sortedDoctors = computed(() =>
  employees.value.filter((emp) => emp.flg_calendar).sort((a, b) => (a.display_order || 999) - (b.display_order || 999))
)

// Calculate doctor column width
const doctorWidth = computed(() => {
  const numberColumnWidth = 100
  const timeSlotColumnWidth = window.innerWidth * 0.2211
  const totalRemainingWidth = 100 - (numberColumnWidth / window.innerWidth) * 100 - 22
  return sortedDoctors.value.length ? totalRemainingWidth / sortedDoctors.value.length : 0
})

// Helper function to check if a day is Saturday (type_weekday = 16)
const isSaturday = (day: DayData) => {
  return day.type_weekday === 16
}

// Helper function to check if a day is Sunday (type_weekday = 17)
const isSunday = (day: DayData) => {
  return day.type_weekday === 17
}

// Helper function to check if a day is a Holiday (type_weekday = 18)
const isHoliday = (day: DayData) => {
  return day.type_weekday === 18
}

// Change the selected week
const changeDate = async (prefix: 'next' | 'prev') => {
  if (prefix === 'next') {
    selectedDate.value = selectedDate.value.add(1, 'week')
  } else {
    selectedDate.value = selectedDate.value.subtract(1, 'week')
  }
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
  await fetchWeeklySchedule()
}

// Fetch weekly schedule data
const fetchWeeklySchedule = async () => {
  if (!clinicId.value) return
  
  isLoading.value = true

  try {
    // Fetch scheduling data for the selected week
    const response = await workScheduleStore.fetchSchedulingData({
      clinic_id: clinicId.value,
      period_type: 'weekly',
      start_date: selectedDate.value.format('YYYY-MM-DD')
    })

    // Set employees from API response
    if (response?.employees) {
      employees.value = response.employees
    }

    buildWeeklyData()
  } catch (error) {
    console.error('Error fetching weekly schedule:', error)
  } finally {
    isLoading.value = false
  }
}

// Build weekly data structure from scheduling API response
const buildWeeklyData = () => {
  const schedulingData = schedulingStore.getWeeklySchedulingData()
  if (!schedulingData?.length) return

  weeklyData.value = schedulingData.map((day: any) => {
    // For holiday (type_weekday: 18), we don't need to calculate a specific date
    const dayDate = day.type_weekday === 18 
      ? null 
      : selectedDate.value.add(day.day_index, 'day')

    // Create day data object
    const dayData: DayData = {
      // For holiday, use a static display name
      display_date: day.type_weekday === 18 ? '祝祭日' : (dayDate ? dayDate.format('ddd') : ''),
      date: dayDate ? dayDate.format('YYYY-MM-DD') : null,
      day_index: day.day_index,
      day_of_week: day.day_index, // 0-6 for Monday-Sunday
      type_weekday: day.type_weekday, // 11-17 for UI display, 18 for holiday
      today: dayDate ? dayjs().isSame(dayDate, 'day') : false,
      business_hour_slot: day.business_hour_slot,
      employeeSchedules: {},
      is_off_day: day.business_hour_slot?.type_business_day === 90,
      slot_name: day.slot_name,
      slot_type: day.slot_type,
      employee_schedules: day.employee_schedules,
      isHoliday: day.type_weekday === 18
    }

    // Map employee availability data
    if (day.employee_schedules) {
      day.employee_schedules.forEach((employeeSchedule: any) => {
        if (!dayData.employeeSchedules[employeeSchedule.id_employee]) {
          dayData.employeeSchedules[employeeSchedule.id_employee] = {}
        }

        // Map each schedule to a slot number
        employeeSchedule.schedules.forEach((schedule: any) => {
          // Figure out which time slot this schedule corresponds to
          const slotNumber = getSlotNumberForTime(schedule.time_workschedule_start, day.business_hour_slot?.time_slots)

          if (slotNumber) {
            dayData.employeeSchedules[employeeSchedule.id_employee][slotNumber] = {
              checked: schedule.flg_whole_dayoff,
              id_employee_workschedule: schedule.id_employee_workschedule || null,
              time_workschedule_start: schedule.time_workschedule_start,
              time_workschedule_end: schedule.time_workschedule_end,
              flg_whole_dayoff: schedule.flg_whole_dayoff
            }
          }
        })
      })
    }

    // Create slots for all doctors and all potential time slots
    sortedDoctors.value.forEach((doctor) => {
      if (!dayData.employeeSchedules[doctor.id_employee]) {
        dayData.employeeSchedules[doctor.id_employee] = {}
      }

      // Ensure all slots exist
      if (day.business_hour_slot?.time_slots) {
        day.business_hour_slot.time_slots.forEach((slot: any, index: number) => {
          const slotNumber = index + 1
          if (!dayData.employeeSchedules[doctor.id_employee][slotNumber]) {
            dayData.employeeSchedules[doctor.id_employee][slotNumber] = {
              checked: false,
              id_employee_workschedule: null,
              flg_whole_dayoff: false
            }
          }
        })
      }
    })

    return dayData
  })

  console.log('Weekly data built:', weeklyData.value)
}

// Helper function to match a time to a slot number
const getSlotNumberForTime = (time: string, timeSlots?: TimeSlot[]): number | null => {
  if (!timeSlots) return null

  for (let i = 0; i < timeSlots.length; i++) {
    if (timeSlots[i].business_time.start === time) {
      return i + 1
    }
  }
  return null
}

// Helper function to get business day type name
const typeBusinessDayName = (value: number) => typeBusinessDay.find((v) => v.value === value)

// Get total number of time slots for a day
const getTotalSlots = (timeSlots?: TimeSlot[]) => {
  return timeSlots?.length || 0
}

// Check if all slots for an employee on a specific day are marked as off
const isAllSlotChecked = (data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return false

  const values = Object.values(employeeSchedules)
  return values.length > 0 && values.every((item) => item.checked === true)
}

// Set all slots for an employee on a specific day to checked or unchecked
const setAllSlotChecked = (checked: boolean, data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return

  Object.values(employeeSchedules).forEach((item) => {
    item.checked = checked
  })
}

// Update a single slot's checked state
const updateSlotChecked = (day: DayData, employeeId: number | string, slotNumber: number, value: boolean) => {
  if (day.employeeSchedules[employeeId]?.[slotNumber]) {
    day.employeeSchedules[employeeId][slotNumber].checked = value
  }
}

// Update all slots for an employee on a specific day
const updateAllSlotChecked = (day: DayData, employeeId: number | string, value: boolean) => {
  setAllSlotChecked(value, day, employeeId)
}

// Helper function to prepare the bulk update data
const prepareBulkUpdateData = async (): Promise<any[]> => {
  // Prepare payload for scheduling API - directly filter to only include data with changes or existing IDs
  const payload = weeklyData.value.flatMap((day) => {
    return Object.entries(day.employeeSchedules).flatMap(([employeeId, slots]) => {
      return Object.entries(slots as Record<string, EmployeeSchedule>)
        .filter(([_, schedule]) => {
          // Only include records that have changes or existing IDs
          return schedule.id_employee_workschedule || schedule.checked
        })
        .map(([slotNumber, schedule]) => {
          // Find the corresponding time slot
          const timeSlot = day.business_hour_slot?.time_slots?.[parseInt(slotNumber) - 1]

          // Ensure type_weekday is between 11-18 (Mon-Sun, Holiday)
          const typeWeekday = day.type_weekday >= 11 && day.type_weekday <= 18 
            ? day.type_weekday 
            : 11; // Default to Monday if invalid

          // Keep track of existing schedule IDs for potential deletion
          const id_employee_workschedule = schedule.id_employee_workschedule || null;

          return {
            id_employee_workschedule: id_employee_workschedule,
            id_employee: parseInt(employeeId as string),
            type_weekday: typeWeekday, 
            time_workschedule_start: timeSlot?.business_time.start || '00:00:00',
            time_workschedule_end: timeSlot?.business_time.end || '00:00:00',
            flg_whole_dayoff: schedule.checked,
            id_clinic: clinicId.value
          }
        })
    })
  }).filter(item => item !== null && item !== undefined);

  return payload;
}

// Save changes
const bulkUpdate = async () => {
  // If in day mode, directly update without showing modal
  if (displayMode.value === 'day') {
    await directBulkUpdate()
    return
  }

  const payload = await prepareBulkUpdateData();
  // Store payload in the ref for use by the modal
  bulkUpdateData.value = payload

  // Show modal instead of making API call directly
  openBulkUpdateModal()
}

// Direct bulk update for day mode
const directBulkUpdate = async () => {
  try {
    isLoading.value = true

    // Collect selected employees and days with checked schedules
    const selectedEmployees = new Set<number>()
    const selectedData: any[] = []
    
    weeklyData.value.forEach((day) => {
      if (day.is_off_day) return // Skip off days

      Object.entries(day.employeeSchedules).forEach(([employeeId, slots]) => {
        const hasCheckedSlots = Object.values(slots as Record<string, EmployeeSchedule>)
          .some(schedule => schedule.checked === true)
        
        if (hasCheckedSlots) {
          selectedEmployees.add(parseInt(employeeId as string))
          selectedData.push({
            id_employee: parseInt(employeeId as string),
            type_weekday: day.type_weekday, // Use the actual weekday type (11-18)
            date_booking_special: null // No special date for weekly view
          })
        }
      })
    })

    if (selectedData.length === 0) {
      mtUtils.alert('更新する予定がありません')
      return
    }

    // Remove duplicates based on employee and weekday
    const uniqueData = selectedData.filter((item, index, self) => 
      index === self.findIndex(t => 
        t.id_employee === item.id_employee && t.type_weekday === item.type_weekday
      )
    )

    // Prepare employee work schedules for API
    const employeeList = Array.from(selectedEmployees).map((employeeId) => {
      // Filter uniqueData for current employee
      const employeeData = uniqueData.filter((item: any) => item.id_employee === employeeId)
      
      const employeeWorkscheduleList = employeeData.map((data: any) => ({
        type_weekday: data.type_weekday,
        time_workschedule_start: '00:00:00',
        time_workschedule_end: '00:00:00',
        flg_whole_dayoff: true, // Always true for day mode
        date_booking_special: null,
        min_rest: 0
      }))

      return {
        id_employee: employeeId,
        employee_workschedule_list: employeeWorkscheduleList
      }
    })

    // Call the API
    if (clinicId.value === null) {
      throw new Error('Clinic ID is required');
    }
    
    await workScheduleStore.createOrUpdateWorkSchedules({
      id_clinic: clinicId.value,
      employee_list: employeeList
    })

    // Show success message
    mtUtils.autoCloseAlert('休みを適用しました！')
    
    // Refresh data and exit edit mode
    await fetchWeeklySchedule()
  } catch (error) {
    console.error('Failed to update schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの更新に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Open the bulk update modal
const openBulkUpdateModal = async () => {
  if (!bulkUpdateData.value.length) {
    mtUtils.alert('更新する予定がありません')
    return
  }

  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: bulkUpdateData.value,
    clinicId: clinicId.value,
    displayMode: displayMode.value,
    onSuccess: async () => {
      // Refresh data
      await fetchWeeklySchedule()
    }
  })
}

// Expose bulkUpdate method to parent
defineExpose({
  bulkUpdate,
  fetchWeeklySchedule,
  prepareBulkUpdateData
})

// Fetch clinic name
const fetchClinicName = async () => {
  if (clinicId.value) {
    try {
      const clinic = await clinicStore.fetchClinicById(clinicId.value)
      clinicName.value = clinic.name_clinic_display
    } catch (error) {
      console.error('Error fetching clinic name:', error)
      clinicName.value = ''
    }
  } else {
    clinicName.value = ''
  }
}

// Format time string to display format (removing :00 seconds)
const formatTimeDisplay = (time: string) => {
  return time ? time.replace(/:00$/, '') : ''
}

// Check if all slots for an employee across all days in the current week are marked as off
const isAllDaysSlotChecked = (employeeId: number | string) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => !day.is_off_day)
  if (!days.length) return false
  
  return days.every(day => {
    const employeeSchedules = day.employeeSchedules[employeeId]
    if (!employeeSchedules) return false
    
    const values = Object.values(employeeSchedules) as EmployeeSchedule[]
    return values.length > 0 && values.every(item => item.checked === true)
  })
}

// Update all slots for an employee across all days in the current week
const updateAllDaysSlotChecked = (employeeId: number | string, value: boolean) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => !day.is_off_day)
  days.forEach(day => {
    updateAllSlotChecked(day, employeeId, value)
  })
}

// Add click handler for schedule items
const handleScheduleClick = async (empSchedule: EmployeeSchedule) => {
  if (empSchedule.id_employee_workschedule) {
    await mtUtils.smallPopup(BulkUpdateShiftModal, {
      bulkUpdateData: [],
      clinicId: clinicId.value,
      workScheduleId: empSchedule.id_employee_workschedule,
      onSuccess: async () => {
        await fetchWeeklySchedule()
      }
    })
  }
}

// Add a new helper function to get the highest priority schedule
const getHighestPrioritySchedule = (employeeSchedules: any[], employeeId: number): EmployeeScheduleItem | null => {
  if (!employeeSchedules || !employeeSchedules.length) return null;
  
  // Filter schedules for the given employee
  const empSchedules = employeeSchedules.filter(emp => emp.id_employee === employeeId);
  if (!empSchedules.length || !empSchedules[0].schedules || !empSchedules[0].schedules.length) return null;
  
  const schedules = empSchedules[0].schedules;
  
  // Priority: Regular schedules (type_weekday=11-18)
  const regularSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday && s.type_weekday >= 11 && s.type_weekday <= 18);
  if (regularSchedules.length) {
    return {
      ...regularSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule || null,
      checked: regularSchedules[0].flg_whole_dayoff
    };
  }
  
  // If no matching schedules, return the first one with checked property
  return {
    ...schedules[0],
    id_employee_workschedule: empSchedules[0].id_employee_workschedule || null,
    checked: schedules[0].flg_whole_dayoff
  };
};

// Function to display multiple time slots
const formatMultipleTimeSlots = (schedule: EmployeeScheduleItem | null): string => {
  if (!schedule) return '';
  
  // If it's a day off, return the "休" character
  if (schedule.flg_whole_dayoff) {
    return '<span class="text-darkred">休</span>';
  }
  
  let result = '';
  
  // Format primary time slot
  if (schedule.time_workschedule_start && schedule.time_workschedule_end) {
    result += `${formatTimeDisplay(schedule.time_workschedule_start)}~${formatTimeDisplay(schedule.time_workschedule_end)}`;
  }
  
  // Add secondary time slot if exists
  // if (schedule.time_workschedule_start2 && schedule.time_workschedule_end2) {
  //   result += `<br>${formatTimeDisplay(schedule.time_workschedule_start2)}~${formatTimeDisplay(schedule.time_workschedule_end2)}`;
  // }
  
  // // Add tertiary time slot if exists
  // if (schedule.time_workschedule_start3 && schedule.time_workschedule_end3) {
  //   result += `<br>${formatTimeDisplay(schedule.time_workschedule_start3)}~${formatTimeDisplay(schedule.time_workschedule_end3)}`;
  // }
  
  return result;
}

// Initialize on component mount
onMounted(async () => {
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
    
  // Only fetch data if clinicId is available
  if (clinicId.value) {
    await fetchWeeklySchedule()
  }
})
</script>

<template>
  <q-layout container :style="{ height: 'calc(100vh - 70px)' }">
    <q-page-container>
      <q-page>
        <div class="q-pl-xl q-pr-sm">
          <div class="row items-center justify-between q-my-md"></div>

          <!-- Weekly schedule view -->
          <div class="calendar-view" v-if="!isLoading && weeklyData.length">
            <div class="row items-center">
              <div class="col-1 bg-grey-300 q-ba-400 q-pa-sm h-40" style="width: 75px"></div>
              <div class="col bg-grey-300 text-center q-ba-400 h-40 q-pa-xs" style="width: 22.11%">営業時間帯</div>
              <div class="doctor-wrapper flex">
                <div
                  class="col-1- bg-grey-100 text-center q-ba-400 q-pa-sm doc-name"
                  v-for="(doctor, i) in sortedDoctors"
                  :key="i"
                  :style="{ width: `${doctorWidth}%` }"
                >
                  <div class="">
                    <MtFormCheckBox
                      v-if="editMode"
                      type="checkbox"
                      label=""
                      :checked="isAllDaysSlotChecked(doctor.id_employee)"
                      class="caption1 q-mt-xs"
                      style="padding: 0; border: none"
                      @update:checked="(newVal) => updateAllDaysSlotChecked(doctor.id_employee, newVal)"
                    />

                    {{ doctor?.name_display }}
                  </div>
                </div>
              </div>
            </div>

            <div class="row" v-for="(day, i) in weeklyData" :key="i" :class="{ off_day: day.is_off_day }">
              <span
                class="q-bb q-br q-bl q-pa-sm q-pt-sm flex items-center h-95 w-75 display_date"
                :class="{ 
                  sat: isSaturday(day), 
                  sun: isSunday(day),
                  'holiday': day.type_weekday === 18
                }"
              >
                {{ day.display_date }}
              </span>
              <div
                class="business-hours-column q-bb q-br q-pa-sm"
                :class="{ 
                  'bg-accent-100': day.is_off_day,
                }"
                style="width: 22.11%"
              >
                <div class="caption1 q-mb-sm">
                  {{ day.slot_name || typeBusinessDayName(day.business_hour_slot?.type_business_day || 0)?.label }} /
                  {{ day.business_hour_slot?.name_business_hour }}
                </div>
                <template v-if="day.business_hour_slot?.time_slots">
                  <div class="flex flex-wrap gap-2 text-caption">
                    <div 
                      v-for="(timeSlot, slotIdx) in day.business_hour_slot.time_slots" 
                      :key="slotIdx"
                      class="flex items-center gap-2"
                    >
                      枠{{ slotIdx + 1 }}
                      {{ formatTimeDisplay(timeSlot.business_time.start) }} ~
                      {{ formatTimeDisplay(timeSlot.business_time.end) }}
                    </div>
                  </div>
                </template>
              </div>

              <template v-for="(doctor, j) in sortedDoctors" :key="j">
                <div
                  class="col-1 text-center column justify-around q-bb q-br q-bl caption2 h-95 bg-grey-100 availability"
                  :class="{ offDay: day.is_off_day }"
                  :style="{ width: `${doctorWidth}%` }"
                >
                  <template v-if="!day.is_off_day">
                    <template v-if="!editMode">
                      <!-- Show only the highest priority schedule for this employee -->
                      <template v-if="day.employee_schedules">
                        <div 
                          v-if="!getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)" 
                          class="text-grey-700 h-30"
                        >
                          <!-- 予定なし -->
                        </div>
                        <div 
                          v-else
                          :class="{ 'text-darkred': getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                          class="schedule-item h-30 cursor-pointer"
                          @click="handleScheduleClick(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee) || {
                            id_employee_workschedule: null,
                            time_workschedule_start: '',
                            time_workschedule_end: '',
                            flg_whole_dayoff: false,
                            checked: false
                          })"
                        >
                          <template v-if="getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                            休
                          </template>
                          <template v-else>
                            {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                            {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                          </template>
                        </div>
                      </template>
                    </template>
                    <template v-else>
                      <div 
                        class="flex flex-col justify-center items-center cursor-pointer"
                        @click="updateAllSlotChecked(day, doctor.id_employee, !isAllSlotChecked(day, doctor.id_employee))"
                      >
                        <div class="flex items-center">
                          <MtFormCheckBox
                            type="checkbox"
                            label=""
                            :checked="isAllSlotChecked(day, doctor.id_employee)"
                            class="caption1 q-pa-none"
                            style="padding: 0; border: none"
                            @update:checked="(newVal) => updateAllSlotChecked(day, doctor.id_employee, newVal)"
                          />
                        </div>
                        <!-- Display time information alongside checkbox -->
                        <template v-if="day.employee_schedules">
                          <template v-if="!getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)">
                            <!-- <div class="text-grey-700 h-30">予定なし</div> -->
                          </template>
                          <template v-else>
                            <div
                              :class="{ 'text-darkred': getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                              class="schedule-item h-30"
                            >
                              <template v-if="getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                                休
                              </template>
                              <template v-else>
                                {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                                {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                              </template>
                            </div>
                          </template>
                        </template>
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    <div class="text-darkred">休</div>
                  </template>
                </div>
              </template>
            </div>
          </div>

          <!-- Loading or no data states -->
          <div v-else-if="isLoading" class="flex justify-center items-center" style="height: 300px">
            <q-spinner size="40px" color="primary" />
          </div>
          <div v-else class="flex justify-center items-center" style="height: 300px">
            <div class="text-grey-700">データがありません</div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<style lang="scss" scoped>
.doctor-wrapper {
  display: flex;
  flex-direction: row;
  width: calc(100% - 75px - 22.11%);
}

.doctor-wrapper .doc-name {
  flex: 1;
  text-align: center;
}

.availability {
  flex: 1;
  text-align: center;
}

.business-hours-column {
  display: flex;
  flex-direction: column;
}

.slot-checkbox-wrapper {
  display: flex;
  align-items: center;
  padding: 2px 0;

  .slot-label {
    margin-right: 5px;
  }
}

@media screen and (max-width: 1180px) and (min-width: 820px) {
  .col-2.flex.items-center.justify-between.q-bb.q-br.q-pa-sm.h-95.bg-accent-100 {
    width: 12% !important;
  }

  .business-hours-column {
    width: 28.11% !important;
  }

  .col-4.bg-grey-300.text-center.q-ba-400.q-pa-sm {
    width: 28.11% !important;
  }
}

.calendar-view {
  .h-40 {
    height: 32px;
  }
  .h-95 {
    height: 3.6rem;
  }
  .h-30 {
    height: 2rem;
  }
  .h-45 {
    height: 3rem;
  }
  .w-75 {
    width: 75px;
  }
  .p-5 {
    padding: 5px;
  }
  .bg-yellow-100 {
    background-color: rgba(255, 235, 59, 0.3) !important;
  }
  .schedule-item {
    padding: 2px;
    font-size: 12px;
    border-bottom: 1px solid #e0e0e0;
    &:last-child {
      border-bottom: none;
    }
  }
  .slotTime {
    .timerange {
      padding-bottom: 6px;
      border-bottom: 1px solid #e0e0e0;
      &:nth-last-child(1) {
        border-bottom: none;
      }
    }
  }
  .availability {
    width: 75px;
    div {
      border-bottom: 1px solid #e0e0e0;
      padding: 3px;
      padding-bottom: 6px;
      &:nth-last-child(1) {
        border-bottom: none;
      }
    }
  }
  .truncated {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    width: 250px !important;
    word-wrap: break-word;
    white-space: normal !important;
    text-align: left;
    @media only screen and (min-width: 1500px) {
      width: 130px !important;
    }
  }
  .off_day {
    div {
      background-color: $grey-400 !important;
    }
    .display_date {
      color: $darkRed;
    }
  }
  .sat {
    color: $blue !important;
  }
  .sun {
    color: $darkRed !important;
  }
  .offDay {
    background-color: $grey-400 !important;
  }
  .doctor-wrapper {
    height: 32px;
    .doc-name {
      width: 75px;
      font-size: 12px;
    }
  }
  .holiday {
    color: $darkRed !important;
    font-weight: bold;
    background-color: rgba(244, 67, 54, 0.1) !important;
  }
}

.cursor-pointer {
  cursor: pointer;
}
</style>
