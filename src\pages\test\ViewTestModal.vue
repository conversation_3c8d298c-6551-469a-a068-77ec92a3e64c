<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import useEmployeeStore from '@/stores/employees'
import useBookingItemStore from '@/stores/booking-items'
import useCommonStore from '@/stores/common'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import dayjs from 'dayjs'
import weekday from 'dayjs/plugin/weekday'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import fallbackScheduleData from '@/data/scheduleData.json'
import ScheduleDetailModal from './components/ScheduleDetailModal.vue'

// Extend dayjs with plugins
dayjs.extend(weekday)
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

const props = withDefaults(
  defineProps<{
    data?: any
    searchData?: any
    selectedClinic?: number
  }>(),
  {
    data: () => ({}),
    searchData: () => ({}),
    selectedClinic: 1
  }
)

const emits = defineEmits(['close'])

// Initialize stores
const employeeStore = useEmployeeStore()
const bookingItemStore = useBookingItemStore()
const commonStore = useCommonStore()

// Get reactive data from stores
const { getEmployees } = storeToRefs(employeeStore)
const { getBookingItems, getCurrentBookingItem, getBookableItemService } = storeToRefs(bookingItemStore)
const { getCommonTypeServiceOptionList } = storeToRefs(commonStore)

// Loading and error states
const loading = ref(false)
const error = ref('')

// Calendar data from API types
interface SlotData {
  display: string
  available_count: number
  booked_count: number
  total_capacity: number
  is_clinic_open: boolean
  is_bookable: boolean
  booking_items: any[]
}

interface CalendarGrid {
  [timeSlot: string]: {
    [date: string]: SlotData
  }
}

interface CalendarData {
  calendar_grid: CalendarGrid
  date_headers: string[]
  time_slots: string[]
  summary: any
}

// Calendar data from API
const calendarData = ref<CalendarData>({
  calendar_grid: {},
  date_headers: [],
  time_slots: [],
  summary: {}
})

// Selected cell data
const selectedCellData = ref({
  date: '',
  time: '',
  itemServiceId: 0,
  bookingItemId: 0
})

// Current date range (2-week view)
const currentStartDate = ref('')
const currentEndDate = ref('')

// Real data for 予約カレンダー管理 dropdown (Service Types from common store)
const calendarManagementOptions = computed(() => 
  getCommonTypeServiceOptionList.value.map((option: any) => ({
    label: option.label,
    value: option.value
  }))
)

// Real data for booking items from booking store
const bookingItems = computed(() => {
  // use getBookableItemService
  console.log(getBookableItemService.value)
  return getBookableItemService.value.map((item: any) => ({
    id: item.id_item_service || 0,
    name: item.name_item_service || 'Booking Item',
    service: item.name_category1 || 'Service',
    allowsEmployeeWorkshift: Boolean(item.flg_book_per_employee) ?? false,
  }))
})

// Real data for employee dropdown from employee store
const employeeOptions = computed(() => [
  { label: '未選択', value: '' }, // Unselected option
  ...employeeStore.getEmployees.map((employee: any) => ({
    label: employee.name_display,
    value: String(employee.id_employee)
  }))
])

// Selected values
const selectedCalendarManagement = ref(calendarManagementOptions.value[0]?.value || 1)
const selectedBookingItem = ref(bookingItems.value[0]?.id || 1)
const selectedEmployee = ref('') // Default to unselected

// Check if current booking item allows employee workshift
const currentBookingItemAllowsEmployee = computed(() => {
  const currentItem = bookingItems.value.find(item => item.id === selectedBookingItem.value)
  return currentItem?.allowsEmployeeWorkshift || false
})

// Initialize current date range (2-week view starting from today)
const initializeDateRange = () => {
  // Start from Monday of current week
  const startDate = dayjs().startOf('day')
  const endDate = startDate.add(13, 'days') // 2 weeks
  
  currentStartDate.value = startDate.format('YYYY-MM-DD')
  currentEndDate.value = endDate.format('YYYY-MM-DD')
}

// Format date for display (e.g., "10" for date number)
const formatDateNumber = (dateStr: string) => {
  return dayjs(dateStr).format('D')
}

// Get day of week in Japanese
const getDayOfWeek = (dateStr: string) => {
  const days = ['日', '月', '火', '水', '木', '金', '土']
  return days[dayjs(dateStr).day()]
}

// Create columns for q-table
const columns = computed(() => [
  {
    name: 'time',
    label: '',
    field: 'time',
    align: 'center' as const,
    style: 'width: 60px; background-color: #fafafa; font-weight: bold; font-size: 10px; color: #666;'
  },
  ...calendarData.value.date_headers.map((date: string) => ({
    name: `date_${date}`,
    label: formatDateNumber(date),
    field: `date_${date}`,
    align: 'center' as const,
    style: 'min-width: 50px;',
    headerStyle: 'background-color: #f8f9fa; font-weight: bold;',
    dayOfWeek: getDayOfWeek(date),
    fullDate: date
  }))
])

// Create rows for q-table
const rows = computed(() => 
  calendarData.value.time_slots.map((timeSlot: string) => {
    const row: any = { time: timeSlot }
    calendarData.value.date_headers.forEach((date: string) => {
      const slotData = calendarData.value.calendar_grid[timeSlot]?.[date]
      row[`date_${date}`] = slotData?.display || '-'
    })
    return row
  })
)

// Generate fallback data based on current date range
const generateFallbackData = (startDate: string, endDate: string): CalendarData => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const dates: string[] = []
  
  // Generate date headers for the range
  let current = start
  while (current.isSameOrBefore(end, 'day')) {
    dates.push(current.format('YYYY-MM-DD'))
    current = current.add(1, 'day')
  }
  
  const timeSlots = ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00']
  
  // Create calendar grid with dummy data
  const calendarGrid: CalendarGrid = {}
  
  timeSlots.forEach(timeSlot => {
    calendarGrid[timeSlot] = {}
    dates.forEach(date => {
      const isWeekend = dayjs(date).day() === 0 || dayjs(date).day() === 6
      
      if (isWeekend) {
        // Weekends are closed
        calendarGrid[timeSlot][date] = {
          display: '-',
          available_count: 0,
          booked_count: 0,
          total_capacity: 0,
          is_clinic_open: false,
          is_bookable: false,
          booking_items: []
        }
      } else {
        // Random booking data for weekdays
        const total = 3
        const booked = Math.floor(Math.random() * (total + 1))
        const available = total - booked
        
        calendarGrid[timeSlot][date] = {
          display: `${booked}/${total}`,
          available_count: available,
          booked_count: booked,
          total_capacity: total,
          is_clinic_open: true,
          is_bookable: available > 0,
          booking_items: []
        }
      }
    })
  })
  
  return {
    calendar_grid: calendarGrid,
    date_headers: dates,
    time_slots: timeSlots,
    summary: {
      total_slots_checked: timeSlots.length * dates.length,
      date_range_days: dates.length,
      time_slots_count: timeSlots.length
    }
  }
}

// API call to fetch calendar scheduler data
const fetchCalendarData = async () => {
  try {
    loading.value = true
    error.value = ''

    // Build API parameters
    const params: any = {
      start_date: currentStartDate.value,
      end_date: currentEndDate.value,
      id_clinic: Number(props.selectedClinic),
      time_start: '00:00',
      time_end: '23:59'
    }

    // Add optional filters
    if (selectedCalendarManagement.value) {
      params.id_item_service = Number(selectedBookingItem.value)
    }
    if (selectedEmployee.value) {
      params.id_employee = Number(selectedEmployee.value)
    }

    // Make API call
    const response = await mtUtils.callApi(
      selectOptions.reqMethod.GET,
      '/booking/tx-slots/calendar-scheduler/',
      params
    )

    // Extract calendar data from response
    const apiData = response?.data || response
    
    if (!apiData || !apiData.calendar_grid) {
      throw new Error('Invalid API response: missing calendar data')
    }

    // Process and set calendar data
    const processedData = processIncompleteCalendarData(apiData, currentStartDate.value, currentEndDate.value)
    calendarData.value = processedData

  } catch (err: any) {
    error.value = err.message || 'Failed to load calendar data'
    console.error('Calendar API error:', err)
  } finally {
    loading.value = false
  }
}

// Process incomplete calendar data to fill in missing dates
const processIncompleteCalendarData = (apiData: any, startDate: string, endDate: string): CalendarData => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const allDates: string[] = []
  
  // Generate all dates in the range
  let current = start
  while (current.isSameOrBefore(end, 'day')) {
    allDates.push(current.format('YYYY-MM-DD'))
    current = current.add(1, 'day')
  }
  
  // Get time slots from API data or use default
  const timeSlots = apiData.time_slots || Object.keys(apiData.calendar_grid || {}) || ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00']
  
  // Find a reference date that has data to use as template
  let referenceDate: string | null = null
  let referenceData: any = null
  
  // Look for any date that has complete data in the API response
  for (const timeSlot of timeSlots) {
    const timeSlotData = apiData.calendar_grid?.[timeSlot]
    if (timeSlotData) {
      const availableDates = Object.keys(timeSlotData)
      if (availableDates.length > 0) {
        referenceDate = availableDates[0]
        break
      }
    }
  }
  
  // Process calendar grid to fill in missing dates
  const processedCalendarGrid: CalendarGrid = {}
  
  timeSlots.forEach((timeSlot: string) => {
    processedCalendarGrid[timeSlot] = {}
    
    allDates.forEach((date: string) => {
      // Check if API data has this time slot and date
      const apiSlotData = apiData.calendar_grid?.[timeSlot]?.[date]
      
      if (apiSlotData) {
        // Use API data if available
        processedCalendarGrid[timeSlot][date] = apiSlotData
      } else if (referenceDate && apiData.calendar_grid?.[timeSlot]?.[referenceDate]) {
        // Use reference date data as template for missing dates
        const referenceSlotData = apiData.calendar_grid[timeSlot][referenceDate]
        processedCalendarGrid[timeSlot][date] = {
          ...referenceSlotData
        }
      } else {
        // Fallback to default values if no reference data available
        const isWeekend = dayjs(date).day() === 0 || dayjs(date).day() === 6
        
        if (isWeekend) {
          // Default weekend data (closed)
          processedCalendarGrid[timeSlot][date] = {
            display: '-',
            available_count: 0,
            booked_count: 0,
            total_capacity: 0,
            is_clinic_open: false,
            is_bookable: false,
            booking_items: []
          }
        } else {
          // Default weekday data (available)
          processedCalendarGrid[timeSlot][date] = {
            display: '0/3',
            available_count: 3,
            booked_count: 0,
            total_capacity: 3,
            is_clinic_open: true,
            is_bookable: true,
            booking_items: []
          }
        }
      }
    })
  })
  
  return {
    calendar_grid: processedCalendarGrid,
    date_headers: allDates,
    time_slots: timeSlots,
    summary: {
      total_slots_checked: timeSlots.length * allDates.length,
      date_range_days: allDates.length,
      time_slots_count: timeSlots.length
    }
  }
}

const convertTypeWeekdayFromDate = (date: string) => {
  const day = dayjs(date).day()
  switch (day) {
    case 0: return 17 // Sunday = 17
    case 1: return 11 // Monday = 11
    case 2: return 12 // Tuesday = 12
    case 3: return 13 // Wednesday = 13
    case 4: return 14 // Thursday = 14
    case 5: return 15 // Friday = 15
    case 6: return 16 // Saturday = 16
  }
}
// Handle cell click to open schedule detail modal
const handleCellClick = async (timeSlot: string, date: string) => {
  if (selectedEmployee.value == '') {
    mtUtils.autoCloseAlert('担当者を選択してください')
    return
  }

  // Get the cell data from calendar grid
  const cellData = calendarData.value.calendar_grid[timeSlot]?.[date]
  
  // Only allow clicking on available cells
  if (!cellData || !cellData.is_clinic_open || cellData.display === '-') {
    return
  }
  
  // Set selected data
  selectedCellData.value = {
    date: date,
    time: timeSlot,
    itemServiceId: Number(selectedBookingItem.value),
    bookingItemId: 0
  }
  
  try {
    // Fetch booking item using the filter
    await bookingItemStore.fetchBookingItemByFilter({
      id_clinic: Number(props.selectedClinic),
      id_item_service: Number(selectedBookingItem.value)
    })
    
    // Get the booking item ID from the store
    if (bookingItemStore.currentBookingItem?.booking_item.id_booking_item) {
      selectedCellData.value.bookingItemId = bookingItemStore.currentBookingItem.booking_item.id_booking_item
    }

    const typeWeekdayConvert = convertTypeWeekdayFromDate(selectedCellData.value.date)
    
    // Show the modal using mtUtils.popup
    mtUtils.smallPopup(ScheduleDetailModal, {
      selectedDate: selectedCellData.value.date,
      selectedTime: selectedCellData.value.time,
      selectedEmployee: selectedEmployee.value,
      selectedClinic: props.selectedClinic,
      selectedTypeWeekday: typeWeekdayConvert,
      selectedBookingItem: selectedCellData.value.bookingItemId,
    })
  } catch (error) {
    console.error('Error fetching booking item:', error)
  }
}

const getCellClass = (value: string) => {
  if (value === '-') return 'cell-unavailable'
  if (value === '予約') return 'cell-reserved'
  if (value === '0/3' || /^0\/\d+$/.test(value)) return 'cell-full'
  if (/^\d+\/\d+$/.test(value)) return 'cell-available'
  return 'cell-default'
}

const navigatePrevious = async () => {
  const startDate = dayjs(currentStartDate.value).subtract(14, 'days')
  const endDate = startDate.add(13, 'days')
  
  currentStartDate.value = startDate.format('YYYY-MM-DD')
  currentEndDate.value = endDate.format('YYYY-MM-DD')
  
  await fetchCalendarData()
}

const navigateNext = async () => {
  const startDate = dayjs(currentStartDate.value).add(14, 'days')
  const endDate = startDate.add(13, 'days')
  
  currentStartDate.value = startDate.format('YYYY-MM-DD')
  currentEndDate.value = endDate.format('YYYY-MM-DD')
  
  await fetchCalendarData()
}

const navigatePreviousWeek = async () => {
  const startDate = dayjs(currentStartDate.value).subtract(7, 'days')
  const endDate = startDate.add(13, 'days')
  
  currentStartDate.value = startDate.format('YYYY-MM-DD')
  currentEndDate.value = endDate.format('YYYY-MM-DD')
  
  await fetchCalendarData()
}

const navigateNextWeek = async () => {
  const startDate = dayjs(currentStartDate.value).add(7, 'days')
  const endDate = startDate.add(13, 'days')
  
  currentStartDate.value = startDate.format('YYYY-MM-DD')
  currentEndDate.value = endDate.format('YYYY-MM-DD')
  
  await fetchCalendarData()
}

// Format current month display
const currentMonth = computed(() => {
  if (!currentStartDate.value) return ''
  return dayjs(currentStartDate.value).format('YYYY年MM月')
})

// Handle calendar management dropdown change
const onCalendarManagementChange = async () => {
  console.log('Calendar management changed:', selectedCalendarManagement.value)
  
  // Fetch filtered booking items when service type changes
  if (selectedCalendarManagement.value) {
    await bookingItemStore.fetchBookingItemByType({
      id_clinic: Number(props.selectedClinic),
      id_item_service: Number(selectedCalendarManagement.value)
    })
    
    // Reset booking item selection to first available item
    if (bookingItems.value.length > 0) {
      selectedBookingItem.value = bookingItems.value[0].id
    }
  }
  
  await fetchCalendarData()
}

// Handle booking item selection
const onBookingItemSelect = async (itemId: number) => {
  selectedBookingItem.value = itemId
  // Reset employee selection to unselected when booking item changes
  selectedEmployee.value = ''
  console.log('Booking item selected:', itemId)
  await fetchCalendarData()
}

// Handle employee dropdown change
const onEmployeeChange = async () => {
  console.log('Employee changed:', selectedEmployee.value)
  await fetchCalendarData()
}

// Watch for clinic changes from parent
watch(() => props.selectedClinic, async () => {
  await fetchCalendarData()
})

// Initialize data when component mounts
onMounted(async () => {
  try {
    initializeDateRange()
    
    // Load employee data if not already loaded
    if (getEmployees.value.length === 0) {
      await employeeStore.fetchEmployees({ id_clinic: Number(props.selectedClinic) })
    }
    
    // Load booking items data if not already loaded
    if (getBookingItems.value.length === 0 && !getCurrentBookingItem.value) {
      await bookingItemStore.fetchBookingItemByType({ id_clinic: Number(props.selectedClinic), id_item_service: Number(selectedCalendarManagement.value) })
    }
    
    // Load common service types data if not already loaded
    if (getCommonTypeServiceOptionList.value.length === 0) {
      await commonStore.fetchPreparationCommonList({ code_common: [11] })
    }

    // Set default values after data is loaded
    if (calendarManagementOptions.value.length > 0) {
      selectedCalendarManagement.value = calendarManagementOptions.value[0].value
    }
    if (bookingItems.value.length > 0) {
      selectedBookingItem.value = bookingItems.value[0].id
    }
    // Keep employee as unselected by default
    selectedEmployee.value = ''

    // Load initial calendar data
    await fetchCalendarData()
  } catch (error) {
    console.error('Error loading data:', error)
  }
})

</script>

<template>
  <q-card flat class="schedule-container">
    <!-- Loading Overlay -->
    <q-linear-progress v-if="loading" indeterminate color="primary" />
    
    <!-- Error Message -->
    <q-banner v-if="error" rounded class="bg-red-1 text-red-8 q-ma-md">
      <q-icon name="error" />
      {{ error }}
      <template v-slot:action>
        <q-btn flat color="red-8" label="Retry" @click="fetchCalendarData" />
      </template>
    </q-banner>

    <!-- Header Controls -->
    <q-card-section class="header-controls">
      <!-- Calendar Management Dropdown and Booking Items Buttons in same row -->
      <div class="row items-start q-gutter-md q-mb-md">
        <!-- Calendar Management Dropdown - Left -->
        <div class="col-auto">
          <q-select
            v-model="selectedCalendarManagement"
            :options="calendarManagementOptions"
            outlined
            dense
            emit-value
            map-options
            label="予約商品カテゴリ"
            style="min-width: 200px"
            @update:model-value="onCalendarManagementChange"
            :loading="loading"
          />
        </div>

        <!-- Booking Items Buttons - Right -->
        <div class="col booking-items-section">
          <div class="booking-items-buttons-row">
            <div class="booking-items-buttons scroll-x">
              <q-btn
                v-for="item in bookingItems"
                :key="item.id"
                :class="['booking-item-btn', { 'selected': selectedBookingItem === item.id }]"
                :label="item.name"
                @click="onBookingItemSelect(item.id)"
                :loading="loading"
                no-caps
                unelevated
                :color="selectedBookingItem === item.id ? 'primary' : 'grey-3'"
                :text-color="selectedBookingItem === item.id ? 'white' : 'black'"
                :flat="selectedBookingItem !== item.id"
                :push="selectedBookingItem === item.id"
                class="q-mr-xs"
                style="min-width: 150px; max-width: 220px; padding: 0 20px; font-size: 15px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
              />
            </div>
          </div>
        </div>
      </div>
    </q-card-section>

    <q-separator />

    <!-- Month Navigation -->
    <q-card-section class="month-navigation">
      <!-- Row 1: Month Navigation and Employee Selection -->
      <div class="row items-center justify-between q-mb-md">
        <!-- Month Navigation - Left -->
        <div class="row items-center">
          <q-btn flat round size="sm" icon="chevron_left" @click="navigatePrevious" :loading="loading" />
          <div class="text-h6 q-mx-lg">{{ currentMonth }}</div>
          <q-btn flat round size="sm" icon="chevron_right" @click="navigateNext" :loading="loading" />
        </div>
        
        <!-- Employee Dropdown - Right -->
        <div class="employee-section">
          <q-select
            v-model="selectedEmployee"
            :options="employeeOptions"
            outlined
            dense
            emit-value
            map-options
            label="担当者"
            style="min-width: 150px"
            @update:model-value="onEmployeeChange"
            :loading="loading"
          />
        </div>
      </div>

      <!-- Row 2: Week Navigation -->
      <div class="row items-center q-gutter-md">
        <q-btn flat size="sm" class="text-primary week-nav-btn" @click="navigatePreviousWeek" :loading="loading">
          &lt; 前の週
        </q-btn>
        <q-btn flat size="sm" class="text-primary week-nav-btn" @click="navigateNextWeek" :loading="loading">
          次の週 &gt;
        </q-btn>
      </div>
    </q-card-section>

    <q-separator />

    <!-- Schedule Table -->
    <q-card-section class="q-pa-none">
      <q-table
        :columns="columns"
        :rows="rows"
        row-key="time"
        flat
        bordered
        dense
        hide-pagination
        :pagination="{ rowsPerPage: 0 }"
        class="schedule-table"
        :loading="loading"
      >
        <!-- Custom header slot for date columns -->
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th auto-width class="time-header">
            </q-th>
            <q-th
              v-for="col in props.cols.slice(1)"
              :key="col.name"
              :props="props"
              class="date-header"
            >
              <div class="date-cell">
                <div class="date-number">{{ col.label }}</div>
                <div class="day-of-week" :class="{
                  'text-blue': col.dayOfWeek === '土',
                  'text-red': col.dayOfWeek === '日'
                }">
                  ({{ col.dayOfWeek }})
                </div>
              </div>
            </q-th>
          </q-tr>
        </template>

        <!-- Custom body slot for availability cells -->
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td class="time-slot">
              {{ props.row.time }}
            </q-td>
            <q-td
              v-for="col in props.cols.slice(1)"
              :key="col.name"
              :props="props"
              :class="['availability-cell', getCellClass(props.row[col.field])]"
              style="cursor: pointer;"
              @click="handleCellClick(props.row.time, col.fullDate)"
            >
              <span class="availability-text">
                {{ props.row[col.field] }}
              </span>
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </q-card-section>

    <q-separator />

    <!-- Legend -->
    <q-card-section class="legend">
      <div class="row items-center justify-end q-gutter-md">
        <div class="row items-center">
          <q-chip 
            size="sm" 
            color="light-blue-1" 
            text-color="blue-8"
            icon="square"
          >
            予約済み/予約可能件数
          </q-chip>
        </div>
        <div class="row items-center">
          <q-chip 
            size="sm" 
            color="grey-3" 
            text-color="grey-7"
            icon="square"
          >
            休日・予約不可に設定した日の時間帯
          </q-chip>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<style lang="scss" scoped>
.schedule-container {
  width: 100%;
}

.header-controls {
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e0;
}

.booking-items-section {
  .booking-items-buttons-row {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    width: 100%;
  }
  .booking-items-buttons {
    display: flex;
    flex-direction: row;
    gap: 8px;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 2px;
    align-items: flex-end;
    min-height: 36px;
    scrollbar-width: thin;
    scrollbar-color: #ccc #fafafa;
    width: 100%;
  }
  .booking-item-btn {
    min-width: 150px;
    max-width: 220px;
    padding: 0 20px;
    font-size: 15px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.employee-section {
  margin: 0;
}

.month-navigation {
  background-color: #fafafa;
  padding: 16px;
  
  .week-nav-btn {
    font-size: 12px;
    text-decoration: underline;
    padding: 4px 8px;
    color: #1976d2;
    
    &:hover {
      background-color: rgba(25, 118, 210, 0.1);
    }
  }
  
  .employee-section {
    margin: 0;
  }
}

.schedule-table {
  font-size: 11px;
  
  :deep(.q-table__top),
  :deep(.q-table__bottom) {
    display: none;
  }
  
  :deep(th), :deep(td) {
    padding: 4px 2px;
    min-width: 40px;
    height: 28px;
  }
  
  .time-header {
    background-color: #f8f9fa;
    font-weight: bold;
    width: 60px;
    font-size: 10px;
  }
  
  .date-header {
    background-color: #f8f9fa;
    font-weight: bold;
    min-width: 50px;
    
    .date-cell {
      .date-number {
        font-size: 12px;
        font-weight: bold;
        line-height: 1.2;
      }
      
      .day-of-week {
        font-size: 9px;
        margin-top: 1px;
        line-height: 1;
      }
    }
  }
  
  .time-slot {
    background-color: #fafafa;
    font-weight: bold;
    font-size: 10px;
    color: #666;
  }
  
  .availability-cell {
    font-size: 10px;
    transition: background-color 0.2s;
    vertical-align: middle;
    
    .availability-text {
      font-weight: 500;
    }
    
    &:hover {
      background-color: #f0f0f0 !important;
    }
    
    &.cell-unavailable {
      background-color: #e0e0e0;
      color: #999;
      
      .availability-text {
        font-weight: normal;
      }
    }
    
    &.cell-full {
      background-color: #e3f2fd;
      color: #1565c0;
    }
    
    &.cell-available {
      background-color: white;
      color: #1565c0;
    }
    
    &.cell-reserved {
      background-color: #fff3e0;
      color: #ef6c00;
      font-weight: bold;
    }
    
    &.cell-default {
      background-color: white;
      color: #1565c0;
    }
  }
}

.text-blue {
  color: #1976d2;
}

.text-red {
  color: #d32f2f;
}

.legend {
  background-color: #fafafa;
}
</style>
