<script lang="ts">
export type HtmlFilledDataType = {
  top?: string
  left?: string
  right?: string
  width?: string
  height?: string
  /** font size with unit eg. 16px, 1mm, 1.5rem @default 16px */
  size?: string
  /** HTML content to render */
  html: string
  /** color in hex, rgb, or named color @default black */
  color?: string
  /** background color in hex, rgb, or named color to cover PDF areas */
  bgColor?: string
}

export type HtmlFilledDataArray = HtmlFilledDataType[]

interface PdfFillerProps {
  /** provide array of HTML content with positioning and styling */
  fillData: () => Promise<{
    pdf: string // URL to the PDF file
    data: HtmlFilledDataArray
  }>
  /** label for button if slot is not provided @default PDF出力 */
  label?: string
  /** file name defaults to file_<datetime_now>_0000<uuid> */
  fileName?: string
  /** replace the string to convert from returned string
   *  return false or undefined to cancel export
   */
  /** PDF export options */
  options?: {
    /** 1 - portrait , 2- landscape */
    orientation?: 1 | 2
    /** Margin can be:
     * - number: all sides equal
     * - [vMargin, hMargin]: vertical and horizontal margins
     * - [top, left, bottom, right]: individual side margins
     */
    margin?: number | [number, number] | [number, number, number, number]
    unit?: 'pt' | 'mm' | 'cm' | 'in' | 'px' | 'pc' | 'em' | 'ex'
    format?: 'a4' | 'letter' | 'government-letter' | 'legal' | 'junior-legal' | 'ledger' | 'tabloid' | 'credit-card'
    /** Page break mode for PDF generation */
    pagebreak?: {
      mode?: 'css' | 'legacy' | 'avoid-all' | Array<'css' | 'legacy' | 'avoid-all'>
      avoid?: string[] | string
      before?: string[] | string
      after?: string[] | string
    }
  }
  /** Action type for PDF **/
  action?: 'preview' | 'print',
  /** div wrapper class */
  containerClass?: string | Record<string , boolean>
}
</script>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { ref } from 'vue'
import _ from 'lodash'
import fontkit from '@pdf-lib/fontkit';

const props = withDefaults(defineProps<PdfFillerProps>(), {
  label: 'PDF出力',
  fileName: `file_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_0000${_.uniqueId()}`,
  options: () => ({}),
  actionType: 'print'
})

const isLoading = ref<boolean>(false)
const htmlFilledData = ref<HtmlFilledDataArray>([])
const htmlPdfBytes = ref<Uint8Array>()
const iframeRendererRef = ref<HTMLIFrameElement>()

const emits = defineEmits<{
  (e: 'success'): void
  (e: 'error', error: string): void
}>()

const importLib = async () => {
  try {
    // Import from CDN
    // @ts-ignore
    const pdfLib = await import('https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.esm.min.js')
    
    return pdfLib
  } catch (error) {
    console.error('Failed to import PDF-lib library:', error)
    throw error
  }
}

/**
 * Load Japanese font for PDF generation
 */
const loadJapaneseFont = async (pdfDoc: any, pdfLib: any) => {
  try {
    // Try to load the Japanese font from the public directory
    const fontUrl = '/fonts/Base64NotoSans.txt'
    const response = await fetch(fontUrl)
    if (!response.ok) {
      throw new Error('Failed to load Japanese font')
    }
    const base64Font = await response.text()
    
    // Convert base64 to Uint8Array
    const fontBytes = Uint8Array.from(atob(base64Font), c => c.charCodeAt(0))
    
    // Embed the font
    const japaneseFont = await pdfDoc.embedFont(fontBytes)
    return japaneseFont
  } catch (error) {
    console.warn('Failed to load Japanese font, falling back to Helvetica:', error)
    // Fallback to standard font if Japanese font loading fails
    return await pdfDoc.embedFont(pdfLib.StandardFonts.Helvetica)
  }
}

/**
 * @experimental not implemented
 */
const previewPdf = async () => {}

const printPdf = async () => {
  try {
    isLoading.value = true
    const pdfLib = await importLib()
    
    if (!pdfLib) {
      emits('error', 'Failed to load PDF library')
      return
    }

    if (htmlPdfBytes.value) {
      // Load PDF using the imported library
      const pdfDoc = await pdfLib.PDFDocument.load(htmlPdfBytes.value)
      pdfDoc.registerFontkit(fontkit)

      // Get the form
      const form = pdfDoc.getForm()

      // Fill fields with data from htmlFilledData or props
      // You can customize these field names and values based on your form
      try {
        // If we have HTML filled data, draw it directly on the PDF
        if (htmlFilledData.value && htmlFilledData.value.length > 0) {
          // Embed Japanese font for drawing text
          const japaneseFont = await loadJapaneseFont(pdfDoc, pdfLib)
          
          // Get the first page (assuming single page form)
          const pages = pdfDoc.getPages()
          const page = pages[0]
          const { width, height } = page.getSize()
          htmlFilledData.value.forEach((item, index) => {
            try {
              // Parse HTML content (remove tags for text rendering)
              const textContent = item.html.replace(/<[^>]*>/g, '').trim()
              
              // Convert positioning values to numbers (default to 50 if not provided)
              const top = parseFloat(item.top || '50')
              const left = parseFloat(item.left || '50')
              
              // Convert font size to number (default to 16)
              const fontSize = parseFloat(item.size || '16')
              
              // Draw background rectangle if bgColor is specified
              if (item.bgColor) {
                try {
                  let bgColor = pdfLib.rgb(1, 1, 1) // default white
                  
                  // Parse background color
                  if (item.bgColor.startsWith('#')) {
                    const hex = item.bgColor.slice(1)
                    const r = parseInt(hex.slice(0, 2), 16) / 255
                    const g = parseInt(hex.slice(2, 4), 16) / 255
                    const b = parseInt(hex.slice(4, 6), 16) / 255
                    bgColor = pdfLib.rgb(r, g, b)
                  } else if (item.bgColor.startsWith('rgb')) {
                    const rgbMatch = item.bgColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
                    if (rgbMatch) {
                      const r = parseInt(rgbMatch[1]) / 255
                      const g = parseInt(rgbMatch[2]) / 255
                      const b = parseInt(rgbMatch[3]) / 255
                      bgColor = pdfLib.rgb(r, g, b)
                    }
                  } else if (item.bgColor.toLowerCase() === 'white') {
                    bgColor = pdfLib.rgb(1, 1, 1)
                  } else if (item.bgColor.toLowerCase() === 'black') {
                    bgColor = pdfLib.rgb(0, 0, 0)
                  }
                  
                  // Calculate rectangle dimensions
                  const rectWidth = parseFloat(item.width || '100')
                  const rectHeight = parseFloat(item.height || (fontSize + 4).toString())
                  
                  // Draw background rectangle
                  page.drawRectangle({
                    x: left,
                    y: height - top - (rectHeight - 14),
                    width: rectWidth,
                    height: rectHeight,
                    color: bgColor
                  })
                } catch (bgColorError) {
                  console.warn('Invalid background color format:', item.bgColor, bgColorError)
                }
              }
              
              // Parse color (default to black)
              let color = pdfLib.rgb(0, 0, 0) // default black
              if (item.color) {
                try {
                  // Handle hex colors
                  if (item.color.startsWith('#')) {
                    const hex = item.color.slice(1)
                    const r = parseInt(hex.slice(0, 2), 16) / 255
                    const g = parseInt(hex.slice(2, 4), 16) / 255
                    const b = parseInt(hex.slice(4, 6), 16) / 255
                    color = pdfLib.rgb(r, g, b)
                  }
                  // Handle rgb colors
                  else if (item.color.startsWith('rgb')) {
                    const rgbMatch = item.color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
                    if (rgbMatch) {
                      const r = parseInt(rgbMatch[1]) / 255
                      const g = parseInt(rgbMatch[2]) / 255
                      const b = parseInt(rgbMatch[3]) / 255
                      color = pdfLib.rgb(r, g, b)
                    }
                  }
                  // Handle named colors (basic support)
                  else if (item.color.toLowerCase() === 'red') {
                    color = pdfLib.rgb(1, 0, 0)
                  } else if (item.color.toLowerCase() === 'green') {
                    color = pdfLib.rgb(0, 1, 0)
                  } else if (item.color.toLowerCase() === 'blue') {
                    color = pdfLib.rgb(0, 0, 1)
                  }
                } catch (colorError) {
                  console.warn('Invalid color format:', item.color, colorError)
                }
              }
              
              // Draw the text at the specified position
              if (textContent) {
                page.drawText(textContent, {
                  x: left,
                  y: height - top, // Convert top position to PDF coordinates
                  size: fontSize,
                  font: japaneseFont,
                  color: color
                })
              }
              
            } catch (drawError) {
              console.warn(`Could not draw text for item ${index}:`, drawError)
            }
          })
        }
      } catch (fieldError) {
        console.warn('Error accessing form fields:', fieldError)
      }

      // Flatten the form to preserve filled data
      form.flatten()

      // Save to bytes
      htmlPdfBytes.value = await pdfDoc.save()
    } else {
      // Create new PDF from HTML data or create a simple PDF
      const pdfDoc = await pdfLib.PDFDocument.create()
      pdfDoc.registerFontkit(fontkit)

      // Add a page
      const page = pdfDoc.addPage()
      const { width, height } = page.getSize()

      // Embed Japanese font
      const japaneseFont = await loadJapaneseFont(pdfDoc, pdfLib)

      // Add content to the PDF
      if (htmlFilledData.value && htmlFilledData.value.length > 0) {
        // Loop through the HTML filled data array
        htmlFilledData.value.forEach((item, index) => {
          // Parse HTML content (remove tags for text rendering)
          const textContent = item.html.replace(/<[^>]*>/g, '').trim()

          if (textContent) {
            // Convert positioning values to numbers (default to 50 if not provided)
            const top = parseFloat(item.top || '50')
            const left = parseFloat(item.left || '50')

            // Convert font size to number (default to 16)
            const fontSize = parseFloat(item.size || '16')

            // Draw background rectangle if bgColor is specified
            if (item.bgColor) {
              try {
                let bgColor = pdfLib.rgb(1, 1, 1) // default white
                
                // Parse background color
                if (item.bgColor.startsWith('#')) {
                  const hex = item.bgColor.slice(1)
                  const r = parseInt(hex.slice(0, 2), 16) / 255
                  const g = parseInt(hex.slice(2, 4), 16) / 255
                  const b = parseInt(hex.slice(4, 6), 16) / 255
                  bgColor = pdfLib.rgb(r, g, b)
                } else if (item.bgColor.startsWith('rgb')) {
                  const rgbMatch = item.bgColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
                  if (rgbMatch) {
                    const r = parseInt(rgbMatch[1]) / 255
                    const g = parseInt(rgbMatch[2]) / 255
                    const b = parseInt(rgbMatch[3]) / 255
                    bgColor = pdfLib.rgb(r, g, b)
                  }
                } else if (item.bgColor.toLowerCase() === 'white') {
                  bgColor = pdfLib.rgb(1, 1, 1)
                } else if (item.bgColor.toLowerCase() === 'black') {
                  bgColor = pdfLib.rgb(0, 0, 0)
                }
                
                // Calculate rectangle dimensions
                const rectWidth = parseFloat(item.width || '100')
                const rectHeight = parseFloat(item.height || (fontSize + 4).toString())
                
                // Draw background rectangle
                page.drawRectangle({
                  x: left,
                  y: height - top - rectHeight,
                  width: rectWidth,
                  height: rectHeight,
                  color: bgColor
                })
              } catch (bgColorError) {
                console.warn('Invalid background color format:', item.bgColor, bgColorError)
              }
            }
            
            // Parse color (default to black)
            let color = pdfLib.rgb(0, 0, 0) // default black
            if (item.color) {
              try {
                // Handle hex colors
                if (item.color.startsWith('#')) {
                  const hex = item.color.slice(1)
                  const r = parseInt(hex.slice(0, 2), 16) / 255
                  const g = parseInt(hex.slice(2, 4), 16) / 255
                  const b = parseInt(hex.slice(4, 6), 16) / 255
                  color = pdfLib.rgb(r, g, b)
                }
                // Handle rgb colors
                else if (item.color.startsWith('rgb')) {
                  const rgbMatch = item.color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
                  if (rgbMatch) {
                    const r = parseInt(rgbMatch[1]) / 255
                    const g = parseInt(rgbMatch[2]) / 255
                    const b = parseInt(rgbMatch[3]) / 255
                    color = pdfLib.rgb(r, g, b)
                  }
                }
                // Handle named colors (basic support)
                else if (item.color.toLowerCase() === 'red') {
                  color = pdfLib.rgb(1, 0, 0)
                } else if (item.color.toLowerCase() === 'green') {
                  color = pdfLib.rgb(0, 1, 0)
                } else if (item.color.toLowerCase() === 'blue') {
                  color = pdfLib.rgb(0, 0, 1)
                }
              } catch (colorError) {
                console.warn('Invalid color format:', item.color, colorError)
              }
            }
            
            // Draw the text at the specified position
            page.drawText(textContent, {
              x: left,
              y: height - top, // Convert top position to PDF coordinates
              size: fontSize,
              font: japaneseFont,
              color: color
            })
          }
        })
      } else {
        // Default content if no HTML data
        page.drawText('PDF Generated Successfully', {
          x: 50,
          y: height - 50,
          size: 20,
          font: japaneseFont,
          color: pdfLib.rgb(0, 0, 0)
        })

        page.drawText(`Generated on: ${new Date().toLocaleString()}`, {
          x: 50,
          y: height - 100,
          size: 12,
          font: japaneseFont,
          color: pdfLib.rgb(0.5, 0.5, 0.5)
        })
      }

      // Save to bytes
      htmlPdfBytes.value = await pdfDoc.save()
    }

    if (!htmlPdfBytes.value) {
      emits('error', 'PDF data is not available')
      return
    }
    const blob = new Blob([htmlPdfBytes.value], { type: 'application/pdf' })
    const blobUrl = URL.createObjectURL(blob)

    // Use the existing iframe for printing
    if (iframeRendererRef.value) {
      const iframe = iframeRendererRef.value

      // Set the PDF URL to the iframe
      iframe.src = blobUrl

      // Wait for iframe to load, then print
      iframe.onload = () => {
        try {
          // Access the iframe's window and print
          const iframeWindow = iframe.contentWindow
          if (iframeWindow) {
            iframeWindow.print()
          }
        } catch (error) {
          console.error('Error printing from iframe:', error)
          // Fallback: try to print the iframe itself
          iframe.contentWindow?.print()
        }

        // Clean up the blob URL after printing
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl)
        }, 1000)
      }
    } else {
      emits('error', 'Iframe reference not found')
    }

    emits('success')
  } catch (error) {
    console.error('Error in printPdf:', error)
    emits('error', error instanceof Error ? error.message : 'Unknown error occurred')
  } finally {
    isLoading.value = false
  }
}

const handleExport = async () => {
  if (props.fillData && _.isFunction(props.fillData)) {
    const { data, pdf } = await props.fillData()
    htmlFilledData.value = data
    // Fetch the PDF from the URL and store as Uint8Array
    if (pdf) {
      try {
        const response = await fetch(pdf)
        if (!response.ok) throw new Error('Failed to fetch PDF from URL')
        const arrayBuffer = await response.arrayBuffer()
        htmlPdfBytes.value = new Uint8Array(arrayBuffer)
      } catch (err) {
        emits('error', 'Failed to download PDF: ' + (err instanceof Error ? err.message : String(err)))
        return
      }
    } else {
      htmlPdfBytes.value = undefined
    }
  }
  switch (props.action) {
    case 'preview':
      await previewPdf()
      break
    case 'print':
    default:
      await printPdf()
      break
  }
}
</script>

<template>
  <div class="relative" style="display: flex" :class="containerClass">
    <iframe ref="iframeRendererRef" class="hidden-container"></iframe>
    <slot :onExport="handleExport" :isLoading="isLoading" :label="label" v-if="$slots.default"> </slot>
    <q-btn color="primary" unelevated @click.prevent="handleExport" class="export-btn" :loading="isLoading" v-else>
      {{ label }}
    </q-btn>
  </div>
</template>

<style scoped>
.hidden-container {
  display: none;
}

.export-btn {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.export-btn:hover {
  background-color: #45a049;
}
</style>
