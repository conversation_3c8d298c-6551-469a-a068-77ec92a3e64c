<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { storeToRefs } from 'pinia'
import usePPSQuestionTemplateStore, {
  PPSQuestion,
  PPSQuestionChoice as StorePPSQuestionChoice
} from '@/stores/pps-question-template'
import aahMessages from '@/utils/aahMessages'
import mtUtils from '@/utils/mtUtils'
import * as MtModalHeaderComponent from '@/components/MtModalHeader.vue'
import { defineAsyncComponent } from 'vue'
import MtSpinnerLoading from '@/components/MtSpinnerLoading.vue'

// Extract component from import
const MtModalHeader =
  'default' in MtModalHeaderComponent
    ? MtModalHeaderComponent.default
    : MtModalHeaderComponent

const $q = useQuasar()
const ppsStore = usePPSQuestionTemplateStore()
const { template_questions } = storeToRefs(ppsStore)

const InterviewPreviewModal = defineAsyncComponent({
  loader: () => import('./InterviewPreviewModal.vue'),
  loadingComponent: MtSpinnerLoading,
  delay: 200,
  timeout: 10000
})

// Props definition
interface Props {
  id_pps_qs_template?: number
  templateName?: string
  questions?: Array<Question>
  callBackRefresh?: Function
}

const props = withDefaults(defineProps<Props>(), {
  id_pps_qs_template: undefined,
  templateName: '',
  questions: () => [],
  callBackRefresh: () => {}
})

const emits = defineEmits(['close'])

// Types definition
interface Question {
  id_pps_question?: number
  text_question: string
  memo_short?: string
  memo_extra?: string
  flg_start: boolean
  choices: Array<Choice>
  display_order?: number
  type_qs_option?: number | null
}

interface Choice {
  id_pps_choice?: number
  text_choice: string
  text_short?: string
  id_pps_question_next: number | null
  display_order?: number
}

// Extended interface to handle the case where id_pps_question_next can be an object
interface PPSQuestionChoice
  extends Omit<StorePPSQuestionChoice, 'id_pps_question_next'> {
  id_pps_question_next:
    | number
    | null
    | {
        id_pps_question: number
        text_question: string
        memo_short: string
        flg_start: boolean
      }
}

// Template data
const templateData = ref({
  id_pps_qs_template: props.id_pps_qs_template,
  name_button: '',
  memo_explanation: '',
  id_disease_relate: null as number | null,
  flg_composition: false,
  type_qs_composition: null as number | null,
  id_pps_qs_template_pre: null as number | null,
  id_pps_qs_template_post: null as number | null,
  display_order: 1,
  id_clinic: 2 // Default value as shown in the example
})

// Data
const selectedQuestion = ref<Question | null>(null)
const questions = ref<Question[]>(props.questions || [])
const editingChoice = ref<number | null>(null)
const setSameNextQuestion = ref(false)
const loading = ref(false)

// Question option types
const questionOptionTypes = computed(() => ppsStore.qs_option_types)

// Template name display
const templateName = computed(() => {
  return (
    templateData.value.name_button ||
    props.templateName ||
    'create new template'
  )
})

// Methods
const addNewQuestion = async () => {
  // Check if we have a template ID (editing mode)
  if (!props.id_pps_qs_template) {
    // If we're creating a new template, just add the question to the local array
    const newQuestion: Question = {
      text_question: '',
      memo_short: '',
      memo_extra: '',
      flg_start: false,
      choices: [],
      display_order: questions.value.length + 1,
      type_qs_option: null // Default value
    }

    questions.value.push(newQuestion)
    selectQuestion(newQuestion)
    return
  }

  // If we're editing an existing template, create the question via API
  loading.value = true
  try {
    // Prepare question data
    const questionData = {
      text_question: '',
      memo_short: '',
      memo_extra: '',
      flg_start: false,
      display_order: questions.value.length + 1,
      id_clinic: templateData.value.id_clinic,
      type_qs_option: null // Default value
    }

    // Call the API to create the question
    const response = (await ppsStore.addQuestion(
      props.id_pps_qs_template,
      questionData
    )) as any

    // If successful, add the new question to our local array
    if (response && response.data && response.data.data) {
      const apiQuestion = response.data.data

      // Transform API question to internal format
      const newQuestion: Question = {
        id_pps_question: apiQuestion.id_pps_question,
        text_question: apiQuestion.text_question,
        memo_short: apiQuestion.memo_short || '',
        memo_extra: apiQuestion.memo_extra || '',
        flg_start: apiQuestion.flg_start || false,
        display_order: apiQuestion.display_order || 1,
        type_qs_option: apiQuestion.type_qs_option || null, // Default to 1 if not provided
        choices: []
      }

      questions.value.push(newQuestion)
      selectQuestion(newQuestion)
    }
  } catch (error) {
    console.error('Error creating new question:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)

    // Still add a local question even if API fails to maintain UX
    const newQuestion: Question = {
      text_question: '',
      memo_short: '',
      memo_extra: '',
      flg_start: false,
      choices: [],
      display_order: questions.value.length + 1,
      type_qs_option: null // Default value
    }

    questions.value.push(newQuestion)
    selectQuestion(newQuestion)
  } finally {
    loading.value = false
  }
}

const selectQuestion = (question: Question) => {
  selectedQuestion.value = question
}

const addChoice = () => {
  if (!selectedQuestion.value) return

  // Get the next question ID to apply if setSameNextQuestion is enabled
  let nextQuestionId: number | null = null
  if (setSameNextQuestion.value && selectedQuestion.value.choices.length > 0) {
    nextQuestionId =
      selectedQuestion.value.choices[0].id_pps_question_next ?? null
  }

  selectedQuestion.value.choices.push({
    text_choice: '',
    text_short: '',
    id_pps_question_next: nextQuestionId,
    display_order: selectedQuestion.value.choices.length + 1
  })
}

const moveChoiceUp = (index: number) => {
  if (!selectedQuestion.value || index <= 0) return

  const choices = selectedQuestion.value.choices
  const temp = choices[index]
  choices[index] = choices[index - 1]
  choices[index - 1] = temp

  // Update display order
  choices.forEach((choice, idx) => {
    choice.display_order = idx + 1
  })
}

const moveChoiceDown = (index: number) => {
  if (
    !selectedQuestion.value ||
    index >= selectedQuestion.value.choices.length - 1
  )
    return

  const choices = selectedQuestion.value.choices
  const temp = choices[index]
  choices[index] = choices[index + 1]
  choices[index + 1] = temp

  // Update display order
  choices.forEach((choice, idx) => {
    choice.display_order = idx + 1
  })
}

const copyChoice = (index: number) => {
  if (!selectedQuestion.value) return

  const choiceToCopy = { ...selectedQuestion.value.choices[index] }
  choiceToCopy.id_pps_choice = undefined // Remove ID for the copy
  selectedQuestion.value.choices.splice(index + 1, 0, choiceToCopy)

  // Update display order
  selectedQuestion.value.choices.forEach((choice, idx) => {
    choice.display_order = idx + 1
  })
}

const deleteChoice = async (index: number) => {
  if (!selectedQuestion.value) return

  const choice = selectedQuestion.value.choices[index]

  // If this is an existing choice (has an ID) and we're in update mode (template has ID)
  if (
    choice.id_pps_choice &&
    props.id_pps_qs_template &&
    selectedQuestion.value.id_pps_question
  ) {
    // Directly proceed with deletion without confirmation
    try {
      loading.value = true

      // Recheck that selectedQuestion is still valid when callback executes
      if (!selectedQuestion.value || !selectedQuestion.value.id_pps_question) {
        console.error('Question no longer selected or valid')
        mtUtils.autoCloseAlert(aahMessages.failed)
        loading.value = false
        return
      }

      // Delete via API
      await ppsStore.deleteChoice(
        props.id_pps_qs_template as number,
        selectedQuestion.value.id_pps_question,
        choice.id_pps_choice as number
      )

      // Remove from local state
      selectedQuestion.value.choices.splice(index, 1)

      // Update display order
      selectedQuestion.value.choices.forEach((c, idx) => {
        c.display_order = idx + 1
      })

      mtUtils.autoCloseAlert(aahMessages.success)
    } catch (error) {
      console.error('Error deleting choice:', error)
      mtUtils.autoCloseAlert(aahMessages.failed)
    } finally {
      loading.value = false
    }
  } else {
    // For new choices without an ID, just remove them locally
    selectedQuestion.value.choices.splice(index, 1)

    // Update display order
    selectedQuestion.value.choices.forEach((c, idx) => {
      c.display_order = idx + 1
    })
  }
}

const updateAllNextQuestions = (nextQuestionId?: number | null) => {
  if (!selectedQuestion.value) return

  // If nextQuestionId is provided, use it; otherwise use the first choice's next question
  let idToApply: number | null = nextQuestionId ?? null

  if (idToApply === null && selectedQuestion.value.choices.length > 0) {
    idToApply = selectedQuestion.value.choices[0].id_pps_question_next ?? null
  }

  // Apply to all choices
  selectedQuestion.value.choices.forEach((choice) => {
    choice.id_pps_question_next = idToApply
  })
}

const simulateQuestion = async () => {
  // Only allow simulation if we have a template ID
  if (props.id_pps_qs_template) {
    try {
      // Open the interview preview modal using mediumPopup
      await mtUtils.mediumPopup(InterviewPreviewModal, {
        id_pps_qs_template: props.id_pps_qs_template,
        templateName: templateName.value
      })
    } catch (error) {
      console.error('Error opening interview preview:', error)
      mtUtils.autoCloseAlert(aahMessages.failed)
    }
  } else {
    mtUtils.autoCloseAlert('Please save the template first before previewing')
  }
}

// Watch for setSameNextQuestion changes
watch(setSameNextQuestion, (val: boolean) => {
  if (
    val &&
    selectedQuestion.value &&
    selectedQuestion.value.choices.length > 0
  ) {
    // When turned on, apply the first choice's next_question to all other choices
    updateAllNextQuestions()
  }
})

// Transform internal Question format to API PPSQuestion format
const transformQuestionsForAPI = () => {
  return questions.value.map((q): PPSQuestion => {
    return {
      id_pps_question: q.id_pps_question,
      text_question: q.text_question,
      memo_short: q.memo_short || '',
      memo_extra: q.memo_extra || '',
      flg_start: q.flg_start,
      display_order: q.display_order || 1,
      id_clinic: templateData.value.id_clinic,
      type_qs_option: q.type_qs_option || null,
      choices: q.choices.map((c) => {
        return {
          id_pps_choice: c.id_pps_choice,
          text_choice: c.text_choice,
          text_short: c.text_short || '',
          id_pps_question_next: c.id_pps_question_next,
          display_order: c.display_order || 1,
          id_clinic: templateData.value.id_clinic
        }
      })
    }
  })
}

// Transform API PPSQuestion format to internal Question format
const transformAPIQuestionsToInternal = (apiQuestions: any[]) => {
  return apiQuestions.map((q): Question => {
    return {
      id_pps_question: q.id_pps_question,
      text_question: q.text_question,
      memo_short: q.memo_short,
      memo_extra: q.memo_extra,
      flg_start: q.flg_start,
      display_order: q.display_order,
      type_qs_option: q.type_qs_option || null,
      choices: q.choices.map((c: any): Choice => {
        // Extract the next question ID from either object or number
        let nextQuestionId = null

        if (
          c.id_pps_question_next !== null &&
          c.id_pps_question_next !== undefined
        ) {
          if (
            typeof c.id_pps_question_next === 'object' &&
            c.id_pps_question_next?.id_pps_question
          ) {
            nextQuestionId = c.id_pps_question_next.id_pps_question
          } else if (typeof c.id_pps_question_next === 'number') {
            nextQuestionId = c.id_pps_question_next
          }
        }

        return {
          id_pps_choice: c.id_pps_choice,
          text_choice: c.text_choice,
          text_short: c.text_short || '',
          id_pps_question_next: nextQuestionId,
          display_order: c.display_order
        }
      })
    }
  })
}

import type { PPSQuestionTemplate } from '@/stores/pps-question-template'

const saveQuestion = async () => {
  if (questions.value.length === 0) {
    return
  }

  loading.value = true

  try {
    // Prepare data for API
    const apiData = {
      id_pps_qs_template: props.id_pps_qs_template,
      id_clinic: templateData.value.id_clinic,
      questions: transformQuestionsForAPI()
    }

    await ppsStore.saveCompleteTemplate(apiData)

    mtUtils.autoCloseAlert(aahMessages.success)

    if (props.callBackRefresh) {
      props.callBackRefresh()
    }

    closeModal()
  } catch (error) {
    console.error('Error saving question template:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
  } finally {
    loading.value = false
  }
}

const deleteQuestion = async () => {
  if (!selectedQuestion.value) return

  // Ask for confirmation before deleting
  const confirmation = await mtUtils.confirm(
    aahMessages.delete_ask,
    aahMessages.delete
  )
  if (!confirmation) return

  // If this is an existing question (has an ID) and we're in update mode (template has ID)
  if (selectedQuestion.value.id_pps_question && props.id_pps_qs_template) {
    try {
      loading.value = true

      // Recheck that selectedQuestion is still valid when callback executes
      if (!selectedQuestion.value || !selectedQuestion.value.id_pps_question) {
        console.error('Question no longer selected or valid')
        mtUtils.autoCloseAlert(aahMessages.failed)
        loading.value = false
        return
      }

      // Delete via API
      await ppsStore.deleteQuestion(
        props.id_pps_qs_template as number,
        selectedQuestion.value.id_pps_question
      )

      // Remove from local state
      const index = questions.value.findIndex(
        (q) => q === selectedQuestion.value
      )
      if (index !== -1) {
        questions.value.splice(index, 1)
        selectedQuestion.value =
          questions.value.length > 0 ? questions.value[0] : null
      }

      mtUtils.autoCloseAlert(aahMessages.success)
    } catch (error) {
      console.error('Error deleting question:', error)
      mtUtils.autoCloseAlert(aahMessages.failed)
    } finally {
      loading.value = false
    }
  } else {
    // For new questions without an ID, just remove them locally
    const index = questions.value.findIndex((q) => q === selectedQuestion.value)
    if (index !== -1) {
      questions.value.splice(index, 1)
      selectedQuestion.value =
        questions.value.length > 0 ? questions.value[0] : null
    }
  }
}

const closeModal = () => {
  emits('close')
}

// Load template data if an ID is provided
const loadTemplateData = async () => {
  if (!props.id_pps_qs_template) return

  loading.value = true
  try {
    await ppsStore.fetchCompleteTemplate(props.id_pps_qs_template)

    // Get the template details
    const template = ppsStore.getSelectedTemplate

    // Fix accessing disease_relate ID
    let diseaseRelateId = null
    if (template.id_disease_relate) {
      // Use type assertion to avoid TypeScript errors
      const diseaseRelateObj = template.id_disease_relate as any
      diseaseRelateId =
        typeof diseaseRelateObj === 'object' && diseaseRelateObj.id_disease
          ? diseaseRelateObj.id_disease
          : diseaseRelateObj
    }

    // Fix accessing template reference types
    const preTemplateId =
      typeof template.id_pps_qs_template_pre === 'object' &&
      template.id_pps_qs_template_pre
        ? template.id_pps_qs_template_pre.id_pps_qs_template
        : template.id_pps_qs_template_pre

    const postTemplateId =
      typeof template.id_pps_qs_template_post === 'object' &&
      template.id_pps_qs_template_post
        ? template.id_pps_qs_template_post.id_pps_qs_template
        : template.id_pps_qs_template_post

    templateData.value = {
      id_pps_qs_template: template.id_pps_qs_template,
      name_button: template.name_button || '',
      memo_explanation: template.memo_explanation || '',
      id_disease_relate: diseaseRelateId,
      flg_composition: template.flg_composition || false,
      type_qs_composition: template.type_qs_composition || null,
      id_pps_qs_template_pre: preTemplateId,
      id_pps_qs_template_post: postTemplateId,
      display_order: template.display_order || 1,
      id_clinic: template.id_clinic || 2
    }

    // Get the questions and transform them to the internal format
    questions.value = transformAPIQuestionsToInternal(
      ppsStore.getTemplateQuestions
    )

    // Select the first question if available
    if (questions.value.length > 0) {
      selectQuestion(questions.value[0])
    }
  } catch (error) {
    console.error('Error loading template data:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
  } finally {
    loading.value = false
  }
}

// Initialize component
onMounted(async () => {
  // Load template data if an ID is provided
  if (props.id_pps_qs_template) {
    await loadTemplateData()
  } else {
    // If there are existing questions, select the first one
    if (questions.value.length > 0) {
      selectQuestion(questions.value[0])
    }
  }
  await ppsStore.fetchQsOptionTypes()
})

// Update template metadata from parent component if provided
const updateTemplateMetadata = (metadata: {
  name_button?: string
  memo_explanation?: string
  id_disease_relate?: number | null
  flg_composition?: boolean
  type_qs_composition?: number | null
}) => {
  if (metadata.name_button !== undefined) {
    templateData.value.name_button = metadata.name_button
  }

  if (metadata.memo_explanation !== undefined) {
    templateData.value.memo_explanation = metadata.memo_explanation
  }

  if (metadata.id_disease_relate !== undefined) {
    templateData.value.id_disease_relate = metadata.id_disease_relate
  }

  if (metadata.flg_composition !== undefined) {
    templateData.value.flg_composition = metadata.flg_composition
  }

  if (metadata.type_qs_composition !== undefined) {
    templateData.value.type_qs_composition = metadata.type_qs_composition
  }
}

// This function can be exposed to parent components
const setTemplateMetadata = (metadata: any) => {
  updateTemplateMetadata(metadata)
}

// Expose API for parent components
defineExpose({
  setTemplateMetadata
})
</script>

<template>
  <div class="create-update-pps-question-modal full-height column no-wrap">
    <!-- Header with template name and close button -->
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="q-mr-auto text-h6">
        {{ templateName }}
      </q-toolbar-title>
    </MtModalHeader>

    <!-- Main content area with grid layout -->
    <q-card-section class="row content">
      <!-- Left panel - Question list -->
      <div class="left-panel q-pr-md col-4">
        <div class="bg-white rounded-borders questions-panel q-pa-md">
          <div class="flex justify-between q-mb-md row">
            <q-btn
              flat
              dense
              color="primary"
              icon="add"
              label="Add Question"
              @click="addNewQuestion"
              class="q-mr-sm"
              :style="{
                color: '#004B81'
              }"
            />
            <q-btn
              flat
              dense
              color="white"
              label="Simulate question"
              @click="simulateQuestion"
              :style="{
                padding: '9px 15px',
                background: '#383838',
                borderRadius: '7px'
              }"
            />
          </div>

          <div class="q-mb-sm text-caption">
            There are {{ questions.length }} questions in this template
          </div>

          <!-- List of questions -->
          <q-scroll-area style="height: calc(100vh - 220px)">
            <q-list padding separator>
              <q-item
                v-for="(question, index) in questions"
                :key="index"
                clickable
                :active="selectedQuestion === question"
                active-class="bg-blue-1"
                @click="selectQuestion(question)"
                class="rounded-borders"
              >
                <q-item-section>
                  <q-item-label lines="3">
                    {{ question.text_question || '' }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-scroll-area>
        </div>
      </div>

      <!-- Right panel - Question editor -->
      <div
        class="right-panel bg-white rounded-borders col-8 q-pa-md"
        v-if="selectedQuestion"
      >
        <!-- Question text input -->
        <div class="q-mb-md">
          <div class="q-mb-xs text-subtitle1">Question text</div>
          <q-input
            v-model="selectedQuestion.text_question"
            dense
            autogrow
            class="full-width"
          />
        </div>

        <!-- Memo short -->
        <div class="q-mb-md row q-col-gutter-md">
          <div class="col-6">
            <div class="q-mb-xs text-subtitle1">memo Short</div>
            <q-input
              v-model="selectedQuestion.memo_short"
              dense
              class="full-width"
            />
          </div>
          <div class="flex items-center q-pl-lg col-6">
            <q-checkbox
              v-model="selectedQuestion.flg_start"
              label="flg-start"
            />
          </div>
        </div>

        <!-- memo extra -->
        <div class="q-mb-md row q-col-gutter-md">
          <div class="col-12">
            <div class="q-mb-xs text-subtitle1">memo Extra</div>
            <q-input
              type="textarea"
              v-model="selectedQuestion.memo_extra"
              dense
              class="full-width"
            />
          </div>
        </div>

        <!-- Question type option -->
        <div class="q-mb-md">
          <div class="q-mb-xs text-subtitle1">Question Type</div>
          <q-select
            v-model="selectedQuestion.type_qs_option"
            :options="questionOptionTypes"
            dense
            emit-value
            map-options
            class="full-width"
            style="max-width: 200px"
          >
            <template v-slot:selected>
              {{
                questionOptionTypes.find(function (type) {
                  return type.value === selectedQuestion?.type_qs_option
                })?.label || ''
              }}
            </template>
          </q-select>
        </div>

        <!-- Choices section -->
        <div class="q-mb-md">
          <div class="q-mb-sm text-subtitle1">Choices</div>

          <!-- Choices list -->
          <div class="q-mb-md choices-list">
            <div
              v-for="(choice, index) in selectedQuestion.choices"
              :key="index"
              class="items-center q-mb-sm choice-item row"
            >
              <!-- Choice number -->
              <div class="q-mr-sm col-auto">
                <div class="text-weight-bold">{{ index + 1 }}</div>
              </div>

              <!-- Choice text -->
              <div class="q-mr-sm col-5">
                <q-input
                  v-model="choice.text_choice"
                  dense
                  placeholder="Choice"
                />
              </div>

              <!-- text short -->
              <div class="q-mr-sm col-4">
                <q-input
                  v-model="choice.text_short"
                  dense
                  placeholder="text short"
                />
              </div>

              <!-- Next question dropdown -->
              <div class="q-mr-md col-4">
                <q-select
                  v-model="choice.id_pps_question_next"
                  :options="
                    questions
                      .filter((q) => q !== selectedQuestion)
                      .map((q) => ({
                        label: q.text_question,
                        value: q.id_pps_question
                      }))
                  "
                  dense
                  emit-value
                  map-options
                  placeholder="Next question"
                  clearable
                />
              </div>

              <!-- Action buttons -->
              <div class="col-auto row no-wrap">
                <q-btn
                  flat
                  dense
                  icon="arrow_upward"
                  @click="moveChoiceUp(index)"
                  color="grey-7"
                />
                <q-btn
                  flat
                  dense
                  icon="arrow_downward"
                  @click="moveChoiceDown(index)"
                  color="grey-7"
                />
                <q-btn
                  flat
                  dense
                  icon="content_copy"
                  @click="copyChoice(index)"
                  color="grey-7"
                />
                <q-btn
                  flat
                  dense
                  icon="close"
                  @click="deleteChoice(index)"
                  color="red"
                />
              </div>
            </div>
          </div>

          <!-- Add choice button -->
          <div class="flex justify-between">
            <q-btn
              flat
              dense
              color="primary"
              icon="add"
              label="Add Choice"
              @click="addChoice"
              class="q-mb-md"
              text-color="#004b81"
            />

            <!-- Set same next question option -->
            <div class="q-mb-md">
              <q-checkbox
                v-model="setSameNextQuestion"
                label="Set same next question at a time"
              />
            </div>
          </div>
        </div>

        <!-- Display order -->
        <div class="q-mb-md">
          <div class="q-mb-xs text-subtitle1">Display order</div>
          <q-input
            v-model.number="selectedQuestion.display_order"
            type="number"
            dense
            style="max-width: 200px"
          />
        </div>

        <!-- Action buttons -->
        <div class="justify-between q-mt-lg row">
          <q-btn
            outline
            color="negative"
            label="Delete"
            @click="deleteQuestion"
            class="q-px-md"
          />

          <q-btn
            color="primary"
            label="Save"
            @click="saveQuestion"
            class="q-px-md"
            :loading="loading"
          />
        </div>
      </div>

      <!-- Empty state when no question is selected -->
      <div
        class="right-panel flex flex-center bg-white rounded-borders col-8 q-pa-md"
        v-else
      >
        <div class="text-center">
          <q-icon name="help_outline" size="50px" color="grey-5" />
          <div class="q-mt-sm text-grey-6 text-h6">No question selected</div>
          <div class="q-mt-sm text-grey-6">
            Select a question from the list or add a new one
          </div>
          <q-btn
            flat
            color="primary"
            label="Add New Question"
            icon="add"
            @click="addNewQuestion"
            class="q-mt-md"
          />
        </div>
      </div>
    </q-card-section>
  </div>
</template>

<style lang="scss" scoped>
.create-update-pps-question-modal {
  overflow: hidden;

  .modal-header {
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #ddd;
  }

  .modal-content {
    overflow: hidden;
  }

  .left-panel,
  .right-panel {
    overflow: auto;
  }

  .questions-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .choice-item {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 8px;
    background-color: #f9f9f9;
  }
}
</style>
