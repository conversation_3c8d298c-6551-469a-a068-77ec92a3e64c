<script lang="ts" setup>
import { computed, h, nextTick, onMounted, reactive, ref, toRaw } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import mtUtils from '@/utils/mtUtils'
import useCommonStore from '@/stores/common'
import CreateLabToLabSetModal from './CreateLabToLabSetModal.vue'
import useCategoryStore from '@/stores/categories'
import useLabSetStore from '@/stores/lab-sets'
import aahMessages from '@/utils/aahMessages'
import _, { forEach, groupBy, mapValues, sortBy } from 'lodash'
import { Category, Common, GenericValueLabelType, LabSet } from '@/types/types'
import useCliCommonStore from '@/stores/cli-common'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import UpdateLabModal, { SaveCallbackProps } from './UpdateLabModal.vue'
import useLabDeviceStore from '@/stores/lab-devices'
import useRequestStore from '@/stores/requests'
import useLabStore from '@/stores/labs'
import { QSpinner } from 'quasar'
import { LabRangeItem } from './UpdateLabRange.vue'
import { dateFormat } from '@/utils/aahUtils'
import useModalStore from '@/stores/modal'
import ExtraConfirmationModal from '@/components/ExtraConfirmationModal.vue'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'
import { labSetModalHelperContents } from '@/utils/menuHelperContents'

const props = withDefaults(defineProps<{
  id_category: number,
  lab_set_type: string,
  id_device: number,
  is_only_detail: boolean
}>(), {
  id_category: undefined,
  lab_set_type: undefined,
  id_device: undefined,
  is_only_detail: false
})
const requestStore = useRequestStore()
const cliCommonStore = useCliCommonStore()
const commonStore = useCommonStore()
const categoryStore = useCategoryStore()
const labSetStore = useLabSetStore()
const labDeviceStore = useLabDeviceStore()
const labStore = useLabStore()
const modalStore = useModalStore()
const emits = defineEmits(['close'])

const categoriesLB2DefaultList = reactive<GenericValueLabelType[]>([])
const categoriesLB2List = ref<GenericValueLabelType[]>([])

const commonDeviceDefaultList = reactive<GenericValueLabelType[]>([])
const commonDeviceList = ref<GenericValueLabelType[]>([])

const labSetForm = ref({
  id_lab_set: '',
  id_category2: '',
})
const labDeviceForm = ref({
  id_lab_device: '',
  code_device: '',
})
const deleteProcess = ref(false)
const labSetList = ref<Record<string, any>>({})
const labDeviceList = ref([])
const lastKnownSaveLabSetValue = ref<Record<string, any> | null>(null)
const isEdit = ref(false)
const categoryLabCounts = ref<{ [key: string]: number } | null>(null)
const forceRenderLabItem = ref<boolean>(true)

const draggedItem = ref<number | null>(null)
const dragOverItem = ref<number | null>(null)
const dragDirection = ref<'before' | 'after' | null>(null)

const groupedByDeviceLabSetList = computed(() => {
  // First, group by device as before
  const groupedObj = Object.entries(labSetList.value).reduce((acc, [categoryId, categoryData]) => {
    // Flatten and group by id_cm_device (using '0' for null values)
    Object.entries(categoryData).forEach(([subCategoryId, items]) => {
      items.forEach(item => {
        const deviceKey = item.id_cm_device || '0';
        if (!acc[deviceKey]) acc[deviceKey] = {};
        if (!acc[deviceKey][subCategoryId]) acc[deviceKey][subCategoryId] = [];
        acc[deviceKey][subCategoryId].push(item);
      });
    });
    return acc;
  }, {});
  
  // Now convert to array format for sorting
  const groupedArray = Object.entries(groupedObj).map(([deviceId, deviceData]) => {
    return {
      id: deviceId,
      data: deviceData,
      display_order: commonStore.getCommonDeviceOptionList.find(d => d.id_common.toString() === deviceId)?.display_order || 999
    };
  });
  
  // Sort by display_order
  const sortedArray = _.sortBy(groupedArray, 'display_order');
  
  return sortedArray;
});

const getGroupedCategorizationLength = (id_category: number, id_device: number) => {
  return labSetList.value?.[props.id_category || labSetForm.value.id_category2]?.[id_category]
    ?.filter((v: any) => (v.id_cm_device || 0) == id_device)
    .length
}

const moveUp = async (labId: number, subCategoryId: number, direction: 'up' | 'down' = 'up') => {
  if (props.lab_set_type == 'lab-set') {
    const idCategory = props.id_category
    const labSetGroup = labSetList.value[idCategory][subCategoryId]
    if(!labSetGroup)
      return false
    
    const index = labSetGroup.findIndex((v) => v.id_lab === labId)
    if(index < 0) return false
    
    if(direction == 'up') {
      const temp = labSetGroup[index - 1]
      labSetGroup[index - 1] = labSetGroup[index]
      labSetGroup[index] = temp
    }
    else if(direction == 'down') {
      const temp = labSetGroup[index + 1]
      labSetGroup[index + 1] = labSetGroup[index]
      labSetGroup[index] = temp
    }

    labSetList.value[idCategory][subCategoryId] = labSetGroup
  }
}

const moveDown = async (index: number, id_category: number, id_device: number = 0) => {
  if (props.lab_set_type == 'lab-set') {
    // @note: this is not working , use MoveUp instead for lab-set
    const labSetDevice: any[] = labSetList.value?.[props.id_category || labSetForm.value.id_category2]?.[id_category]
      .filter((v: any) => (v.id_cm_device || 0) === id_device)

    // Swap the items in the array
    if (labSetDevice) {
      const temp = labSetDevice[index + 1]
      labSetDevice[index + 1] = labSetDevice[index]
      labSetDevice[index] = temp

      const displayOrderArr = labSetDevice.map((v: any, k: number) => {
        const newOrder = k + 1
        _.set(labSetList.value, [id_device, id_category, k, 'display_order'], newOrder)
        return {
          id_lab: v.id_lab,
          id_lab_set: parseInt(v.id_lab_set),
          display_order: newOrder
        }
      })
      await labSetStore.updateLabSetDisplayOrder({ lab_set: displayOrderArr })
      requestStore.setRequestPageRefresh(true)
    }
  } else if (props.lab_set_type == 'lab-device') {
    const labSetDevice = labDeviceList.value?.[id_category]

    // Swap the items in the array
    if (labSetDevice) {
      const temp = labSetDevice[index + 1]
      labSetDevice[index + 1] = labSetDevice[index]
      labSetDevice[index] = temp

      const displayOrderArr = labSetDevice.map((v, k) => ({
        id_lab: v.id_lab,
        id_lab_device: parseInt(v.id_lab_device),
        display_order: k + 1
      }))
      await labDeviceStore.updateLabDeviceDisplayOrder({ lab_device: displayOrderArr })
      requestStore.setRequestPageRefresh(true)
    }
  }
}

const areAllMarkedAsDeleted = (id_category: number, id_device: number) => {
  return labSetList.value?.[props.id_category || labSetForm.value.id_category2]?.[id_category]?.filter((v: any) => (v.id_cm_device || 0) == id_device).every((v: any) => v.isDeleted)
}

const toggleRemoveLabFromList = (id_lab: number, id_category: number, id_device: number) => {
  const labDeviceData = labSetList.value?.[props.id_category || labSetForm.value.id_category2]?.[id_category]?.find(
    (v: any) => v.id_lab === id_lab && (v.id_cm_device || 0) == id_device
  )
  if (labDeviceData)
    labDeviceData.isDeleted = !labDeviceData.isDeleted

  if (labSetList.value?.[props.id_category || labSetForm.value.id_category2]?.[id_category]?.filter((v: any) => v.isDeleted).length > 0)
    deleteProcess.value = true
}

const removeCategoryFromList = (id_device: number, id_category: number) => {
  // Find the correct device key and category
  const isAllMarkedAsDeleted = areAllMarkedAsDeleted(id_category, id_device)
  Object.keys(labSetList.value).forEach(deviceKey => {
    const categories = labSetList.value[deviceKey]
    if (categories[id_category]) {
      // Mark all labs in this category with matching device ID as deleted
      categories[id_category].forEach((lab: any) => {
        const labDeviceId = lab.id_cm_device || 0
        if (labDeviceId == id_device) {
          lab.isDeleted = !isAllMarkedAsDeleted
        }
      })
    }
  })
}

/**
 * @note not being used in lab sets
 */
const removeDeviceFromList = (id_device: number) => {
  // Iterate through all categories and mark labs with matching id_cm_device as deleted
  Object.keys(labSetList.value).forEach(deviceKey => {
    const categories = labSetList.value[deviceKey]
    Object.keys(categories).forEach(categoryKey => {
      // Mark matching labs as deleted instead of removing them
      categories[categoryKey].forEach((lab: any) => {
        const labDeviceId = lab.id_cm_device || 0
        if (labDeviceId === id_device) {
          lab.isDeleted = true
        }
      })
    })
  })
}

const openAddLabModal = async () => {
  if (!lastKnownSaveLabSetValue.value) { 
    lastKnownSaveLabSetValue.value = _.cloneDeep(labSetList.value)
  }
  let reset = false
  await mtUtils.mediumPopup(CreateLabToLabSetModal, {
    labSetList: labSetList.value,
    labSetListLastSaved: lastKnownSaveLabSetValue.value,
    doReset: (value: boolean) => {
      if (value) {
        reset = value
      }
    }
  })
  if (labSetStore.getSelectedLabSet) {
    if (reset) {
      await fetchLabSet(props.id_category || labSetForm.value.id_category2)
    }
    const labSetFromStore: Record<string, any> = (() => {
      const labItem: Record<string, any> = {}
      const selectedLabset: Record<string, any> = labSetStore.getSelectedLabSet
      Object.keys(selectedLabset).forEach((vKey) => {
        Object.keys(selectedLabset[vKey]).forEach((vCategoryKey) => {
          if (!labItem[vCategoryKey]) {
            labItem[vCategoryKey] = []
          }
          labItem[vCategoryKey].push(...selectedLabset[vKey][vCategoryKey])
        })
      })
      return labItem
    })()
    if (Object.keys(labSetList.value).length === 0) {
      labSetList.value = {
        [labSetForm.value.id_category2]: {}
      } as any
    }

    Object.keys(labSetList.value).forEach((vKey) => {
      const whitelist: any[] = []
      const labSetListItem = labSetList.value[vKey]
      Object.keys(labSetListItem).forEach((vCategoryKey) => {
        // Get existing items and mark as deleted
        const existingItems = labSetListItem[vCategoryKey].map((v: any) => ({
          ...v,
          isDeleted: true
        }))

        // Filter out items that exist in store
        const filteredExisting = existingItems.filter(
          (v: any) => !labSetFromStore[vCategoryKey]?.find(
            (vFromStore: any) => vFromStore.id_lab === v.id_lab
          ) 
        )
        // Get new items from store
        const newItems = (labSetFromStore[vCategoryKey] || []).map((vFromStore: any) => ({
          ...vFromStore,
          isDeleted: false
        }))
        // Combine filtered existing and new items
        labSetList.value[vKey][vCategoryKey] = _.uniqBy([
          ...filteredExisting,
          ...newItems
        ], 'id_lab').sort((a: any, b: any) => a.display_order - b.display_order)

        labSetList.value[vKey][vCategoryKey].forEach((v: any) => {
          whitelist.push(v.id_lab)
        })
      })
      Object.keys(labSetFromStore).forEach((vCategoryKey) => {
        if (!labSetList.value[vKey][vCategoryKey] && labSetFromStore[vCategoryKey].length > 0) {
          labSetList.value[vKey][vCategoryKey] = []
        }
        if (labSetList.value[vKey][vCategoryKey]) {
          labSetFromStore[vCategoryKey].forEach((v:any) => {
            if (!whitelist.includes(v.id_lab)) {
               labSetList.value[vKey][vCategoryKey].push(v)
            }
          })
          labSetList.value[vKey][vCategoryKey].sort((a: any, b: any) => a.display_order - b.display_order)
        }
      })
    })
    if (labSetList.value.length == 0) mtUtils.autoCloseAlert('データがありません。他のカテゴリを選択してください。')
    labSetStore.resetSelectedLabSet()
  }
}

const fetchLabSet = async (value) => {
  lastKnownSaveLabSetValue.value = null
  labSetList.value = []
  if (value) {
    await labSetStore.fetchLabSets({
      id_category2_lb2: value,
      no_pagination: true,
    })

    const bringLabData = labSetStore.getLabSets.map((v) => {
      if (v.lab) return  {...v.lab, ...v }
      else return v
    })

    const groupedLabSet = mapValues(groupBy(sortBy(bringLabData, 'display_order'), 'id_category2_lb2'),
      (value) => groupBy(sortBy(value, 'display_order'), 'id_category2_lb1')
    )

    labSetList.value = groupedLabSet
    isEdit.value = Object.keys(labSetList.value).length > 0
  }
}

const closeModal = () => {
  emits('close')
}

const getDeviceName = (id: number, lab_set_type: string) => {
  if (lab_set_type == 'lab-device') {
    const findDevice = commonStore.getCommonDeviceOptionList.find((item: Common) => item.id_common == id)
    if (findDevice) return findDevice?.name_common
    else return '機器なし'
  } else if (lab_set_type == 'lab-set') {
    const findCategory = categoryStore.getCategoriesLB2.find((item: Category) => item.id_category == id)
    if (findCategory) return findCategory?.name_category
    else return 'No category'
  } else if (lab_set_type == 'lab-outer')
    return cliCommonStore.getCliCommonOuterLabRef?.[0]?.name_cli_common
}

const getCategoryName = (id_category: number) => categoryStore.getAllCategories?.find((item: Category) => item.id_category == id_category)?.name_category

const onRowClickLabSet = async (row, id_category, id_device) => {
  if (props.is_only_detail) return
  // temporary save data
  let isSaved: boolean = false
  const category_lb1 = categoryStore.getCategoriesLB1.find((v) => v.id_category == id_category)
  const device = commonStore.getCommonDeviceOptionList.find((item: Common) => item.id_common == id_device)
  const usePlaceholder = props.lab_set_type == 'lab-set' ? !row.id_lab_set : !row.id_lab_device
  await mtUtils.mediumPopup(UpdateLabModal,
    {
      id_lab: row.id_lab,
      lab_set_type: 1,
      category_lb1, device,
      lab_set_device: row,
      usePlaceholder,
      saveCallback: (value: SaveCallbackProps) => {
        isSaved = value.isSaved
        if (isSaved) {
          const selectedItem = labSetList.value[labSetForm.value.id_category2][id_category].findIndex(
              (v: any) => v.id_lab == row.id_lab && v.id_cm_device == row.id_cm_device
          )
          if (selectedItem !== -1) {
            const toUpdateData = labSetList.value[labSetForm.value.id_category2][id_category][selectedItem]
            Object.assign(toUpdateData, value.data)
            labSetList.value[labSetForm.value.id_category2][id_category][selectedItem] = {
              ...toUpdateData,
              ...value.data
            }
          }
          nextTick(() => {
            forceRenderLabItem.value = false
          }).then(() => {
            forceRenderLabItem.value = true
          })
        }
      }
    })
}

const onRowClickLabDevice = async (row, id_category) => {
  if (props.is_only_detail) return
  const category_lb1 = categoryStore.getCategoriesLB1.find((v) => v.id_category == id_category)
  const device = commonStore.getCommonDeviceOptionList.find((item: Common) => item.id_common == props.id_device)
  await mtUtils.mediumPopup(UpdateLabModal, { id_lab: row.id_lab, lab_set_type: 2, category_lb1, device, lab_set_device: row })
  initLabDevices()
}

const save = async (noClose?: boolean) => {
  if (!labSetForm.value.id_category2) {
    mtUtils.autoCloseAlert('Category data is not selected')
    return false
  }
  if (labSetList.value.length == 0) {
    mtUtils.autoCloseAlert('Lab data is not selected')
    return false
  }

  if (deleteProcess.value) {
    modalStore.open({
      component: ExtraConfirmationModal,
      data: {
        submitFn: async () => {
          modalStore.toggleLoading(true)
          await saveFunction(noClose)
          modalStore.close()
        },
        closeFn: () => {
          return false
        }
      }
    })
  } else {
    await saveFunction(noClose)
  }

}

const saveFunction = async (noClose?: boolean) => {
  const savePlaceholderLabRanges = async (labRanges: (LabRangeItem & { id_lab_set?: number })[]) => {
    await labSetStore.fetchLabSets({ id_category2_lb2: props.id_category || labSetForm.value.id_category2, no_pagination: true })
    const labRangePayload: LabRangeItem[] = []
    labRanges.forEach(async (formData) => {
      // Find matching lab set with more specific criteria
      // This is dangerous, since we don't match it by unique identifier
      const labSet = labSetStore.getLabSets.find((v:any) => 
        v.id_lab == Number(formData.id_lab) && 
        v.id_category2_lb1 == (formData.id_category2_lb1 ?? '').toString() &&
        v.id_cm_device == Number(formData.id_cm_device) &&
        v.id_clinic == localStorage.getItem('id_clinic') &&
        (!v.isDeleted || typeof v.isDeleted !== 'boolean')
      )
      if (labSet) {
        // logic copied from updateLabRange.vue' submit()
        if (formData.date_start) formData.date_start = dateFormat(formData.date_start) + ' 00:00:00'
        if (formData.date_end) formData.date_end = dateFormat(formData.date_end) + ' 00:00:00'
        if (labSet) formData.id_lab = (labSet.id_lab ?? '').toString()
        if (localStorage.getItem('id_clinic')) formData.id_clinic = localStorage.getItem('id_clinic')
        if (!formData.id_lab_range || formData.id_lab_range == '') delete formData.id_lab_range

        let payload: LabRangeItem = {}
        Object.keys(formData).forEach((v) => {
          const formDataProp: LabRangeItem[keyof LabRangeItem] = formData[v as keyof LabRangeItem]
          if (v == 'id_category2_lb1' && props.lab_set_type == (1).toString()) {
            if (typeof (formDataProp) == 'object' && formDataProp !== null) {
              const categoryObj = formDataProp as { id_category?: string | number }
              Object.assign(payload, { [v]: categoryObj?.id_category })
            } else {
              Object.assign(payload, { [v]: formDataProp })
            }
          } else if (!['date_end', 'date_start', 'id_cm_device'].includes(v)) {
            Object.assign(payload, { [v]: formDataProp })
          }
        })

        if (props.lab_set_type== 'lab-set')
          Object.assign(payload, { id_lab_set: labSet.id_lab_set })
        if (labSet.date_start)
          Object.assign(payload, { date_start: labSet.date_start ? labSet.date_start + ' 00:00:00' : '' })
        if (labSet.date_end)
          Object.assign(payload, { date_end: labSet.date_end ? labSet.date_end + ' 00:00:00' : '' })
        Object.assign(payload, { id_lab_device: labSet.id_lab_device })

        labRangePayload.push(payload)
        delete payload.id_lab_range // added beforehand a temporary id, should delete it
      }
    })
    if (labRangePayload.length > 0) { 
      await mtUtils.promiseAllWithLoader( labRangePayload.map((payload) => [
        mtUtils.callApi('POST', 'mst/lab_range', payload)
      ]) )
    }
  }

  const labRanges: LabRangeItem[] = []
  if (isEdit.value) {
    if (props.lab_set_type == 'lab-set') {
      const labSetArr: any[] = []
      forEach(labSetList.value, (category, id_device) => {
        forEach(category, (lab_set, id_category) => {
          let displayOrder = 1
          forEach(lab_set, (l) => {
            if (!l.isDeleted) {
              labSetArr.push({
                id_lab_set: l?.id_lab_set,
                id_category2_lb2: labSetForm.value?.id_category2,
                id_category2_lb1: id_category,
                // id_cm_device: parseInt(id_device) != 0 ? parseInt(id_device) : null,
                id_cm_device: l?.id_cm_device || null,
                id_lab: l?.id_lab,
                id_clinic: localStorage.getItem('id_clinic'),
                display_order: displayOrder++
              })
              if (!l.id_lab_set && l.lab_range) {
                // only with unsave lab set
                labRanges.push( ...l.lab_range as any[])
              }
            }
          })
        })
      })
      const idCategory = props.id_category || labSetForm.value.id_category2
      const res = await labSetStore.updateLabSetByCategory(idCategory, { id_category: idCategory, lab_sets: labSetArr })
      await savePlaceholderLabRanges(labRanges)
      if (res) {
        requestStore.setRequestPageRefresh(true)
        mtUtils.autoCloseAlert(aahMessages.success)
      } else {
        mtUtils.autoCloseAlert(aahMessages.failed)
      }
    }
  } else {
    const labSetArr: any[] = []
    forEach(labSetList.value, (category, id_device) => {
      forEach(category, (lab_set, id_category) => {
        let display_order = 1
        forEach(lab_set, (l) => {
          if (!l.isDeleted || typeof l.isDeleted !== 'boolean') {
            labSetArr.push({
              id_category2_lb2: labSetForm.value?.id_category2,
              id_category2_lb1: id_category,
              // id_cm_device: parseInt(id_device) != 0 ? parseInt(id_device) : null,
              id_cm_device: l?.id_cm_device || null,
              id_lab: l?.id_lab,
              id_clinic: localStorage.getItem('id_clinic'),
              display_order: display_order++,
            })
          }
          if (!l.id_lab_set && l.lab_range) {
            // only with unsave lab set
            labRanges.push( ...l.lab_range as any[])
          }
        })
      })
    })

    const res = await (
      labSetForm.value?.id_category2 ?
        labSetStore.updateLabSetByCategory(Number(labSetForm.value?.id_category2),
          { id_category: labSetForm.value?.id_category2, lab_sets: labSetArr }) :
        labSetStore.submitLabSet({ lab_sets: labSetArr })
    )
    await savePlaceholderLabRanges(labRanges)
    if (res) {
      requestStore.setRequestPageRefresh(true)
      mtUtils.autoCloseAlert('更新しました。')
    } else {
      mtUtils.autoCloseAlert(aahMessages.failed)
    }
  }
  if(noClose) return
  closeModal()
}

const initLabSets = async () => {
  lastKnownSaveLabSetValue.value = null
  await mtUtils.promiseAllWithLoader([
    labSetStore.fetchLabSets({ id_category2_lb2: props.id_category, no_pagination: true }),
  ])

  const category_lb2 = categoryStore.getCategoriesLB2.find((v) => v.id_category == props.id_category)
  labSetForm.value.id_category2 = category_lb2?.id_category
  const bringLabData = labSetStore.getLabSets.map((v) => {
    if (v.lab) return  {...v.lab, ...v, isDeleted: false }
    else return {...v, isDeleted: false}
  })
  const groupedLabSet = mapValues(groupBy(sortBy(bringLabData, 'display_order'), 'id_category2_lb2'),
    (value) => groupBy(sortBy(value, 'display_order'), 'id_category2_lb1')
  )
  labSetList.value = groupedLabSet
}

const initLabDevices = async () => {
  const currentDevice = commonStore.getCommonDeviceOptionList.find((v) => v.id_common == props.id_device)
  labDeviceForm.id_lab_device = currentDevice.id_lab_device

  await labDeviceStore.fetchLabDevices({
    code_device: props.id_device,
  })

  const lab_devices_mapping = labDeviceStore.getLabDevices.filter((v) => v.lab).map(v => {
    const category = categoryStore.getAllCategories.find(c => c.id_category == v.lab?.id_category2_lab)
    return {...v, category}
  })

  labDeviceList.value = groupBy(sortBy(sortBy(lab_devices_mapping, 'display_order'), 'category.display_order'), 'lab.id_category2_lab')
  
}

const initLabCounts = async () => {
  await labSetStore.fetchPreparationLabSets({ no_pagination: true })
  const labSets = labSetStore.getAllLabSets
  
  // Group by id_category2_lb2 and count
  const counts: Record<string, number> = {}
  labSets.forEach(labSet => {
    const categoryId = labSet.id_category2_lb2
    if (categoryId) {
      if (!counts[categoryId]) {
        counts[categoryId] = 0
      }
      counts[categoryId]++
    }
  })
  
  categoryLabCounts.value = counts as Record<string, number>
}

const renderLabCount = (categoryId: number) => {
  if (!categoryLabCounts.value) {
    return h(QSpinner, {
      class: 'text-grey-600',
      size: '16px'
    }, `(0)`)
  }
  const count = categoryLabCounts.value?.[categoryId] || 0
  return h('span', {
    class: 'text-grey-600'
  }, `${count}`)
}

const handleDragStartLab = (labId: number, event: DragEvent) => {
  draggedItem.value = labId
  const element = (event.target as HTMLElement)?.closest('.lab-row')
  if (element) element.classList.add('dragging')

  event.dataTransfer?.setData('text/plain', `${labId}`)
  event.dataTransfer!.effectAllowed = 'move'
}

const handleDragOverLab = (labId: number, event: DragEvent) => {
  event.preventDefault()
  const element = (event.target as HTMLElement)?.closest('.lab-row')
  if (!element || draggedItem.value === null) return

  dragOverItem.value = labId

  const rect = element.getBoundingClientRect()
  const mouseY = event.clientY - rect.top
  dragDirection.value = mouseY < rect.height / 2 ? 'before' : 'after'

  element.classList.remove('drop-before', 'drop-after')
  element.classList.add(`drop-${dragDirection.value}`)
}

const handleDragLeaveLab = (event: DragEvent) => {
  const element = (event.target as HTMLElement)?.closest('.lab-row')
  if (element) {
    element.classList.remove('drop-before', 'drop-after')
    element.style.cursor = ''
  }
}

const handleDropLab = async (labId: number, subCategoryId: number, idCmDevice: number, event: DragEvent) => {
  event.preventDefault()
  document.querySelectorAll('.drop-before, .drop-after, .dragging').forEach(el => {
    el.classList.remove('drop-before', 'drop-after', 'dragging')
  })

  if (draggedItem.value === null || draggedItem.value === labId) return

  const fromLabId = draggedItem.value
  const toLabId = labId

  if(props.lab_set_type == 'lab-set') {
    const parentCat = props.id_category
    const catGroup = labSetList.value[parentCat][subCategoryId]
    if(catGroup) {
      const fromLabSet = catGroup.find((v) => v.id_lab === fromLabId)
      const toLabIndex = catGroup.findIndex((v) => v.id_lab === toLabId)
      const fromLabIndex = catGroup.findIndex((v) => v.id_lab === fromLabId)
      if(!fromLabSet) {
        return mtUtils.autoCloseAlert('Cannot drag lab set item to another group')
      }
      catGroup.splice(toLabIndex, 0, fromLabSet)
      catGroup.splice(fromLabIndex + (fromLabIndex < toLabIndex ? 0 : 1), 1)
      labSetList.value[parentCat][subCategoryId] = catGroup
    }
  }

  draggedItem.value = null
  dragOverItem.value = null
  dragDirection.value = null
}

const handleDragEndLab = () => {
  document.querySelectorAll('.dragging, .drop-before, .drop-after').forEach(el => {
    el.classList.remove('dragging', 'drop-before', 'drop-after')
    if (el instanceof HTMLElement) el.style.cursor = ''
  })

  draggedItem.value = null
  dragOverItem.value = null
  dragDirection.value = null
}

const openHelpMenu1 = async () => {
  await mtUtils.mediumPopup(MtToolTipsSmall, {
    title: labSetModalHelperContents.title,
    content: labSetModalHelperContents.content,
  })
}

onMounted(async () => {
  await mtUtils.promiseAllWithLoader([
    categoryStore.fetchCategories({ flg_for_lab: true, code_category_prefix: 'LB2_' }, 'LB2'),
    commonStore.fetchPreparationCommonList({ code_common: [4, 7] }, true)
  ])

  if (categoryStore.getCategoriesLB2.length > 0) {
    categoryStore.getCategoriesLB2.forEach((item) => {
      categoriesLB2DefaultList.push({ value: item.id_category, label: item.name_category })
    })
    categoriesLB2List.value = [...categoriesLB2DefaultList]
  }
  if (commonStore.getCommonDeviceOptionList?.length) {
    commonStore.getCommonDeviceOptionList.forEach((item: Common) => {
      commonDeviceDefaultList.push({ value: item.id_common, label: item.name_common })
    })
    commonDeviceList.value = [...commonDeviceDefaultList]
  }

  if(props.lab_set_type == 'lab-set') {
    initLabCounts()
  }

  if (props.lab_set_type == 'lab-set' && props.id_category) {
    isEdit.value = true
    initLabSets()
  } else if (props.lab_set_type == 'lab-device') {
    isEdit.value = true
    initLabDevices()
  }
})
</script>

<template>
  <q-form @submit="() => save()">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        {{ props.lab_set_type == 'lab-set' ? '手入力検査' : (props.lab_set_type == 'lab-device' ? '検査機器 表示項目管理' : '') }}
      </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="content">
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-lg-6 col-md-6 col-sm-6">
          <MtFilterSelect
            v-if="props.lab_set_type == 'lab-set'"
            v-model:selecting="labSetForm.id_category2"
            v-model:options="categoriesLB2List"
            :options-default="categoriesLB2DefaultList"
            label="手入力検査"
            class="q-mr-sm selection-field"
            outlined
            @update:selecting="fetchLabSet"
            @clear="fetchLabSet"
            :disable="!!props.id_category"
            custom-option
          >
            <template #option="{slotProps}">
              <q-item v-bind="slotProps?.itemProps" clickable>
                <div class="row q-pa-sm full-width justify-between items-center">
                  <span>{{ slotProps?.opt?.label }}</span>
                  <component :is="renderLabCount(slotProps?.opt?.value)" />
                </div>
              </q-item>
            </template>
          </MtFilterSelect>
          <MtFilterSelect
            v-if="props.lab_set_type == 'lab-device'"
            v-model:options="commonDeviceList"
            :options-default="commonDeviceDefaultList"
            label="院内検査機器"
            class="q-mr-sm selection-field"
            outlined
            v-model:selecting="props.id_device"
            disable
          />
        </div>
        <div v-if="props.lab_set_type == 'lab-set' && !props.is_only_detail" class="col-lg-6 col-md-6 col-sm-6">
          <div class="flex justify-end">
            <q-btn
              class="q-ml-sm"
              color="grey-800"
              text-color="white"
              unelevated
              @click="openAddLabModal()"
            >
              <q-icon name="add" size="20px" />
              検査項目
            </q-btn>
          </div>
        </div>
        <div>手入力検査の管理ページです。 
          <q-btn dense flat round @click="openHelpMenu1" class="q-mx-sm">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </div>

        <template v-if="props.lab_set_type == 'lab-set'">
          <div 
            class="col-lg-12 col-md-12 col-sm-12" 
            v-for="deviceGroup in groupedByDeviceLabSetList"
            :key="deviceGroup.id"
          >
            <div>
              <div class="bg-grey q-pa-sm flex justify-between items-center">
                {{ getDeviceName(deviceGroup.id, 'lab-device') }}
                <q-btn @click="removeDeviceFromList(deviceGroup.id)" flat dense rounded v-if="false">
                  <q-icon name="close" color="white" size="5" />
                </q-btn>
              </div>
              <div v-if="deviceGroup.data" v-for="(lab_set, id_category) in deviceGroup.data" :key="id_category">
                <div class="bg-grey-200 q-pa-sm flex justify-between items-center">
                  {{ getCategoryName(id_category) }}
                  <q-btn v-if="!props.is_only_detail" @click="removeCategoryFromList(deviceGroup.id, id_category)" flat dense rounded>
                    <q-icon :name="areAllMarkedAsDeleted(id_category, deviceGroup.id) ? 'remove' : 'close'" color="black" size="5" />
                  </q-btn>
                </div>
                <template 
                  v-if="lab_set && forceRenderLabItem"
                  v-for="(lab, key) in lab_set"
                  :key="lab.id_lab"
                >
                  <template v-if="lab.flg_above_blank_row">
                    <div class="bg-grey q-py-md"></div>
                  </template>
                  <div
                    class="on-hover-grey q-px-sm"
                    :class="lab?.isDeleted ? 'deleted' : '' + props.is_only_detail ? ' ' : ' cursor-pointer'"
                    @click="onRowClickLabSet(lab, id_category, deviceGroup.id)"
                  >
                    <div class="row items-center lab-row"
                      draggable="true"
                      @dragstart="(e) => handleDragStartLab(lab.id_lab, e)"
                      @dragover="(e) => handleDragOverLab(lab.id_lab, e)"
                      @dragleave="handleDragLeaveLab"
                      @drop="(e) => handleDropLab(lab.id_lab, id_category, lab.id_cm_device || 0, e)"
                      @dragend="handleDragEndLab"
                    >
                      <div class="col name-col flex items-center gap-2" :class="lab?.flg_indent ? 'q-ml-md' : ''">
                        <div class="index">{{`${(key + 1).toString()}  `}}</div>&nbsp;{{ lab?.name_lab }}
                      </div>
                      <div class="col">
                        <div class="text-wrap">
                          {{ lab?.name_lab_en?.replace('%',' ') }}
                        </div>
                      </div>
                      <div class="col">
                        {{ lab?.memo_lab }}
                      </div>
                      <div class="col-2" v-if="!props.is_only_detail">
                        <div class="flex justify-end">
                          <q-btn v-if="key != 0" @click.stop="moveUp(lab?.id_lab, id_category)" flat dense rounded>
                            <q-icon name="arrow_upward" size="5" />
                          </q-btn>
                          <q-btn v-if="key != getGroupedCategorizationLength(id_category, deviceGroup.id) - 1" @click.stop="moveUp(lab?.id_lab, id_category, 'down')" flat dense rounded>
                            <q-icon name="arrow_downward" size="5" />
                          </q-btn>
                          <q-btn @click.stop="toggleRemoveLabFromList(lab?.id_lab, id_category, deviceGroup.id)" flat dense rounded>
                            <q-icon name="delete" size="5" />
                          </q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </template>
        <template v-if="props.lab_set_type == 'lab-device'">
          <div class="col-lg-12 col-md-12 col-sm-12" v-for="(lab_set, id_category) in labDeviceList">
          <div>
            <div class="bg-grey-200 q-pa-sm flex justify-between items-center">
              {{ getCategoryName(id_category) }}
            </div>
            <template 
              v-if="lab_set"
              v-for="(lab, key) in lab_set"
            >
              <template v-if="lab?.flg_above_blank_row">
                <div class="bg-grey-200 q-py-sm"></div>
              </template>
              <div @click="onRowClickLabDevice(lab, id_category)" class="on-hover-grey q-pa-sm" :class="props.is_only_detail ? '' : 'cursor-pointer'">
                <div class="row items-center">
                  <div class="col" :class="lab?.flg_indent ? 'q-ml-md' : ''">
                    {{ lab?.lab?.name_lab }}
                  </div>
                  <div class="col">
                    <div class="text-wrap">
                      {{ lab?.lab?.name_lab_en?.replace('%',' ') }}
                    </div>
                  </div>
                  <div class="col">
                    {{ lab?.memo_lab ? lab?.memo_lab : lab?.lab?.memo_lab }}
                  </div>
                  <div class="col-1" v-if="!props.is_only_detail">
                    <div class="flex justify-end">
                      <q-btn v-if="key != 0" @click.stop="moveUp(key, id_category)" flat dense rounded>
                        <q-icon name="arrow_upward" size="5" />
                      </q-btn>
                      <q-btn v-if="key != lab_set.length - 1" @click.stop="moveDown(key, id_category)" flat dense rounded>
                        <q-icon name="arrow_downward" size="5" />
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        </template>

      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white q-pt-md">
      <div class="">
        <div class="text-center modal-btn">
          <q-btn
            class="bg-grey-100 text-grey-800"
            outline
            @click="closeModal()"
          >
            <span>キャンセル</span>
          </q-btn>
          <q-btn
            v-if="props.lab_set_type == 'lab-set' && !props.is_only_detail"
            class="q-ml-md"
            color="primary"
            tabindex="20"
            type="submit"
            unelevated
          >
            <span>保存</span>
          </q-btn>
        </div>
        <div></div>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.c-grid {
  display: grid;
  grid-template-columns: 120px auto 120px;
  flex-direction: column;
}

.first-item {
  max-width: 100px;
}

.q-item {
  min-height: auto !important;
  padding: 2px 0 !important;
}
.on-hover-grey {
  &:hover {
    background-color: $grey-300
  }
  &.deleted {
    background-color: rgb(252, 131, 131);
  }
  margin-top: 8px;
  &:first-child {
    margin-top: 0;
  }
}

.lab-row {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s ease;
  padding: 4px 10px;
  cursor: pointer;
  .name-col {
    .index {
      border-radius: 45px;
      background: #d5e1e5;
      padding: 3px 9px;
      color: #006483;
      text-align: center;
    }
  }
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.dragging {
    opacity: 0.5;
    border: 2px dashed #ccc;
  }
  
  &.drop-before {
    border-top: 3px solid #1976d2;
  }
  
  &.drop-after {
    border-bottom: 3px solid #1976d2;
  }
  
  &.expanded {
    background-color: #f5f5f5;
  }
}
</style>
