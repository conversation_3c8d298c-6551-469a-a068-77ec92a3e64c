<template>
    <div class="dynamic-form-builder">
      <div class="q-mb-md form-builder-header">
        <h5 class="q-my-sm">予約に関する追加質問</h5>
        <q-btn color="primary" label="質問" icon="add" @click="addField" />
      </div>
  
      <div v-if="formFields.length === 0" class="text-center q-pa-md">
        <q-icon name="info" size="md" color="grey" />
        <p class="text-grey">No questions added yet. Click "Add Field" to create your first question.</p>
      </div>
  
      <q-list bordered separator class="rounded-borders" v-else>
        <q-item v-for="(field, index) in formFields" :key="index">
          <q-item-section>
            <div class="items-center row">
              <div class="col-12">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-6">
                    <q-input v-model="field.label" label="質問" dense outlined @update:model-value="updateField(index)" />
                  </div>
                  <div class="col-12 col-md-6">
                    <q-select
                      v-model="field.type"
                      :options="fieldTypes"
                      label="回答方式"
                      dense
                      outlined
                      emit-value
                      map-options
                      @update:model-value="updateFieldType(index, field.type)"
                    />
                  </div>
                </div>
  
                <div class="q-mt-sm row q-col-gutter-md">
                  <div class="col-12 col-md-12">
                    <q-checkbox v-model="field.required" label="必須" @update:model-value="updateField(index)" />
                  </div>
                </div>
  
                <!-- Options for select, radio, checkbox types -->
                <div v-if="['select', 'radio', 'checkbox'].includes(field.type)" class="q-mt-md">
                  <div class="q-mb-sm text-subtitle2">選択肢</div>
                  <div v-for="(option, optIndex) in field.options || []" :key="optIndex" class="q-mb-sm row q-col-gutter-sm">
                    <div class="col-5">
                      <q-input v-model="option.value" dense outlined label="値" @update:model-value="updateField(index)" />
                    </div>
                    <div class="col-5">
                      <q-input v-model="option.label" dense outlined label="オーナーが見る選択肢" @update:model-value="updateField(index)" />
                    </div>
                    <div class="col-2">
                      <q-btn flat color="negative" icon="delete" @click="removeOption(index, optIndex)" />
                    </div>
                  </div>
                  <q-btn flat color="primary" label="選択肢" icon="add" class="q-mt-sm" @click="addOption(index)" />
                </div>
              </div>
            </div>
          </q-item-section>
          <q-item-section side>
            <div class="row">
              <q-btn flat round color="grey" icon="arrow_upward" @click="moveField(index, -1)" :disable="index === 0" />
              <q-btn flat round color="grey" icon="arrow_downward" @click="moveField(index, 1)" :disable="index === formFields.length - 1" />
              <q-btn flat round color="negative" icon="delete" @click="removeField(index)" />
            </div>
          </q-item-section>
        </q-item>
      </q-list>
  
      <q-dialog v-model="showAddFieldDialog">
        <q-card style="min-width: 350px">
          <q-card-section>
            <div class="text-h6">質問</div>
          </q-card-section>
  
          <q-card-section>
            <q-select
              v-model="newField.type"
              :options="fieldTypes"
              label="質問方式"
              outlined
              emit-value
              map-options
              @update:model-value="prepareNewField"
            />
            <q-input v-model="newField.label" label="質問" outlined class="q-mt-md" />
            <q-checkbox v-model="newField.required" label="必須" class="q-mt-md" />
          </q-card-section>
  
          <q-card-actions align="right">
            <q-btn label="キャンセル" color="primary" flat v-close-popup />
            <q-btn label="追加" color="primary" @click="confirmAddField" :disable="!newField.label" v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>
  
      <!-- Preview Modal -->
      <q-dialog v-model="showPreview">
        <q-card style="width: 700px; max-width: 95vw">
          <q-card-section>
            <div class="text-h6">Form Preview</div>
          </q-card-section>
          <q-card-section class="q-pt-none">
            <DynamicFormRenderer :form-fields="formFields" />
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Close" color="primary" v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>
  
      <div class="justify-end q-mt-md row">
        <q-btn color="secondary" label="プレビュー" @click="previewForm" class="q-mr-sm" />
      </div>
    </div>
  </template>
  
  <script lang="ts">
  import { defineComponent, ref, computed, watch } from 'vue';
  import DynamicFormRenderer from './DynamicFormRenderer.vue';
  import { FormField, FormFieldType, FormFieldOption } from '../../types/formTypes';
  
  // Function to generate UUID
  function generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  export default defineComponent({
    name: 'DynamicFormBuilder',
    components: {
      DynamicFormRenderer
    },
    props: {
      modelValue: {
        type: Array as () => FormField[],
        default: () => []
      }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
      const formFields = ref<FormField[]>([]);
      const showAddFieldDialog = ref(false);
      const showPreview = ref(false);
      
      const newField = ref<FormField>({
        key: generateUUID(),
        type: 'text' as FormFieldType,
        label: '',
        required: false,
        options: []
      });
  
      const fieldTypes = [
        { label: '短文回答（自由記述）', value: 'text' },
        { label: '長文回答（自由記述)', value: 'textarea' },
        { label: '数値入力', value: 'number' },
        { label: '日付', value: 'date' },
        { label: '時刻', value: 'time' },
        { label: '選択肢', value: 'select' },
        { label: '単一選択', value: 'radio' },
        { label: 'チェックボックス（複数選択）', value: 'checkbox' }
      ];
  
      // Initialize formFields from props
      watch(() => props.modelValue, (newVal) => {
        if (newVal && Array.isArray(newVal)) {
          formFields.value = JSON.parse(JSON.stringify(newVal));
        }
      }, { immediate: true });
  
      const updateField = (index: number) => {
        const updatedFields = [...formFields.value];
        emit('update:modelValue', updatedFields);
      };
  
      const updateFieldType = (index: number, newType: FormFieldType) => {
        const field = formFields.value[index];
        
        // Initialize options array if switching to a type that needs options
        if (['select', 'radio', 'checkbox'].includes(newType)) {
          if (!field.options || field.options.length === 0) {
            field.options = [{ value: 'option1', label: 'Option 1' }];
          }
        }
        
        updateField(index);
      };
  
      const addField = () => {
        newField.value = {
          key: generateUUID(),
          type: 'text',
          label: '',
          required: false,
          options: []
        };
        showAddFieldDialog.value = true;
      };
  
      const prepareNewField = (type: FormFieldType) => {
        if (['select', 'radio', 'checkbox'].includes(type)) {
          if (!newField.value.options || newField.value.options.length === 0) {
            newField.value.options = [{ value: 'option1', label: 'Option 1' }];
          }
        }
      };
  
      const confirmAddField = () => {
        const fieldToAdd = JSON.parse(JSON.stringify(newField.value));
        
        // Ensure the field has a key
        if (!fieldToAdd.key || fieldToAdd.key.trim() === '') {
          fieldToAdd.key = generateUUID();
        }
        
        formFields.value.push(fieldToAdd);
        emit('update:modelValue', formFields.value);
        showAddFieldDialog.value = false;
      };
  
      const removeField = (index: number) => {
        formFields.value.splice(index, 1);
        emit('update:modelValue', formFields.value);
      };
  
      const moveField = (index: number, direction: number) => {
        const newIndex = index + direction;
        if (newIndex >= 0 && newIndex < formFields.value.length) {
          const fields = [...formFields.value];
          const temp = fields[index];
          fields[index] = fields[newIndex];
          fields[newIndex] = temp;
          formFields.value = fields;
          emit('update:modelValue', formFields.value);
        }
      };
  
      const addOption = (fieldIndex: number) => {
        const field = formFields.value[fieldIndex];
        if (!field.options) {
          field.options = [];
        }
        
        field.options.push({
          value: `option${field.options.length + 1}`,
          label: `Option ${field.options.length + 1}`
        });
        updateField(fieldIndex);
      };
  
      const removeOption = (fieldIndex: number, optionIndex: number) => {
        const field = formFields.value[fieldIndex];
        if (field.options && field.options.length > 0) {
          field.options.splice(optionIndex, 1);
          updateField(fieldIndex);
        }
      };
  
      const previewForm = () => {
        showPreview.value = true;
      };
  
      return {
        formFields,
        fieldTypes,
        newField,
        showAddFieldDialog,
        showPreview,
        updateField,
        updateFieldType,
        addField,
        prepareNewField,
        confirmAddField,
        removeField,
        moveField,
        addOption,
        removeOption,
        previewForm
      };
    }
  });
  </script>
  
  <style scoped>
  .dynamic-form-builder {
    max-width: 100%;
  }
  </style> 