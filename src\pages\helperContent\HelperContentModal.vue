<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue';

const emits = defineEmits(['close'])
const closeModal = () => { emits('close') }
</script>

<template>
  <q-form>
    <MtModalHeader @close-modal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        <span>Header Content</span>
      </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="content">
      <div class="row">
        <div class="col-12">
          <h1>Helper Content</h1>
          <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis! Lorem ipsum dolor sit amet consectetur adipisicing elit. Sequi molestiae nam aliquam? Fuga tempora laborum hic eum ut nesciunt culpa, nostrum reiciendis explicabo voluptatem, alias aperiam voluptatibus obcaecati ipsam debitis!</p>
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn color="primary" label="閉じる" @click="closeModal" />
      </div>
    </q-card-section>
    
  </q-form>
</template>
