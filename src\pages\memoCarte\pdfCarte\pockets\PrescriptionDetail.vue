<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// Utilities
import mtUtils from '@/utils/mtUtils'
import { event_bus } from '@/utils/eventBus'
import { decimalToFraction, roundZeroAfterPoint } from '@/utils/aahUtils'

// Stores
import useEmployeeStore from '@/stores/employees'
import useDoseStore from '@/stores/dose-frequencies'
import { getTypeDosageUI } from '@/pages/request/prescription/prescriptionUtils'
import useCommonStore from '@/stores/common'

// Store Instances
const employeeStore = useEmployeeStore()
const doseStore = useDoseStore()

const props = defineProps({
  prescriptionDetail: Object,
  employeeWofLabMap: Map<number, string>
})

const employeeName = (idEmployee: number) => {
  const { employeeWofLabMap } = props
  if(employeeWofLabMap.has(idEmployee)) {
    return employeeWofLabMap.get(idEmployee)
  }
  return ''
}

const getISU = (value, list) => {
  return list?.find((is: any) => is.id_item_service_unit == value)
}

const calculatedTotalDosage = (prescriptionDetail: any) => {
  const tempDosage: any = useDoseStore().getAllDoses.find(
    (dose: any) => dose.value == prescriptionDetail.id_dosage_frequency
  )


  if (tempDosage && tempDosage.quantity_dose)
    return Math.ceil(
      roundZeroAfterPoint(prescriptionDetail.total_days_dose) *
      Number(tempDosage.quantity_dose ?? 1) ?? 1
    )
  return 1
}


function customRound(number) {
  // Separate the integer and decimal parts
  const integerPart = Math.floor(number)
  const decimalPart = number - integerPart

  // Check if decimal part is 0.85 or higher
  if (decimalPart >= 0.90) {
    return Math.ceil(number) // Round up to the next integer
  } else if (decimalPart <= 0.10) {
    return Math.floor(number) // Round down to the previous integer
  } else {
    // Otherwise, round to the nearest tenth
    return Math.round(number * 10) / 10
  }
}


const getFraction = (prescriptionDetail, valDosageSelected) => {
  const totalPill = Number(calculatedTotalDosage(prescriptionDetail)) * Number(valDosageSelected)
  if (customRound(totalPill) - parseInt(customRound(totalPill)) > 0) {

    if (parseInt(Number(totalPill)) > 0) {
      return ` +  ${decimalToFraction(totalPill - parseInt(totalPill))}`
    }

    return `${decimalToFraction(totalPill - parseInt(totalPill))}`
  }
  return ''
}

function getUnitFromAssort(itemService: any, id_item_service_unit) {

  if (!itemService) return

  const itemServiceUnit = itemService.item_service_unit_list.find((isu) => isu.id_service_item_unit = id_item_service_unit)
  if (!itemServiceUnit) return

  return (
    useCommonStore().getCommonUnitOptionList.find(
      (unit) => unit.id_common == itemServiceUnit.id_common
    )?.name_common ?? ''
  )
}


const getWholeAmount = (item, PA) => {
  let temp_dosage = ''
  if (PA.val_dosage_decided && Number(PA.val_dosage_decided) > 0) {
    if (parseInt(Number(calculatedTotalDosage(item) * PA.val_dosage_decided)) > 0) {
      temp_dosage += customRound(Number(calculatedTotalDosage(item) * Number(PA.val_dosage_decided)))
    }
  }

  return Number(temp_dosage).toFixed(1)
}

</script>

<template>
    <div>
      <div v-if="prescriptionDetail.type_detail == 2"
        class="body2 regular ellipsis q-pb-xs">
        <div>
          <span class="q-mr-sm">
            {{ prescriptionDetail.name_prescription_display }}
          </span>
        </div>
        <div v-for="assort in prescriptionDetail.prescription_detail_assort_list" class="flex q-mr-xs caption2 text-grey-700">
          {{ getISU(assort.id_item_service_unit,
            prescriptionDetail.item_service?.item_service_unit_list)?.name_service_item_unit }}
          <div class="q-ml-md">{{ roundZeroAfterPoint(assort.val_dosage_decided) }} 個</div>
        </div>
      </div>
      <!--Normal PD Case-->
      <div
        v-else-if="prescriptionDetail.flg_group_title != '1'"
        class="body2 regular text-grey-900"
      >
        <div class="ellipsis prescription-name">
          <span class="text-weight-bold text-grey-900">{{ prescriptionDetail.name_prescription_display }}</span>
        </div>
        <div class="ellipsis widthToTruncate">
          <span>
            <small>{{ prescriptionDetail.name_category1 }}</small>
          </span>
          <span>
            <small>{{ prescriptionDetail.name_category2 }}</small>
          </span>
        </div>
        <div class="ellipsis widthToTruncate body2 regular q-my-xs">
          <span class="q-mr-sm">
            {{
              doseStore?.getAllDoses?.find(
                (dose) => prescriptionDetail?.id_dosage_frequency == dose?.value
              )?.label
            }}
          </span>
          <small> {{ prescriptionDetail.date_start + ' ~ ' }} </small>
          <small v-if="prescriptionDetail.total_days_dose">
            ( {{ roundZeroAfterPoint(prescriptionDetail.total_days_dose) }} {{ getTypeDosageUI(prescriptionDetail) }} )
          </small>
        </div>
        <div v-for="assort in prescriptionDetail.prescription_detail_assort_list"
             v-if="prescriptionDetail.name_medicine_format == '錠'"
             class="flex q-mr-xs">
          {{ getISU(assort.id_item_service_unit,
            prescriptionDetail.item_service?.item_service_unit_list)?.name_service_item_unit
          }}
          <div class="q-ml-sm">{{ decimalToFraction(assort.val_dosage_decided) }} 錠</div>
          <small v-if="prescriptionDetail.name_medicine_format  == '錠' " class="q-ml-md text-grey-700">総量:</small>
          {{ parseInt(customRound(Number(calculatedTotalDosage(prescriptionDetail)) * Number(assort.val_dosage_decided))) > 0 ?
          `${parseInt(customRound(Number(calculatedTotalDosage(prescriptionDetail)) * Number(assort.val_dosage_decided)))} ` : ''
          }} {{ getFraction(prescriptionDetail, assort.val_dosage_decided) }} 錠 
        </div>
        <div v-for="assort in prescriptionDetail.prescription_detail_assort_list"
             v-if="props.prescriptionDetail.name_medicine_format != '錠'"
             :key="assort.id_prescription_detail_assort"
             class="ellipsis widthToTruncate body2 regular"
        >
          <small>
            {{ roundZeroAfterPoint(assort.val_dosage_decided) }}
            {{ getUnitFromAssort(prescriptionDetail.item_service, assort.id_item_service_unit) }}
          </small>
          <small class="q-mx-md">
            {{
              useDoseStore()?.getAllDoses?.find(
                (dose) => prescriptionDetail?.id_dosage_frequency == dose?.value
              )?.short_name
            }}
          </small>
          <small v-if="prescriptionDetail.type_detail == 1" class="q-ml-md">
            総量：{{ getWholeAmount(prescriptionDetail, assort) }}
            {{ getUnitFromAssort(prescriptionDetail.item_service, assort.id_item_service_unit) }}
          </small>
        </div>
        <div v-if="prescriptionDetail.id_employee_staff && employeeWofLabMap.has(prescriptionDetail.id_employee_staff)" class="q-mt-xs">
          <small class="pocket-staff-label">
            {{ employeeName(prescriptionDetail.id_employee_staff) }}
          </small>
        </div>
      </div>
      <div v-else class="prescription_group" :class="prescriptionDetail.flg_cancel ? 'cancel-notification-box' : ''">
        <span>
          {{ props.prescriptionDetail.name_prescription_display }}
        </span>
      </div>
    </div>
</template>
<style lang="scss" scoped>
.item_divier_large_prescription {
  cursor: pointer;

  &:hover {
    background-color: rgba(213, 239, 255, 0.9);
  }

  &:active {
    background-color: rgba(236, 248, 255, 0.8);
  }

  &:focus {
    background-color: rgba(236, 248, 255, 0.9);
  }

  &.no-left-border {
    border-left: 1px dotted rgb(227 227 227);
  }

  border-radius: 10px;
  background-color: rgba(213, 239, 255, 0.7);
  padding: 8px 10px 7px;

  .prescription-name {
    max-width: 30vw;

    @media screen and (max-width: 1440px) {
      max-width: 22vw;
    }

    &.left {
      @media screen and (max-width: 1440px) {
        max-width: 25vw;
      }
    }
  }
}

.widthToTruncate {
  // max-width: 270px;
  max-width: 30vw;

  @media screen and (max-width: 1100px) {
    max-width: 30vw;
  }

  @media screen and (max-width: 1040px) {
    // For IPAD
    max-width: 25vw;
  }

  @media screen and (max-width: 900px) {
    // For IPAD
    max-width: 22vw;
  }

  @media screen and (max-width: 430px) {
    // For Phone
    max-width: 32vw;
  }

  &.left {
    @media screen and (max-width: 1440px) {
      max-width: 25vw;
    }
  }
}

.prescription-header-btn {
  background-color: #e9efff;
  text-align: center;
  padding: 0px 10px;
}

.prescription-header-btn-span {
  height: 20px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
}

// .prescription_group {
//   background-color: rgba(227, 241, 250, 0.7);
// }
.divider {
  width: 100%;
  padding: 5px 0 5px;
  background: $grey-100;
}
</style>
