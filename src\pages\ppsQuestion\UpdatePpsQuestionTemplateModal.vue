<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { api } from '@/boot/axios'
import { useQuasar } from 'quasar'
import aahMessages from '@/utils/aahMessages'
import mtUtils from '@/utils/mtUtils'
import usePPSQuestionTemplateStore from '@/stores/pps-question-template'
import {
  PPSQuestionTemplate,
  DiseaseRelate,
  TemplateReference,
  extractTemplateId
} from '@/stores/pps-question-template'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputNumber from '@/components/form/MtFormInputNumber.vue'

const $q = useQuasar()
const ppsStore = usePPSQuestionTemplateStore()

interface Props {
  id_pps_qs_template?: number
  data?: Partial<PPSQuestionTemplate>
  callBackRefresh?: Function
}

const props = withDefaults(defineProps<Props>(), {
  id_pps_qs_template: -1,
  data: undefined,
  callBackRefresh: () => {}
})

const emits = defineEmits(['close'])

// Disease options
const diseaseOptions = ref<
  Array<{
    label: string
    value: number
    name_disease: string
    name_disease_en: string
    id_disease: number
  }>
>([])

// Form data - using a more specific type with any to handle API changes
const formData = ref<{
  id_pps_qs_template?: number
  name_button: string
  memo_explanation: string
  id_disease_relate: any // Using any to handle both object and number
  disease_relate?: DiseaseRelate
  flg_composition: boolean
  type_qs_composition: number
  id_pps_qs_template_pre: number | null
  id_pps_qs_template_post: number | null
  flg_active: boolean
  flg_pps_available: boolean
  display_order: string | number // Changed from number | null to string | number
}>({
  id_pps_qs_template: undefined,
  name_button: '',
  memo_explanation: '',
  id_disease_relate: undefined,
  flg_composition: false,
  type_qs_composition: 1,
  id_pps_qs_template_pre: null,
  id_pps_qs_template_post: null,
  flg_active: false,
  flg_pps_available: false,
  display_order: '' // Changed from null to empty string
})

// Options for selects
const preTemplateOptions = ref<any[]>([])
const postTemplateOptions = ref<any[]>([])

// Use the composition types from the store
const qsCompositionOptions = computed(() => ppsStore.composition_types)

// Loading state
const loading = ref(false)
const formRef = ref(null)

// Selected disease handling
const selectedDisease = ref<number | undefined>(undefined)

// Methods
const closeModal = (refresh = false) => {
  if (refresh && props.callBackRefresh) {
    props.callBackRefresh()
  }
  emits('close')
}

const fetchDiseases = async () => {
  try {
    const response = await api.get('/mst/diseases', {
      params: {
        id_clinic: 2,
        clinic_list: '[]'
      }
    })
    if (response?.data?.data) {
      diseaseOptions.value = response.data.data.map((disease: any) => ({
        label: disease.name_disease,
        value: disease.id_disease,
        name_disease: disease.name_disease,
        name_disease_en: disease.name_disease_en || disease.name_disease,
        id_disease: disease.id_disease
      }))
    }
  } catch (error) {
    console.error('Error fetching diseases:', error)
  }
}

// Filter diseases when typing in the search field
const diseaseFilterFn = (
  val: string,
  update: (callback: () => void) => void
) => {
  if (val === '') {
    update(() => {
      // No filtering for empty search
    })
    return
  }

  update(() => {
    const needle = val.toLowerCase()
    diseaseOptions.value = diseaseOptions.value.filter(
      (v) => v.label.toLowerCase().indexOf(needle) > -1
    )
  })
}

// Watch for changes in selectedDisease and update formData
watch(selectedDisease, (newVal) => {
  if (newVal) {
    const disease = diseaseOptions.value.find((d) => d.id_disease === newVal)
    if (disease) {
      // Store as object in id_disease_relate for backward compatibility
      formData.value.id_disease_relate = {
        id_disease: disease.id_disease,
        name_disease: disease.name_disease,
        name_disease_en: disease.name_disease_en
      }
      // Also update disease_relate field
      formData.value.disease_relate = {
        id_disease: disease.id_disease,
        name_disease: disease.name_disease,
        name_disease_en: disease.name_disease_en
      }
    }
  } else {
    formData.value.id_disease_relate = undefined
    formData.value.disease_relate = undefined
  }
})

// Watch for changes in formData.id_disease_relate and update selectedDisease
watch(
  () => formData.value.id_disease_relate,
  (newVal) => {
    if (newVal) {
      // If id_disease_relate is an object with id_disease property
      if (typeof newVal === 'object' && 'id_disease' in newVal) {
        selectedDisease.value = newVal.id_disease
      }
      // If id_disease_relate is a number
      else if (typeof newVal === 'number') {
        selectedDisease.value = newVal
      } else {
        selectedDisease.value = undefined
      }
    } else {
      selectedDisease.value = undefined
    }
  },
  { immediate: true }
)

const fetchTemplates = async () => {
  try {
    const response = await api.get('/pps_question_templates', {
      params: {
        page_size: 100
      }
    })
    if (response && response.data) {
      const templates = response.data.data

      preTemplateOptions.value = templates.map(
        (template: PPSQuestionTemplate) => ({
          label: template.name_button,
          value: template.id_pps_qs_template
        })
      )

      postTemplateOptions.value = [...preTemplateOptions.value]
    }
  } catch (error) {
    console.error('Error fetching templates:', error)
  }
}

// Initialize form data from props
const initFormDataFromProps = () => {
  if (props.data) {
    // Extract template IDs from props.data
    const preTemplateId = extractTemplateId(
      props.data.id_pps_qs_template_pre as TemplateReference
    )
    const postTemplateId = extractTemplateId(
      props.data.id_pps_qs_template_post as TemplateReference
    )

    // If data is provided via props, use it
    formData.value = {
      id_pps_qs_template: props.data.id_pps_qs_template,
      name_button: props.data.name_button || '',
      memo_explanation: props.data.memo_explanation || '',
      id_disease_relate: props.data.id_disease_relate,
      flg_composition: props.data.flg_composition || false,
      type_qs_composition: props.data.type_qs_composition || 1,
      id_pps_qs_template_pre: preTemplateId,
      id_pps_qs_template_post: postTemplateId,
      flg_active: props.data.flg_active || false,
      flg_pps_available: props.data.flg_pps_available || false,
      display_order: props.data.display_order || ''
    }

    // Set selectedDisease based on id_disease_relate
    if (props.data.id_disease_relate) {
      // Check if it's an object (old structure) or a number (new structure)
      if (
        typeof props.data.id_disease_relate === 'object' &&
        props.data.id_disease_relate !== null
      ) {
        // Use type assertion to treat it as DiseaseRelate
        const diseaseRelate = props.data.id_disease_relate as DiseaseRelate
        selectedDisease.value = diseaseRelate.id_disease
      } else if (typeof props.data.id_disease_relate === 'number') {
        selectedDisease.value = props.data.id_disease_relate
      }
    }
  } else if (props.id_pps_qs_template > 0) {
    // If no data but ID is provided, fetch the data
    fetchTemplateById(props.id_pps_qs_template)
  }
}

const fetchTemplateById = async (id: number) => {
  loading.value = true
  try {
    const response = await ppsStore.fetchPPSQuestionTemplateById(id)
    // Cast response to any to handle potential undefined values
    const responseData = response as any
    if (responseData?.data?.data) {
      const template = responseData.data.data

      // Extract template IDs
      const preTemplateId = extractTemplateId(
        template.id_pps_qs_template_pre as TemplateReference
      )
      const postTemplateId = extractTemplateId(
        template.id_pps_qs_template_post as TemplateReference
      )

      formData.value = {
        id_pps_qs_template: template.id_pps_qs_template,
        name_button: template.name_button || '',
        memo_explanation: template.memo_explanation || '',
        // Store both fields (new and old structure)
        id_disease_relate: template.id_disease_relate,
        disease_relate: template.disease_relate,
        flg_composition: template.flg_composition || false,
        type_qs_composition: template.type_qs_composition || 1,
        id_pps_qs_template_pre: preTemplateId,
        id_pps_qs_template_post: postTemplateId,
        flg_active: template.flg_active || false,
        flg_pps_available: template.flg_pps_available || false,
        display_order: template.display_order || ''
      }

      // Set selectedDisease based on available data
      if (typeof template.id_disease_relate === 'number') {
        // New structure: id_disease_relate is a number ID
        selectedDisease.value = template.id_disease_relate
      } else if (template.disease_relate?.id_disease) {
        // New structure: disease_relate is an object with id_disease
        selectedDisease.value = template.disease_relate.id_disease
      } else if (
        typeof template.id_disease_relate === 'object' &&
        template.id_disease_relate?.id_disease
      ) {
        // Old structure: id_disease_relate is an object with id_disease
        selectedDisease.value = template.id_disease_relate.id_disease
      }
    }
  } catch (error) {
    console.error('Error fetching template:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
  } finally {
    loading.value = false
  }
}

const submit = async () => {
  if (!formRef.value) return

  // @ts-ignore
  const isValid = await formRef.value.validate()
  if (!isValid) return

  loading.value = true
  try {
    // Create a submission-ready data object with proper typing
    const apiData: Record<string, any> = {}

    // Copy all properties from formData
    Object.entries(formData.value).forEach(([key, value]) => {
      if (key === 'id_disease_relate' && value) {
        // Convert id_disease_relate from object to ID value
        if (typeof value === 'object' && 'id_disease' in value) {
          apiData[key] = value.id_disease
        } else {
          apiData[key] = value
        }
      } else if (key === 'disease_relate') {
        // Skip this field - we only need id_disease_relate for the API
      } else if (
        key === 'id_pps_qs_template_pre' ||
        key === 'id_pps_qs_template_post'
      ) {
        // Ensure template pre/post are stored as IDs
        apiData[key] = extractTemplateId(value as TemplateReference)
      } else if (value !== undefined) {
        apiData[key] = value
      }
    })

    // Remove id_pps_qs_template from create payload if it's not valid
    if (!props.id_pps_qs_template || props.id_pps_qs_template <= 0) {
      delete apiData.id_pps_qs_template
    }

    let response
    if (props.id_pps_qs_template > 0) {
      response = await ppsStore.updatePPSQuestionTemplate(
        props.id_pps_qs_template,
        apiData
      )
    } else {
      response = await ppsStore.createPPSQuestionTemplate(apiData)
    }

    mtUtils.autoCloseAlert(aahMessages.success)
    closeModal(true)
  } catch (error) {
    console.error('Error saving template:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
  } finally {
    loading.value = false
  }
}

// Reset form to initial values
const resetForm = () => {
  formData.value = {
    id_pps_qs_template: undefined,
    name_button: '',
    memo_explanation: '',
    id_disease_relate: undefined,
    flg_composition: false,
    type_qs_composition: 1,
    id_pps_qs_template_pre: null,
    id_pps_qs_template_post: null,
    flg_active: false,
    flg_pps_available: false,
    display_order: ''
  }
  selectedDisease.value = undefined
}

// Initialize component
onMounted(async () => {
  await Promise.all([
    fetchDiseases(),
    fetchTemplates(),
    ppsStore.fetchQsCompositionTypes()
  ])

  // If an ID is provided, fetch the data
  if (props.id_pps_qs_template && props.id_pps_qs_template > 0) {
    await fetchTemplateById(props.id_pps_qs_template)
  } else if (props.data) {
    // If data is provided via props, use it
    initFormDataFromProps()
  }
})

// Computed properties
const modalTitle = computed(() => {
  return props.data?.name_button || 'テンプレート編集'
})
</script>

<template>
  <q-form ref="formRef" @submit.prevent="submit">
    <MtModalHeader @closeModal="closeModal(false)">
      <q-toolbar-title class="text-grey-900 title2 bold">
        {{ modalTitle }}
      </q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="row content">
      <div class="row q-col-gutter-md">
        <!-- First row -->
        <div class="col-12 col-md-6">
          <q-input
            v-model="formData.name_button"
            label="テンプレート名"
            standard
            :rules="[(val) => !!val || 'テンプレート名は必須です']"
          />
        </div>
        <div class="col-12 col-md-6">
          <q-input v-model="formData.memo_explanation" label="説明" standard />
        </div>

        <!-- Second row -->
        <div class="col-12 col-md-6">
          <q-select
            v-model="selectedDisease"
            :options="diseaseOptions"
            label="疾患"
            standard
            use-input
            input-debounce="300"
            option-label="label"
            option-value="id_disease"
            map-options
            emit-value
            :rules="[(val) => !!val || '疾患は必須です']"
            @filter="diseaseFilterFn"
          >
            <template v-slot:selected>
              <span v-if="selectedDisease">
                {{
                  diseaseOptions.find(
                    (opt) => opt.id_disease === selectedDisease
                  )?.label || ''
                }}
              </span>
            </template>
          </q-select>
        </div>
        <div class="col-12 col-md-6">
          <q-checkbox
            v-model="formData.flg_composition"
            label="複合テンプレート"
          />
        </div>
        <div class="col-12 col-md-6">
          <q-select
            v-model="formData.type_qs_composition"
            :options="qsCompositionOptions"
            label="複合タイプ"
            standard
            map-options
            emit-value
            option-label="label"
            option-value="value"
            :disable="!formData.flg_composition"
          >
            <template v-slot:selected>
              {{
                qsCompositionOptions.find(function (opt) {
                  return opt.value === formData.type_qs_composition
                })?.label || ''
              }}
            </template>
          </q-select>
        </div>

        <!-- Third row -->
        <div class="col-12 col-md-6">
          <q-select
            v-model="formData.id_pps_qs_template_post"
            :options="postTemplateOptions"
            label="事後テンプレート"
            standard
            emit-value
            map-options
            clearable
            :disable="!formData.flg_composition"
          />
        </div>
        <div class="col-12 col-md-6">
          <q-select
            v-model="formData.id_pps_qs_template_pre"
            :options="preTemplateOptions"
            label="事前テンプレート"
            standard
            emit-value
            map-options
            clearable
            :disable="!formData.flg_composition"
          />
        </div>

        <!-- Fourth row -->
        <div class="col-12 col-md-6">
          <q-checkbox v-model="formData.flg_active" label="アクティブ" />
        </div>
        <div class="col-12 col-md-6">
          <q-checkbox
            v-model="formData.flg_pps_available"
            label="PPS利用可能"
          />
        </div>
        <div class="col-12 col-md-6">
          <MtFormInputNumber
            type="number"
            v-model:value="formData.display_order"
            label="表示順"
            class=""
          />
        </div>
      </div>
    </q-card-section>

    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn
          outline
          class="bg-grey-100 text-grey-800"
          @click="closeModal(false)"
        >
          <span>キャンセル</span>
        </q-btn>
        <q-btn
          unelevated
          color="primary"
          class="q-ml-md"
          type="submit"
          :loading="loading"
          :disable="loading"
        >
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
