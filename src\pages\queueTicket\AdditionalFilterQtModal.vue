<script lang="ts">
export const SORT_TYPE_DEFAULT = 'process'

export interface AdditionalFilterQtModalPropsSearch {
  sortState: string,
  typeAnimal: string,
  searchDoctor: string,
}

type AdditionalFilterQtModalPropsSearchCalbackActions = 'save' | 'none' | 'reset'

export interface AdditionalFilterQtModalProps { 
  search: AdditionalFilterQtModalPropsSearch,
  searchCallback: (action: AdditionalFilterQtModalPropsSearchCalbackActions, data: AdditionalFilterQtModalPropsSearch) => Promise<void>
}
</script>
<script lang="ts" setup>
import { ref, onUnmounted } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import _ from 'lodash'

import useCommonStore from '@/stores/common'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'

const commonStore = useCommonStore()
const { getCommonTypeAnimalOptionList } = storeToRefs(commonStore)

const emits = defineEmits<{
  (e: 'close'): void
}>()


const props = defineProps<AdditionalFilterQtModalProps>()

const sortState = ref<AdditionalFilterQtModalPropsSearch['sortState']>(props.search.sortState)
const typeAnimal = ref<AdditionalFilterQtModalPropsSearch['typeAnimal']>(props.search.typeAnimal)
const searchDoctor = ref<AdditionalFilterQtModalPropsSearch['searchDoctor']>(props.search.searchDoctor)
const action = ref<AdditionalFilterQtModalPropsSearchCalbackActions>('none')


const handleClearFilters = () => {
  action.value = 'reset'
  handleCloseModal()
}

const handleSaveFilters = () => {
  action.value = 'save'
  handleCloseModal()
}

const handleSortStateChange = ( value: string) => {
  sortState.value = value ? value : SORT_TYPE_DEFAULT
}

const handleTypeAnimalChange = ( value: string) => {
  typeAnimal.value = value
}
const handleSearchDoctorChange = ( value: string) => {
  searchDoctor.value = value
}


const handleCloseModal = () => emits('close')

onUnmounted(async () => {
  if (props.searchCallback && _.isFunction(props.searchCallback)) {
    await props.searchCallback(action.value, {
      sortState: sortState.value,
      typeAnimal: typeAnimal.value,
      searchDoctor: searchDoctor.value
    })
  }
})

</script>
<template>
  <MtModalHeader @closeModal="handleCloseModal">
    <q-toolbar-title class="text-grey-900 title3 bold">
      詳細検索
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section class="row q-col-gutter-sm gap-2">
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormPullDown
        v-model="sortState"
        :options="[
          { label: '処理順で並び替え', value: 'process' },
          { label: '整理券番号で並び替え', value: 'number' },
          { label: '整理券発行時間で並び替え', value: 'time' }
        ]"
        @update:model-value="handleSortStateChange"
        outlined
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormPullDown
        v-model:selected="typeAnimal"
        label="動物区分"
        :options="
          getCommonTypeAnimalOptionList.filter((p: any) =>
            dayjs(p.date_end).isSame(dayjs('9999/12/31'), 'year')
          )
        "
        outlined
        @update:selected="handleTypeAnimalChange"
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
        <InputEmployeeOptGroup
          label="担当医"
          class=""
          type-occupation="doctor"
          v-model:selected="searchDoctor"
          defaultBlank
          show-select-default-employee
          @update:model-value="handleSearchDoctorChange"
          @update:select-default-employee="handleSearchDoctorChange"
        />
    </div>
    <div class="flex justify-end full-width text-blue cursor-pointer" @click="handleClearFilters">
      全クリア
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn outline @click="handleCloseModal()" class="bg-grey-100 text-grey-800">
        <span>閉じる</span>
      </q-btn>
      <q-btn
        tabindex="10"
        color="primary"
        class="q-ml-md"
        @click="handleSaveFilters"
      >
        <span>適用</span>
      </q-btn>
    </div>
  </q-card-section>
</template>