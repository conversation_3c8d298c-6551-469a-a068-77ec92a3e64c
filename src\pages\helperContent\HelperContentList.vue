<script setup lang="ts">
import { computed, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import tabItemsJson from './HelperContentTabItems.json'

const splitterModel = ref(15)
const searchText = ref('')

type TabItem = {
  label: string
  name: string
  itemType: 'QItem' | 'QExpansionItem'
  level: number
  content: string | null
  children?: TabItem[]
}
const tabItems: TabItem[] = tabItemsJson as TabItem[]
const displayedTabs = computed(() => {
  return tabItems.filter((item) => item.name.includes(searchText.value.toLowerCase()))
})
function flattenPanels(items: TabItem[]): TabItem[] {
  const result: TabItem[] = []
  for (const item of items) {
    result.push(item)
    if (item.children?.length) {
      result.push(...flattenPanels(item.children))
    }
  }
  return result
}
const flatTabPanels = computed(() => flattenPanels(tabItems))

const tab = ref('introduction')
const onSelectTab = (item: TabItem) => {
  tab.value = item.name
}
</script>

<template>
  <MtHeader>
    <q-toolbar class="text-primary q-pa-none">
      <q-toolbar-title class="title2 bold text-grey-900"> Helper Content List </q-toolbar-title>
    </q-toolbar>
  </MtHeader>
  <div class="column">
    <q-splitter v-model="splitterModel" class="col">
      <template v-slot:before>
        <q-input v-model="searchText" placeholder="Search" dense outlined class="q-pa-md">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
        <q-list padding class="rounded-borders text-primary">
          <template v-for="tabItem in displayedTabs" :key="`${tabItem.name}-list`">
            <q-item
              v-if="tabItem.itemType === 'QItem'"
              clickable
              v-ripple
              :active="tab === tabItem.name"
              active-class="menu__active"
              @click="onSelectTab(tabItem)"
            >
              <q-item-section>{{ tabItem.label }}</q-item-section>
            </q-item>
            <q-expansion-item v-if="tabItem.itemType === 'QExpansionItem'" :label="tabItem.label">
              <template v-for="childItem in tabItem.children" :key="`${tabItem.name}-expansion`">
                <q-item
                  v-if="childItem.itemType === 'QItem'"
                  clickable
                  v-ripple
                  dense
                  :active="tab === childItem.name"
                  :inset-level="childItem.level"
                  :class="`menu__child menu__child-${childItem.level}`"
                  active-class="menu__active"
                  @click="onSelectTab(childItem)"
                >
                  <q-item-section>{{ childItem.label }}</q-item-section>
                </q-item>
              </template>
            </q-expansion-item>
          </template>
        </q-list>
      </template>

      <template v-slot:after>
        <q-tab-panels
          v-model="tab"
          animated
          swipeable
          vertical
          transition-prev="jump-up"
          transition-next="jump-up"
          class="q-pa-md"
        >
          <q-tab-panel v-for="tabItem in flatTabPanels" :key="`${tabItem.name}-panel`" :name="tabItem.name">
            <div class="text-h4 q-mb-md">{{ tabItem.label }}</div>
            <div>{{ tabItem.content }}</div>
            <p>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Quis praesentium cumque magnam odio iure quidem,
              quod illum numquam possimus obcaecati commodi minima assumenda consectetur culpa fuga nulla ullam. In,
              libero.
            </p>
            <p>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Quis praesentium cumque magnam odio iure quidem,
              quod illum numquam possimus obcaecati commodi minima assumenda consectetur culpa fuga nulla ullam. In,
              libero.
            </p>
          </q-tab-panel>
        </q-tab-panels>
      </template>
    </q-splitter>
  </div>
</template>

<style scoped lang="scss">
.menu {
  &__active {
    color: white;
    background: #f2c037;
  }
  &__child {
    border-radius: 4px;
  }
  &__child-1 {
    margin: 0px 16px;
    padding-left: 16px !important;
  }
  &__child-2 {
    margin: 0px 16px 0px 32px;
    padding-left: 16px !important;
  }
}
</style>
