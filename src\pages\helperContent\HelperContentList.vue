<script setup lang="ts">
import { computed, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'

const tabItems = [
  {
    label: 'Customers',
    name: 'customer'
  },
  {
    label: 'Clinic',
    name: 'clinic'
  },
  {
    label: 'Requests',
    name: 'request'
  },
  {
    label: 'Item Services',
    name: 'item-service'
  },
  {
    label: 'Check In',
    name: 'check-in'
  },
  {
    label: 'Koekaru',
    name: 'koekaru'
  }
]
const tab = ref('customer')
const searchText = ref('')
const splitterModel = ref(15)

const displayedTabs = computed(() => {
  return tabItems.filter((item) => item.name.includes(searchText.value.toLowerCase()))
})
</script>

<template>
  <MtHeader>
    <q-toolbar class="text-primary q-pa-none">
      <q-toolbar-title class="title2 bold text-grey-900"> Helper Content List </q-toolbar-title>
    </q-toolbar>
  </MtHeader>
  <div class="column">
    <q-splitter v-model="splitterModel" class="col">
      <template v-slot:before>
        <q-input v-model="searchText" placeholder="Search" dense outlined class="q-pa-md">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
        <q-tabs
          v-model="tab"
          vertical
          no-caps
          align="left"
          active-bg-color="primary"
          active-color="white"
          indicator-color="teal"
        >
          <q-tab v-for="tabItem in displayedTabs" :key="tabItem.name" :name="tabItem.name" :label="tabItem.label" />
        </q-tabs>
      </template>

      <template v-slot:after>
        <q-tab-panels
          v-model="tab"
          animated
          swipeable
          vertical
          transition-prev="jump-up"
          transition-next="jump-up"
          class="q-pa-md"
        >
          <q-tab-panel v-for="tabItem in tabItems" :key="`${tabItem.name}-panel`" :name="tabItem.name">
            <div class="text-h4 q-mb-md">{{ tabItem.label }}</div>
            <p>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Quis praesentium cumque magnam odio iure quidem,
              quod illum numquam possimus obcaecati commodi minima assumenda consectetur culpa fuga nulla ullam. In,
              libero.
            </p>
            <p>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Quis praesentium cumque magnam odio iure quidem,
              quod illum numquam possimus obcaecati commodi minima assumenda consectetur culpa fuga nulla ullam. In,
              libero.
            </p>
          </q-tab-panel>
        </q-tab-panels>
      </template>
    </q-splitter>
  </div>
</template>

<style scoped lang="scss"></style>
