<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import UpdateShemeImageTemplateModal from './UpdateShemeImageTemplateModal.vue'
import mtUtils from '@/utils/mtUtils'
import useTextTemplateStore from '@/stores/text-template'
import { storeToRefs } from 'pinia'
import { typeSchemaImageAnimal, typeSchemaImageCategory, typeTextTemplate } from '@/utils/enum'
import { useRouter } from 'vue-router'
import { setPageTitle } from '@/utils/pageTitleHelper'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import MtCarousel from '@/components/MtCarousel.vue'
import { sortBy } from 'lodash'

const templateStore = useTextTemplateStore()
const { getTemplates } = storeToRefs(templateStore)
const router = useRouter()


const columns = [
  // {
  //   name: 'memo_template',
  //   label: '名称',
  //   field: 'memo_template',
  //   align: 'left'
  // },
  {
    name: 'img_file_path_template',
    label: 'Image - Name',
    field: 'img_file_path_template',
    align: 'left'
  },
  {
    name: 'display_order',
    label: '表示順序',
    field: 'display_order',
    align: 'left'
  }
]

function init() {}

const openAddModal = async () => {
  let updatedFlg = { value: false }
  templateStore.selectTemplate(null)
  await mtUtils.smallPopup(UpdateShemeImageTemplateModal, { updatedFlg })
  if (updatedFlg.value) {
    search()
  }
}
const onRowClick = async (data) => {
  let updatedFlg = { value: false }
  templateStore.selectTemplate(data.id_text_template)
  await mtUtils.smallPopup(UpdateShemeImageTemplateModal, {
    updatedFlg,
    data,
    searchData: search
  })
  if (updatedFlg.value) {
    search()
  }
}
const typeEtc1 = ref([...typeSchemaImageAnimal].map(item => item.value))
const typeEtc2Options = ref([...typeSchemaImageCategory])
const selectedTypeEtc2 = ref(null)
const search = async () => {
  await templateStore.fetchTemplates({
    no_pagination: true,
  type_text_template: 100,
    // type_etc1: typeEtc1.value.join(','),
    // type_etc2: selectedTypeEtc2.value,
  })
}
onMounted(async () => {
  await search()
  // setWheelScroll()
  setPageTitle('シェーマ図')
})

const getGroupedTitle = (item) =>{
  return typeSchemaImageCategory.find(({value}) => {
    return value == item
  })?.label
}
const getGroupedSubTitle = (item) =>{
  return typeSchemaImageCategory.find(({value}) => {
    return value == item
  })?.subLabel
}
const getGroupedTemplate = computed(() => {
  const obj = {}
  getTemplates.value
    .filter(item => {
      if(typeEtc1.value.length === 0) {
        return true
      }
      return typeEtc1.value.filter(t => {
        if(!item.type_etc1 && t == 590) {
          return true
        }
        return item?.type_etc1?.split(',').map(i => Number.parseInt(i)).includes(t)
      }).length > 0
    })
    .forEach(item => {
      let typeEtc2 = item.type_etc2
      if (!typeEtc2) {
        obj[190] = obj[190] ? [...obj[190], item] : [item]
        return
      }
      if(!obj[typeEtc2]) {
        obj[typeEtc2] = []
      }
      typeEtc2.split(',')
        .forEach((typeItem) => {
          typeItem = Number.parseInt(typeItem.trim())
          let category = typeSchemaImageCategory.find(({value}) => {
            return value == typeItem
          })
          if(category) {
            obj[category.value] = obj[category.value] ? [...obj[category.value], item] : [item]
          }
        })
    })
  
  Object.keys((obj), (key) => {
    obj[key] = sortBy(obj[key], 'display_order', 'asc')
  })

  return obj
})

const moveToSelectedGroup = (item) => {
  selectedTypeEtc2.value = item.value
  let elements = []
  if(window.innerWidth < 1024) {
    elements = document.getElementsByClassName('mobile-group-'+ item.value) || []
  }else {
    elements = document.getElementsByClassName('group-'+ item.value) || []
  }
  Array.from(elements).forEach((element) => {
    const yOffset = -150; 
    const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

    window.scrollTo({
      top: y,
      behavior: 'smooth'
    });
  })
}
const scrollUpdate = (value) => {
  const item = typeEtc2Options.value.find((item) => item.value === value)
  if(!item) {
    return
  }
  moveToSelectedGroup(item)
}

const selectTypeEtc1 = (item) => {
  if(typeEtc1.value.includes(item.value)) {
    typeEtc1.value = typeEtc1.value.filter((i) => i !== item.value)
  } else {
    typeEtc1.value.push(item.value)
  }
}

const isTypeEtc1Selected = (item) => {
  return typeEtc1.value.includes(item.value)
}

const getAvailableGroups = computed(() => {

  const selectOptions = [];
  getTemplates.value
    .forEach((item) => {
      let origOption = null
      if(!item.type_etc2) {
        origOption = typeSchemaImageCategory.find((t) => t.value === 190)
      } else {
        origOption = typeSchemaImageCategory.find((t) => t.value == item.type_etc2)
      }

      if (origOption) {
        const existingOption = selectOptions.find((option) => option.value === origOption.value);
        if (!existingOption) {
          selectOptions.push(origOption);
        }
      }
    })
  selectOptions.sort((a, b) => a.value - b.value)
  return selectOptions
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          シェーマ図一覧
        </q-toolbar-title>
        <q-btn
          @click="openAddModal()"
          unelevated
          color="grey-800"
          text-color="white"
        >
          <q-icon size="20px" name="add" />シェーマ図
        </q-btn>
      </q-toolbar>
    </MtHeader>
    <div class="">
      <div class="bg-white" style="position: sticky; top: 4px; z-index: 10;">
        <div class="flex justify-start align-center q-pa-lg type-stc1-scroll">
            <q-btn
                    v-for="(item, index) in typeSchemaImageAnimal"
                    :key="index"
                   :outline="!isTypeEtc1Selected(item)"
                   :unelevated="!isTypeEtc1Selected(item)"
                   color="grey-800"
                   :text-color="!isTypeEtc1Selected(item) ? 'primary' : 'white'" @click="selectTypeEtc1(item)">
              <span style="text-wrap: nowrap" class="text-h5">{{item.label}}</span>
            </q-btn>
        </div>
        <q-card class="q-mx-lg">
        <div class="collapsable-container q-pa-lg" :class="collapseTypeEtc2 ? 'collapse' : ''">
  <!--          <q-btn v-else  outline class="bg-grey-100 text-grey-800"  @click="moveToSelectedGroup(item)">-->
  <!--            <span style="text-wrap: nowrap" class="text-h5">{{item.label}}</span>-->
  <!--          </q-btn>-->
          <div class="carousel q-px-sm" v-if="getAvailableGroups.length > 0">
            <MtCarousel :options="getAvailableGroups" @update:model-value="scrollUpdate" v-model="selectedTypeEtc2"  />
          </div>
        </div>
        </q-card>
      </div>
      <div class="q-pt-sm q-pa-md">
        <!--   desktop   -->
        <div class=" desktop-layout q-mt-md q-px-lg" style="gap: 16px; width: 100%; display: flex; flex-direction: column;">
          <div class="row  " :class="`group-${index}`" v-for="(groupedItems, index) in getGroupedTemplate" :key="index">
            <div class="col-3 section-link">
              <div class="text-h5">{{getGroupedTitle(index)}}</div>
              <div class="text-caption">{{getGroupedSubTitle(index)}}</div>
            </div>
            <div class="col-9">
              <div class="flex justify-start items-center" style="gap: 16px">
                <q-card class="cursor-pointer" v-for="item in groupedItems" :key="item" style="width: fit-content; height: fit-content" @click="onRowClick(item)">
                  <q-img
                    loading="lazy"
                    class=" rounded-borders"
                    fit="cover"
                    width="260px"
                    height="260px"
                    img-class="fetch-list-img"
                    aspect-ratio="1"
                    :src="item.img_file_path_template"  alt="Item Image" >

                  </q-img>
                </q-card>
              </div>
            </div>
          </div>
        </div>
        <!--   mobile   -->
        <div class="flex column q-px-lg row mobile-layout" style="width: 100%; gap: 16px;">
          <div class="q-mt-sm " v-for="(groupedItems, index) in getGroupedTemplate" :key="index">
            <div class="section-link q-mb-sm" :class="`mobile-group-${index}`">
              <div class="text-h5">{{getGroupedTitle(index)}}</div>
              <div class="text-caption">{{getGroupedSubTitle(index)}}</div>
            </div>
            <div  class="flex justify-start items-center" style="gap: 16px">
              <q-card v-for="(item, index) in groupedItems" :key="index" style="width: fit-content; height: fit-content" @click="onRowClick(item)">
                <q-img
                  loading="lazy"
                  class=" rounded-borders"
                  fit="cover"
                  width="260px"
                  height="260px"
                  img-class="fetch-list-img"
                  aspect-ratio="1"
                  :src="item.img_file_path_template" alt="Item Image" >

                </q-img>
              </q-card>
            </div>
          </div>
          <div class="q-mt-sm flex justify-center items-center" v-if="Object.keys(getGroupedTemplate).length === 0">
            <div class="text-h5">アイテムが見つかりません</div>
          </div>
        </div>
      </div>
      <q-page-scroller position="bottom-right" :scroll-offset="150" :offset="[18, 18]">
        <q-btn fab icon="keyboard_arrow_up" color="primary" />
      </q-page-scroller>
    </div>
   
  </q-page>
</template>
<style lang="scss" scoped>

.grid-item {
  flex-basis: calc(100% - 16px);
  cursor: pointer;
  max-height: 280px;
  max-width: 280px;
  aspect-ratio: 300/300;
  @media screen and (min-width: 500px) {
    flex-basis: calc(50% - 16px);
  }
  @media screen and (min-width: 950px) {
    flex-basis: calc(300px - 16px);
  }
  @media screen and (min-width: 1260px) {
    flex-basis: calc(300px - 16px);
  }
  .q-img {
    width: 100%;
    height: 100%;
  }
  .video-container {
    width: 100%;
    position: relative;
  }
  img,
  .video-container,
  video {
    width: 100%;
    border-radius: 8px;
    height: 100%;
    object-fit: cover;
    max-height: 300px;
    aspect-ratio: 300/300;
  }
}

.collapsable-container {
  width: 100%;
  height: fit-content;
  display: flex;
  align-items: center;
  justify-content: start;
  flex-wrap: wrap;
  gap: 16px;
  position: relative;
  transition: height 0.3s ease-out;
  &.collapse {
    height: 0;
  }
}

.close-collapsable-container {
  position: absolute;
  bottom: 0;
  right: 0;
  
  &.close {
    bottom: 0;
    right: 0;
  }
}

.carousel {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  width: calc(100% - 0px);
}

.desktop-layout {
  display: none !important;
}

@media (min-width: $breakpoint-sm) {
    .desktop-layout {
      display: flex !important;
    }
  
    .mobile-layout {
      display: none;
    }
}

.fetch-list-img {
  max-height: 258px;
  height: 100%;
}

.type-stc1-scroll {
  width: 100%; 
  gap: 16px;
  flex-wrap: nowrap;
  overflow-x: auto;
  
}

</style>
