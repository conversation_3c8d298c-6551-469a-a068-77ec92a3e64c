<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import useEmpInfoStore from '@/stores/empInfo'
import mtUtils from '@/utils/mtUtils'

// Directly Imported Components
import MtModalHeader from '@/components/MtModalHeader.vue'

// Props and Emits
const props = defineProps<{
  employeeName: string
  employeeId: number
  empInfoId: number
  employeeImage?: string
}>()

const emits = defineEmits(['close'])

// Store
const empInfoStore = useEmpInfoStore()

// Close modal
const closeModal = () => {
  emits('close')
}

// Confirm employee read
const confirmRead = async () => {
  try {
    let data = {
      id_emp_info: props.empInfoId,
      id_employee: props.employeeId,
      flg_read: true
    }

    const res = await empInfoStore.updateEmpInfoRead(data)
    if (res) {
      // Refresh the emp info data to show latest status
      await empInfoStore.selectEmpInfo(props.empInfoId)

      // Close the modal
      emits('close')

      // Show success message
      mtUtils.autoCloseAlert('確認しました')
    }
  } catch (error) {
    console.error('Error updating employee read status:', error)
    mtUtils.autoCloseAlert('エラーが発生しました')
  }
}
</script>

<template>
  <q-card-section class="q-px-lg content">
    <div class="">
      <div class="flex items-center q-mb-sm">
        <img class="q-mr-xs emp-info-image" v-if="employeeImage" :src="employeeImage" />
        <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
        <span class="text-grey-900">
          {{ employeeName }}さん
        </span>
      </div>

      <div class="text-grey-900">
        院内連絡確認しましたか？
      </div>
    </div>
  </q-card-section>


  <q-card-section class="q-bt bg-white">
    <div class="text-center">
      <div class="row q-gutter-md justify-center modal-btn">
        <q-btn
          outline
          color="grey-7"
          class="confirmation-btn-close"
          @click="closeModal"
        >
          閉じる
        </q-btn>

        <q-btn
          color="primary"
          class="confirmation-btn-confirm text-white"
          @click="confirmRead"
        >
          <q-icon name="check_circle_outline" class="q-mr-sm" />
          確認しました
        </q-btn>
      </div>
    </div>
  </q-card-section>
</template>

<style lang="scss" scoped>
.modal-btn {
  .q-btn {
    min-width: 120px;
  }
}
</style>
