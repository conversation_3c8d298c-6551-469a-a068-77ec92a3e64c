<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import useEmpInfoStore from '@/stores/empInfo'
import mtUtils from '@/utils/mtUtils'

// Props and Emits
const props = defineProps<{
  employeeName: string
  employeeId: number
  empInfoId: number
}>()

const emits = defineEmits(['close'])

// Store
const empInfoStore = useEmpInfoStore()

// Close modal
const closeModal = () => {
  emits('close')
}

// Confirm employee read
const confirmRead = async () => {
  try {
    let data = {
      id_emp_info: props.empInfoId,
      id_employee: props.employeeId,
      flg_read: true
    }
    
    const res = await empInfoStore.updateEmpInfoRead(data)
    if (res) {
      // Refresh the emp info data to show latest status
      await empInfoStore.selectEmpInfo(props.empInfoId)
      
      // Close the modal
      emits('close')
      
      // Show success message
      mtUtils.autoCloseAlert('確認しました')
    }
  } catch (error) {
    console.error('Error updating employee read status:', error)
    mtUtils.autoCloseAlert('エラーが発生しました')
  }
}
</script>

<template>
  <div class="confirmation-modal-content">
    <div class="text-center q-pa-lg">
      <div class="text-h6 text-grey-700 q-mb-md">
        Emp Info Detail confirmation modal
      </div>
      
      <div class="flex items-center justify-center q-mb-lg">
        <span class="text-h5 text-grey-900">
          {{ employeeName }}さん
        </span>
      </div>
      
      <div class="text-h6 text-grey-900 q-mb-xl">
        院内連絡確認しましたか？
      </div>
      
      <div class="row q-gutter-md justify-center">
        <q-btn 
          outline
          color="grey-7"
          size="lg"
          class="confirmation-btn-close"
          @click="closeModal"
        >
          閉じる
        </q-btn>
        
        <q-btn 
          color="grey-9"
          size="lg"
          class="confirmation-btn-confirm"
          @click="confirmRead"
        >
          <q-icon name="check" class="q-mr-sm" color="white" />
          確認しました
        </q-btn>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.confirmation-modal-content {
  min-width: 400px;
  max-width: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.confirmation-btn-close {
  min-width: 120px;
  padding: 12px 24px;
}

.confirmation-btn-confirm {
  min-width: 160px;
  padding: 12px 24px;
}
</style>
