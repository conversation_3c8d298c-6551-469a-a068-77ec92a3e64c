<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import ViewTestModal from './ViewTestModal.vue'
import { setPageTitle } from '@/utils/pageTitleHelper'
import { computed } from 'vue'
import useClinicStore from '@/stores/clinics'
import useAuthStore from '@/stores/auth'
import useCommonStore from '@/stores/common'
import { ClinicType } from '@/types/types'

const clinicStore = useClinicStore()
const authStore = useAuthStore()
const commonStore = useCommonStore()

const getAuthUser = computed(() => authStore.getAuthUser)

// Mock data for clinic dropdown

const clinicOptions = computed(() => {
  const currentLoginUserClinics = clinicStore.getClinics.filter((c: ClinicType) => {
    return getAuthUser.value.id_clinic_list.includes(c.id_clinic)
  })
  return currentLoginUserClinics
   .map((c: ClinicType) => ({
      ...c,
      label: c.name_clinic_display,
      value: c.id_clinic
    }))
   .sort((a: ClinicType, b: ClinicType) => a.display_order - b.display_order) 
})

// Selected clinic
const selectedClinic = ref<number | null>(null) // Default to VetPrescrinic

const searchData = ref({
  clinic: selectedClinic.value
})

// Update search data when clinic changes
const updateSearchData = () => {
  searchData.value.clinic = selectedClinic.value
}

// Handle clinic dropdown change
const onClinicChange = () => {
  updateSearchData()
}

onMounted(async () => {
  selectedClinic.value = localStorage.getItem('selectedClinic')
  setPageTitle('開発用確認画面')
  updateSearchData()
})
</script>

<template>
  <q-page>
    <MtHeader>
      <q-toolbar>
        <q-toolbar-title class="title1 bold text-grey-900">
          開発用確認画面
        </q-toolbar-title>
      </q-toolbar>
    </MtHeader>
    
    <!-- Clinic Selection -->
    <q-card flat class="clinic-selection">
      <q-card-section>
        <div class="row items-center justify-end">
          <q-select
            v-model="selectedClinic"
            :options="clinicOptions"
            outlined
            dense
            emit-value
            map-options
            label="病院名"
            style="min-width: 200px"
            @update:model-value="onClinicChange"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- Scheduler Content -->
    <div class="scheduler-container">
      <ViewTestModal 
        :searchData="searchData"
        :selectedClinic="selectedClinic"
      />
    </div>
  </q-page>
</template>

<style lang="scss" scoped>
.clinic-selection {
  background-color: #f8f9fa;
  border-bottom: 2px solid #e0e0e0;
  border-left: 4px solid #2196f3;
  margin: 0;
}

.scheduler-container {
  background-color: white;
  min-height: calc(100vh - 200px);
  padding: 0;
}

.title1 {
  font-size: 24px;
  font-weight: bold;
}
</style>
