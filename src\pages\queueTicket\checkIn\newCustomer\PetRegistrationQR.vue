<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import ReceptionConfirmation from '@/pages/queueTicket/checkIn/ReceptionConfirmation.vue'
import petImage from '@/assets/img/checkin/pet.png'

import mtUtils from '@/utils/mtUtils'
import { event_bus } from '@/utils/eventBus'

import {
  openLargeModal
} from '../checkInUtils'

interface Props {
  barCode: string,
  numberQueueTicket: string | string[]
  skipQr: boolean
}
const props = withDefaults(defineProps<Props>(), {
  barCode: '',
  numberQueueTicket: '',
  skipQr: false
})

const { numberQueueTicket } = props

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const openConfirmationModal = () => {
  const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup
  popupFunction(ReceptionConfirmation, {
    confirmedQueueTickets: Array.isArray(numberQueueTicket) ? numberQueueTicket : [numberQueueTicket],
    popup: {
      persistent: true
    }
  }, true)
}

onMounted(() => {
  event_bus.on('finalConfirmationDone', closeModal)
  if (props.skipQr) {
    openConfirmationModal( )
  }
})

onUnmounted(() => {
  event_bus.off('finalConfirmationDone', closeModal)
})
</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
    <MtModalHeader @closeModal="closeModal" :closeBtn="false">
      <q-toolbar-title class="queue-ticket-title row no-wrap">
        <span>
          ペット情報登録のお願い
        </span>
      </q-toolbar-title>
    </MtModalHeader>
      <q-card-section class="qt-wrapper relative-position" style="font-size: 20px; padding-top: 0px !important;">
        <div class="queue-ticket-wrapper">
          <div class="queue-ticket-wrapper-inner">
            <div class="bg-accent-100 q-py-md q-px-lg flex justify-between items-center qr-section">
              <div class="qr-message">
                新しいペット情報の登録をお願いいたします。<br />
                右のQRコードをスマートフォンで読み取り、WEBでの登録をお願いいたします。
              </div>
              <div class="qr-code-wrapper">
                <div class="qr-code-wrapper-inner">
                  <img :src="barCode" />
                </div>
              </div>
              <div class="image-wrapper">
                <img :src="petImage" />
              </div>
            </div>
            <div class="qr-section-sub">
              <span>
                QRコードを読み取りができましたら、下のボタンを選択してください。
              </span>
              <span>
                入力はお待ちの時間でお願いいたします。
              </span>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-section>
        <div class="flex justify-center">
          <q-btn 
            outline 
            class="outline-btn top-btn" 
            @click="openConfirmationModal"
            >
            <span>QRコードを読み取りました</span>
          </q-btn>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<style lang="scss">
.mobile.platform-ios {
  .checkin-feat {
    .queue-ticket-wrapper {
      .queue-ticket-wrapper-inner {
        order: 9999 !important;
        margin-bottom: 0px !important;
      }
    }
  }
}
</style>
<style lang="scss" scoped>

@media (max-width: 1528px) {
  .queue-ticket-wrapper {
    .queue-ticket-wrapper-inner {
      row-gap: 12px;
      .qr-section {
        padding: 25px !important;
        flex-wrap: wrap;
      }
    }
  }
}


@media (max-width: 1280px) {
  .queue-ticket-wrapper {
    .queue-ticket-wrapper-inner {
      .qr-message {
        flex-grow: 1;
        align-items: center;
        text-align: center;
      }
      .qr-section {
        .image-wrapper {
          left: -52px !important;
          bottom: -52px !important;
        }
      }
    }
  }
}


.queue-ticket-title {
  padding: 20px;
  width: 100%;
  span {
    font-size: 44px;
    font-weight: 600;
    text-align: center;
    width: 100%;
  }
}
.queue-ticket-wrapper {
  padding: 42px;
  height: 100%;
  overflow-y: auto;
  .queue-ticket-wrapper-inner {
    display: flex;
    flex-direction: column;
    row-gap: 50px;
    .mobile.platform-ios & {
      row-gap: 12px;
    }
    .qr-message {
      padding-left: var(--checkin-confirmation-qr-code-msg-wrapper-pl);
      padding-right: var(--checkin-confirmation-qr-code-msg-wrapper-pr);
      font-size: var(--checkin-confirmation-regular-text-size);
      font-weight: 600;
      width: var(--checkin-confirmation-qr-code-msg-wrapper-width);
    }
    .qr-section {
      border-radius: 20px;
      padding: 40px;
      display: flex;
      column-gap: 50px;
      flex-wrap: nowrap;
      position: relative;
      .mobile.platform-ios & {
        padding: 25px !important;
      }
      .image-wrapper {
        position: absolute;
        left: -70px;
        bottom: -80px;
        width: var(--checkin-confirmation-pet-img-size);
        height: var(--checkin-confirmation-pet-img-size);
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          object-position: center;
        }
        .mobile.platform-ios & {
          bottom: -50px !important;
        }
      }
      @media (max-width: 1528px) {
        flex-wrap: wrap;
      }
      .qr-code-wrapper {
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        .qr-code-wrapper-inner {
          width: var(--checkin-confirmation-qr-code-size);
          height: var(--checkin-confirmation-qr-code-size);
          border-radius: 8px;
          overflow: hidden;
          padding: 15px;
          background-color: $white;
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        @media (max-width: 1528px) {
          order: 999;
          margin-bottom: 0px;
        }
        @media (max-width: 1280px) {
          order: -1;
          margin-bottom: 50px;
        }
        .mobile.platform-ios & {
          order: 999 !important;
          margin-bottom: 0px !important;
        }
      }
    }
    .qr-section-sub {
      display: flex;
      flex-direction: column;
      row-gap: 5px;
      span {
        font-size: var(--checkin-confirmation-regular-text-size);
        text-align: center;
      }
    }
  }
}
.outline-btn {
  &:before {
    border-color: $dark-blue;
    border-width: 3px;
    border-radius: 20px;
  }
  :deep(.q-btn__content) {
    color: $dark-blue;
  }
}
.top-btn {
  font-size: 32px;
  padding: 18px 20px;
  font-weight: 600;
}
</style>
