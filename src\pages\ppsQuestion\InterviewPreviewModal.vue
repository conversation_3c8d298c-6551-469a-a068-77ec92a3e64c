<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as MtModalHeaderComp from '@/components/MtModalHeader.vue'
import usePPSQuestionTemplateStore from '@/stores/pps-question-template'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'

// Extract component from import
const MtModalHeader =
  'default' in MtModalHeaderComp ? MtModalHeaderComp.default : MtModalHeaderComp

interface DiseaseRelate {
  id_disease: number
  name_disease: string
  name_disease_en: string
}

interface PPSQuestion {
  id_pps_question: number
  text_question: string
  memo_short: string
  flg_start: boolean
  display_order: number
  type_qs_option: number
  template_type: string
  choices: PPSChoice[]
}

interface PPSChoice {
  id_pps_choice: number
  text_choice: string
  text_short?: string
  id_pps_question_next:
    | number
    | null
    | {
        id_pps_question: number
        text_question: string
        memo_short: string
        flg_start: boolean
      }
  display_order: number
}

interface TemplateData {
  id_pps_qs_template: number
  name_button: string
  memo_explanation: string
  flg_composition: boolean
  type_qs_composition: number | null
  disease_relate: DiseaseRelate | null
  merged_questions: PPSQuestion[]
}

interface ApiResponse {
  data: TemplateData
  code: number
  status: number
  message: string
}

const props = defineProps({
  id_pps_qs_template: {
    type: Number,
    required: true
  },
  templateName: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['close'])

const ppsStore = usePPSQuestionTemplateStore()
const loading = ref(false)
const previewData = ref<any>(null)

// Store the template data structure
const templateData = ref<TemplateData>({
  id_pps_qs_template: 0,
  name_button: '',
  memo_explanation: '',
  flg_composition: false,
  type_qs_composition: null,
  disease_relate: null,
  merged_questions: []
})

// Load the preview data
const loadPreviewData = async () => {
  if (!props.id_pps_qs_template) return

  loading.value = true
  try {
    const response = (await ppsStore.fetchInterviewPreview(
      props.id_pps_qs_template
    )) as ApiResponse
    if (response && response.data) {
      templateData.value = response.data
    }
  } catch (error) {
    console.error('Error loading preview data:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
  } finally {
    loading.value = false
  }
}

// Get template type label (pre, main, or post)
const getTemplateTypeLabel = (type: string): string => {
  switch (type) {
    case 'pre':
      return 'Pre-Visit'
    case 'main':
      return 'Main Visit'
    case 'post':
      return 'Post-Visit'
    default:
      return type
  }
}

// Close the modal
const closeModal = () => {
  emits('close')
}

onMounted(() => {
  loadPreviewData()
})
</script>

<template>
  <div class="interview-preview-modal full-height column no-wrap">
    <!-- Header with template name and close button -->
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="q-mr-auto text-h6">
        Interview Preview: {{ templateData.name_button }}
      </q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="content">
      <!-- Loading state -->
      <div v-if="loading" class="flex flex-center full-height">
        <q-spinner size="50px" color="primary" />
        <div class="q-mt-sm">Loading preview...</div>
      </div>

      <!-- Content when loaded -->
      <template v-else>
        <!-- Template metadata -->
        <div class="bg-blue-1 q-mb-md rounded-borders q-pa-md">
          <div class="text-h6">{{ templateData.name_button }}</div>
          <div class="text-subtitle2">{{ templateData.memo_explanation }}</div>

          <div class="q-mt-sm">
            <span class="text-weight-bold">Template ID:</span>
            {{ templateData.id_pps_qs_template }}

            <span class="q-ml-md" v-if="templateData.disease_relate">
              <span class="text-weight-bold">Related Disease:</span>
              {{ templateData.disease_relate.name_disease }}
            </span>
          </div>
        </div>

        <!-- Questions list -->
        <div class="questions-list">
          <div
            v-for="(question, index) in templateData.merged_questions"
            :key="question.id_pps_question"
            class="bg-white q-mb-md rounded-borders question-card q-pa-md"
          >
            <!-- Question header -->
            <div class="q-mb-sm question-header">
              <q-badge
                :color="
                  question.template_type === 'pre'
                    ? 'purple'
                    : question.template_type === 'main'
                    ? 'primary'
                    : 'green'
                "
                class="q-pa-xs"
              >
                {{ getTemplateTypeLabel(question.template_type) }}
              </q-badge>

              <span class="q-ml-sm text-caption">
                <span class="text-weight-bold">ID:</span>
                {{ question.id_pps_question }} |
                <span class="text-weight-bold">Type:</span>
                {{ question.type_qs_option }} |
                <span class="text-weight-bold">Start:</span>
                {{ question.flg_start ? 'Yes' : 'No' }}
              </span>
            </div>

            <!-- Question text -->
            <div class="q-mb-sm text-subtitle1 text-weight-medium">
              {{ index + 1 }}. {{ question.text_question }}
            </div>

            <!-- Question memo -->
            <div class="q-mb-md text-caption" v-if="question.memo_short">
              <span class="text-weight-bold">Memo:</span>
              {{ question.memo_short }}
            </div>

            <!-- Choices -->
            <div class="choices-container">
              <div
                v-for="choice in question.choices"
                :key="choice.id_pps_choice"
                class="bg-grey-2 q-mb-sm rounded-borders choice-item q-pa-sm"
              >
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-weight-medium">
                      {{ choice.text_choice }}
                    </div>
                    <div class="text-caption" v-if="choice.text_short">
                      {{ choice.text_short }}
                    </div>
                  </div>

                  <div v-if="choice.id_pps_question_next" class="text-caption">
                    <span>Next: </span>
                    <q-badge
                      color="teal"
                      v-if="typeof choice.id_pps_question_next === 'object'"
                    >
                      {{ choice.id_pps_question_next.id_pps_question }}
                    </q-badge>
                    <q-badge color="teal" v-else>
                      {{ choice.id_pps_question_next }}
                    </q-badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div
            v-if="templateData.merged_questions.length === 0"
            class="flex flex-center empty-state"
          >
            <q-icon name="help_outline" size="50px" color="grey-5" />
            <div class="q-mt-sm text-grey-6 text-h6">No questions found</div>
          </div>
        </div>
      </template>
    </q-card-section>
  </div>
</template>

<style lang="scss" scoped>
.interview-preview-modal {
  overflow: hidden;

  .content {
    overflow-y: auto;
    height: calc(100vh - 50px);
    padding: 16px 24px;
  }

  .question-card {
    border-left: 4px solid #1976d2;
    margin-bottom: 16px;
  }

  .choice-item {
    border-left: 3px solid #26a69a;
    transition: background-color 0.2s;

    &:hover {
      background-color: #e0e0e0;
    }
  }

  .questions-list {
    max-width: 800px;
    margin: 0 auto;
  }

  .empty-state {
    min-height: 200px;
    flex-direction: column;
  }
}
</style>
