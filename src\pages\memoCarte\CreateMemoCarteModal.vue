<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, onUnmounted, reactive, ref, Ref, watch } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import PdfThumbnail from '@/components/pdf/PdfThumbnail.vue'
import LeftCreateMemoCarteModal from './create/LeftCreateMemoCarteModal.vue'
import FabricMemoCarteModal from './FabricMemoCarteModal.vue'
import AddTextTemplateModal from '@/pages/task/AddTextTemplateModal.vue'
import { useRecordingModal } from './composables/UseRecordingModal'
import { connectWebSocket, useRecording } from './useRecording'
import ViewLargeMemoCarteComparison from './comparison/ViewLargeMemoCarteComparison.vue'
import MtPetInfoDynamic from '@/components/MtPetInfoDynamic.vue'
import UpdatePdfPetCarteSetting from '@/pages/memoCarte/UpdatePdfPetCarteSetting.vue'
import UpdateBulkClinicalDiagnosticProvider from '@/pages/request/detail/UpdateBulkClinicalDiagnosticProvider.vue'
import MtTable2 from '@/components/MtTable2.vue'
import RecordingCountdownModal from './RecordingCountdownModal.vue'
import configRecordingModal from '@/pages/memoCarte/ConfigRecordingModal.vue'
import useCommonStore from '@/stores/common'
import MtFormInputText from '@/components/form/MtFormInputText.vue'

import {
  changeToggleDropdownNames,
  checkServiceHours,
  convertWeightInG,
  copyText,
  dateFormat,
  formatDateTime,
  formatDateWithTime,
  formatNumberWithDecimals,
  getDateTimeNow,
  getDateToday,
  isBitSet,
  openViewAllCartePerDateList,
  scrollAreaHeight
} from '@/utils/aahUtils'
import { removeHtmlTag } from './memoCarteUtils'
import { CliCommon, ClinicalFile, Common, IllnessHistoryType, MedCondition, MemoCarteType, RequestDetailPageType, TextTemplateType } from '@/types/types'
import useCustomerStore from '@/stores/customers'
import useFabricStore from '@/stores/fabrics'
import useClinicCommonStore, { useCliCommonStore } from '@/stores/cli-common'
import useClinicStore from '@/stores/clinics'
import useMemoCarteStore from '@/stores/memo-cartes'
import useIllnessHistoryStore from '@/stores/illness-history'
import useTextTemplateStore from '@/stores/text-template'
import useClinicalFilesStore from '@/stores/clinical-files'
import aahValidations from '@/utils/aahValidations'
import aahMessages from '@/utils/aahMessages'
import { typeBodyWeight, typeCarteConfig, typeDiagnosticInfo, typeMedConditionColor } from '@/utils/enum'
import mtUtils from '@/utils/mtUtils'
import { storeToRefs } from 'pinia'
import { flatMap, forEach, groupBy, isEqual, orderBy, sortBy, uniq, values } from 'lodash'
import { event_bus } from '@/utils/eventBus'
import usePetBioStore from '@/stores/pet-bios'
import selectOptions from '@/utils/selectOptions'
import OptionModal from '@/components/OptionModal.vue'
import useMedConditionStore from '@/stores/med-conditions'
import MtFormInputNumber from '@/components/form/MtFormInputNumber.vue'
import useConversationStore from '@/stores/Conversation'
import koekaruApi, { getInstance, secretKey } from '@/boot/axiosKoekaru'
import { nanoid } from 'nanoid'
import usePetStore from '@/stores/pets' // Pastikan import ini ada
import SparkMD5 from 'spark-md5'
import PreviewOtherCreateMemoCarteModal from './create/PreviewOtherCreateMemoCarteModal.vue'
import { useKoekaruModalStore } from '@/stores/koekaru-modal'
import useCarteConfigStore from '@/stores/carteConfig'
import useLabSetStore from '@/stores/lab-sets'
import useLabResultStore from '@/stores/lab-results'
import useItemServiceUnitStore from '@/stores/item-service-units'
import useCategoryStore from '@/stores/categories'

type memoFieldsType = {
  field: string
  name: string
  label: string
}

const emits = defineEmits(['close'])
const dataChecker = () => {
  const current = currentData.value
  return (
    isEqual(current?.data, data.value) &&
    isEqual(current.petBioInfoData, petBioInfoData.value) &&
    isEqual(current?.medConditionData, medConditionData) &&
    isEqual(current.petSelected, petSelected.value) &&
    isEqual(current.customerSelected, customerSelected.value) &&
    isEqual(current.multipleImage, multipleImage.value) &&
    isEqual(current.id_pet_illness_history, id_pet_illness_history.value)
  )
}

const closeModal = async (direct: boolean = true) => {
  if (!direct) {
    if (!dataChecker()) {
      const confirmation = await mtUtils.confirm(
        '未保存の内容があります。 \n 保存しますか？',
        '確認',
        '保存して閉じる',
        null,
        null,
        null,
        {
          show: false,
          callBackFun: Function
        },
        true,
        '保存しないで閉じる',
        true
      )

      if (confirmation) {
        if (
          !data.value.id_employee ||
          !data.value.datetime_memo_carte ||
          !data.value.id_cli_common ||
          !id_pet_illness_history.value ||
          id_pet_illness_history.value.length === 0
        ) {
          await mtUtils.autoCloseAlert("必須項目を入力してください。");
          return;
        }
        await mtUtils.autoCloseAlert(aahMessages.success)
        submit()
      }
    }
  }

  fabricStore.resetFabricOption()
  event_bus.emit('reloadParent')
  emits('close')
}

const MIN_WIDTH_FOR_ICONS_TEXT = 1400
const showHeaderIconsText = ref(window.innerWidth >= MIN_WIDTH_FOR_ICONS_TEXT)

const customerStore = useCustomerStore()
const fabricStore = useFabricStore()
const clinicCommonStore = useClinicCommonStore()
const clinicStore = useClinicStore()
const illnessHistoryStore = useIllnessHistoryStore()
const textTemplateStore = useTextTemplateStore()
const memoCarteStore = useMemoCarteStore()
const clinicalFilesStore = useClinicalFilesStore()
const cliCommonStore = useCliCommonStore()
const medConditionStore = useMedConditionStore()
const petBioStore = usePetBioStore()
const carteConfigStore = useCarteConfigStore()
const labSetStore = useLabSetStore()
const commonStore = useCommonStore()
const labResultStore = useLabResultStore()
const itemServiceUnitStore = useItemServiceUnitStore()
const categoryStore = useCategoryStore()

const { getPetBios } = storeToRefs(petBioStore)
const { getFabric } = storeToRefs(fabricStore)
const { getIllnessHistorys, getLeftSideIllnessHistoryList } =
  storeToRefs(illnessHistoryStore)
const { getCliCommonTypeCarteSourceList, getCliCommonTypeMemoCarteList } =
  storeToRefs(cliCommonStore)
const { getTemplates } = storeToRefs(textTemplateStore)
const { openRecordingSettingsModal } = useRecordingModal()
const {
  seconds,
  tempFullTranscript,
  tempTranscript,
  flg_auto_memocarte_ai,
  michrophoneList,
  microphonesListDefault,
  recordConfig,
  questionTemplatesList,
  questionTemplatesListDefault,
  pauseRecording,
  resumeRecording,
  saveCurrentRecord,
  isExternalMic
} = useRecording()
const petCartePdfConfirmationDialog = ref(false)
const employeeRef = ref(null);
const dateRef = ref(null);
const pullDownRef = ref(null);
const diseaseRef = ref(null);
const isEdit = ref(false)
const petSelected = ref()
const customerSelected = ref()
const memoFieldsHeaderRef = ref([])
const labResultListRef = ref()
const foreColor: Ref<string[]> = ref([
  '#ffff00',
  '#ffff00',
  '#ffff00',
  '#ffff00'
])
const authUser = JSON.parse(localStorage.getItem('auth'))?.authUser
const currentMemoCarteScrollAreaRef = ref(null)
const selectedMemo = ref('')
const previewOtherCarte = ref([])
const isOpenLeftSidebar = ref(false)
const viewOnlyMemoCarte = ref(false)
const currentFocusedMemo = ref<string>('memo_other'),
  fileInput = ref()
const flgEditorToolbar = ref<boolean>(true)
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))
const clinicFiles = ref([])
const clinicCommonList = ref([]),
  disableCliCommon = ref(false)
const typeCarteConfigList = ref([])
const addTemplateModalFlg = ref(false),
  textTemplatesList = ref([])
const disableClinicalFileBtn = ref(false)
const isDragging = ref(false)
const multipleImage = ref([])
const deleteImage = ref([])
const filePaths = ref([])
const fileHashes = new Set<string>()
const previewImage = ref(false)
const removedConditionData = ref([])
let observer: MutationObserver | null = null
const id_pet_illness_history = ref([]) // Separate ref for reactivity
const currentData = ref()
const memoFieldsRef = ref([])
const labResultList = ref([] as any[])
const schemaTextTemplateList = ref([] as TextTemplateType[])
const currentTemplate = ref('text')
const currentTemplateIndex = ref(null)
const carte_config_id = ref(null)
const id_item_service_unit = ref(null)
const data = ref({
  id_pet: '',
  id_request: '',
  id_customer: '',
  id_employee: defaultEmployee,
  datetime_memo_carte: getDateTimeNow(),
  id_pet_illness_history: null,
  id_clinic: Number(localStorage.getItem('id_clinic')) || '',
  memo_sbj: '',
  memo_obj: '',
  memo_ass: '',
  memo_other: '',
  id_cli_common: -1,
  illnessHistoryOptions: null,
  type_input: 2
})

const petBioInfoData = ref({
  id_pet: null,
  id_customer: null,
  val_weight: null,
  type_body_weight: 1,
  val_temperature: null,
  val_respiration_rate: null,
  val_heartbeat_rate: null,
  val_pressure_systolic: null,
  val_pressure_diastolic: null,
  val_pressure_mean_arterial: null,
  val_blood_oxygen_level: null,
  val_blood_carbon_dioxide_level: null,
  flg_panting: false,
  id_clinic: localStorage.getItem('id_clinic') || ''
})

const labResultColumns = [
  { name: 'name_lab', label: '項目', field: 'name_lab', align: 'center', style: 'width: 35%', headerStyle: 'width: 35%' },
  { name: 'qualifier', label: '符号', field: 'qualifier', align: 'center', style: 'width: 25px' },
  { name: 'val_result', label: '値', field: 'val_result', align: 'center', style: 'width: 25px' },
]
let medConditionData = reactive([])

const props = withDefaults(
  defineProps<{
    date_insert?: string
    datetimeInsert?: string
    data_cartes?: object
    clinical_file?: array
    id_request: string
    id_customer: string
    id_pet: string
    number_request?: string
    id_pet_illness_history?: string
    id_memo_carte?: number
    memo_other?: string
    id_employee?: string
    requestDetailPage?: RequestDetailPageType
    duplciateCart?: { value: boolean }
    autoSaveOnMount: boolean
    callBackRefresh: Function | null
  }>(),
  {
    date_insert: '',
    datetimeInsert: '',
    data_cartes: {},
    clinical_file: [],
    id_request: '',
    number_request: '',
    id_customer: '',
    id_pet: '',
    id_pet_illness_history: '',
    id_memo_carte: -1,
    memo_other: '',
    id_employee: '',
    requestDetailPage: () => {
      return {} as RequestDetailPageType
    },
    duplciateCart: () => {
      return { value: false }
    },
    autoSaveOnMount: false,
    callBackRefresh: null
  }
)

const memoFields: memoFieldsType[] = [
  { field: 'memo_sbj', name: 'memo_sbj', label: 'S: 主観' },
  { field: 'memo_obj', name: 'memo_obj', label: 'O: 客観' },
  { field: 'memo_ass', name: 'memo_ass', label: 'A: 評価' },
  { field: 'memo_other', name: 'memo_other', label: 'P: 計画 / 処置他' }
]

const typeMemoCarte = computed(() =>
  getCliCommonTypeCarteSourceList.value.map((v: CliCommon) => ({
    ...v,
    label: v.name_cli_common,
    value: v.id_cli_common
  }))
)
const cliMedCondition = (code_cli_common: string) =>
  getCliCommonTypeMemoCarteList.value.find(
    (v) => v.code_func1 == code_cli_common
  )
const sanitizeInput = (field) => {
  const content = data.value[field]
  // Check if the content is just a <br> or empty
  if (content.trim() === '<br>' || content.trim() === '') {
    data.value[field] = '' // Set to an empty string
  }
}

const setMemoFieldRef = (el) => {
  if (el) {
    memoFieldsHeaderRef.value.push(el)
  }
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: 'カルテ印刷',
      name: 'pet_carte_pdf',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    },
    {
      title: '削除',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
  let selectedOption = menuOptions.find((i) => i.isChanged == true)
  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm(aahMessages.delete_ask, aahMessages.delete)
        .then(async (confirmation) => {
          if (confirmation) {
            const payload = {
              group_carte: props.data_cartes?.memo_carte_list?.[0]?.group_carte
                ? props.data_cartes?.memo_carte_list?.[0]?.group_carte
                : null,
              id_pet: data.value.id_pet
            }
            await memoCarteStore.destroyMemoCarteGrouped(payload)
            await event_bus.emit('searchMemoCarte')
            mtUtils.autoCloseAlert(aahMessages.success)
            emits('close')
          }
        })
    }
    else if (selectedOption.name == 'pet_carte_pdf') {
      petCartePdfConfirmationDialog.value = true
    }
  }
}

const onPasteEditor = (event) => {
  event.preventDefault();
  event.stopPropagation();

  // Get plain text and HTML from clipboard
  const htmlContent = event.clipboardData.getData('text/html') || '';
  const plainText = event.clipboardData.getData('text/plain');

  let sanitizedContent = htmlContent || plainText;

  // Sanitize and format the content
  sanitizedContent = sanitizedContent
    .replace(/<b>(.*?)<\/b>/g, '<b>$1</b>')
    .replace(/<font color="(.*?)">(.*?)<\/font>/g, '<font color="$1">$2</font>')
    .replace(/\n/g, '<br>');

  // Insert the sanitized content at the current cursor position
  document.execCommand('insertHTML', false, sanitizedContent);
}

const selectDefaultEmployee = () => {
  data.value.id_employee = defaultEmployee
}

const sortedMedCondition = (medConditionArr: any) => {
  const allMedConditionList = clinicCommonStore.getAllCliCommonMedConditionOptionList

  // Create a lookup map for display_order
  const orderMap = Object.fromEntries(allMedConditionList.map(item => [item.code_cli_common, item.display_order]));
  const sortedMedConditionArr = medConditionArr.sort((a, b) => orderMap[a.code_cli_common] - orderMap[b.code_cli_common])

  return sortedMedConditionArr
}

const submit = async (
  closeModal: boolean = true,
  autoSave: boolean = false,
  showSuccessMessage: boolean = true
) => {
  let rightCenterMenuNeedUpdated = false
  const { id_pet, id_customer } = memoCarteStore.currentPage;
  const petId = id_pet || props.id_pet;
  const customerId = id_customer || props.id_customer;
  const datetime = formatDateWithTime(
    data.value.datetime_memo_carte,
    'YYYY/MM/DD HH:mm:ss'
  )

  let { payload, petBioPayload } = await saveCurrentData(autoSave)
  let promises = []
  if (multipleImage.value.length > 0) {
    const idPetIllnessHistoryString = id_pet_illness_history?.value?.join(',') || ''
    multipleImage.value.forEach((item, index) => {
      item.datetime_receive = datetime
      item.id_pet_illness_history = idPetIllnessHistoryString
      item.group_carte = isEdit.value && !props.autoSaveOnMount
        ? props.data_cartes?.memo_carte_list?.[0]?.group_carte
        : null
      item.file_index = index
      if (item.old === undefined || item.old === null || item.old === false) {
        item.old = true
        promises.push(clinicalFilesStore.submitClinicalFile(item))
      }
      // event_bus.emit('reloadParent')
    })

    currentData.value.multipleImage = multipleImage.value
  }

  if (isEdit.value) {
    const promisesEdit = []
    // MED CONDITION
    if (medConditionData) {
      const cliCommonIds = new Set();
      sortBy(
        flatMap(medConditionData) // UPDATE EXISITING MED CONDITION
          .filter((v: MedCondition) => v && v.id_med_condition && !cliCommonIds.has(v.code_cli_common) && cliCommonIds.add(v.code_cli_common)),
        'display_order'
      ).forEach((item: MedCondition) => {
        const cliCommonTypeMemoCarte = getCliCommonTypeMemoCarteList.value.find(
          (v) => v.code_func1 == item?.code_cli_common
        )
        const medItems = {
          ...item,
          id_pet: props.id_pet,
          id_customer: props.id_customer,
          id_employee_record: data.value.id_employee,
          datetime_record: datetime,
          display_order: cliCommonTypeMemoCarte?.display_order || 0
        }
        promisesEdit.push(
          medConditionStore.updateMedCondition(item.id_med_condition, medItems)
        )
      })
      const newMedCondition = sortBy(
        flatMap(medConditionData) // ADD NEW MED CONDITION
          .filter((v: MedCondition) => v && !v.id_med_condition && !cliCommonIds.has(v.code_cli_common) && cliCommonIds.add(v.code_cli_common))
          .map((item: MedCondition) => {
            const cliCommonTypeMemoCarte =
              getCliCommonTypeMemoCarteList.value.find(
                (v) => v.code_func1 == item?.code_cli_common
              )
            return {
              ...item,
              flg_func1: item.flg_func1,
              id_pet: props.id_pet,
              id_customer: props.id_customer,
              id_employee_record: data.value.id_employee,
              datetime_record: datetime,
              datetime_insert: datetime,
              group_carte: isEdit.value && !props.autoSaveOnMount
                ? props.data_cartes?.memo_carte_list?.[0]?.group_carte
                : null,
              display_order: cliCommonTypeMemoCarte?.display_order || 0
            }
          }),
        'display_order'
      )
      if (newMedCondition.length > 0) {
        promisesEdit.push(
          memoCarteStore.submitMemoCarteV1({
            med_condition_list: newMedCondition
          })
        )
      }
      if (removedConditionData) {
        removedConditionData.value.forEach((v) => {
          if (v) {
            const payloadSend = data.value.id_employee
            promisesEdit.push(
              medConditionStore.destroyMedCondition(v, payloadSend)
            )
          }
        })
        removedConditionData.value = []
      }
    }

    // UPDATE LAB RESULT
    if (labResultList.value.length > 0) {
      const filterLabResult = labResultList.value.filter((lab) => lab?.id_lab_result && (lab?.val_result || lab?.qualifier))
      if (filterLabResult.length > 0) {
        const dataForSubmitLabResult = filterLabResult
          .map((lab) => ({
            id_lab_result: lab?.id_lab_result,
            name_lab: lab?.lab?.name_lab,
            name_lab_en: lab?.lab?.name_lab_en,
            id_category2_lab: lab?.id_category2_lb1 || lab?.lab?.id_category2_lab,
            id_category2_lb2: lab?.id_category2_lb2,
            id_lab: parseInt(lab?.lab?.id_lab),
            qualifier: lab?.qualifier,
            val_result: lab?.val_result?.toString()?.replace(',', ''),
            id_pet: props.id_pet,
            id_pet_illness_history: id_pet_illness_history.value?.[0],
            id_employee_registered: data.value.id_employee,
            id_clinic: JSON.parse(localStorage.getItem('id_clinic')),
            id_lab_set: lab?.id_lab_set ? lab?.id_lab_set : null,
            name_unit_result: commonStore.getCommonUnitOptionList.find((item: Common) => item.id_common == lab?.id_cm_unit)?.name_common,
            id_cm_unit: lab?.id_cm_unit,
            seq_processed: 1,
            datetime_registered: datetime,
            flg_delete: lab?.val_result || lab?.qualifier ? false : true,
          }))

        promisesEdit.push(labResultStore.updateLabResult({ lab_result: dataForSubmitLabResult }))
      }

      const filterLabResultNew = labResultList.value.filter((lab) => !lab?.id_lab_result && (lab?.val_result || lab?.qualifier))
      if (filterLabResultNew.length > 0) {
        const dataForSubmitLabResultNew = filterLabResultNew
          .map((lab) => ({
            id_lab_result: lab?.id_lab_result,
            name_lab: lab?.lab?.name_lab,
            name_lab_en: lab?.lab?.name_lab_en,
            id_category2_lab: lab?.id_category2_lb1 || lab?.lab?.id_category2_lab,
            id_category2_lb2: lab?.id_category2_lb2,
            id_lab: parseInt(lab?.lab?.id_lab),
            qualifier: lab?.qualifier,
            val_result: lab?.val_result?.toString()?.replace(',', ''),
            id_pet: props.id_pet,
            id_pet_illness_history: id_pet_illness_history.value?.[0],
            id_employee_registered: data.value.id_employee,
            id_clinic: JSON.parse(localStorage.getItem('id_clinic')),
            id_lab_set: lab?.id_lab_set ? lab?.id_lab_set : null,
            name_unit_result: commonStore.getCommonUnitOptionList.find((item: Common) => item.id_common == lab?.id_cm_unit)?.name_common,
            id_cm_unit: lab?.id_cm_unit,
            seq_processed: 1,
            datetime_registered: datetime,
            flg_delete: lab?.val_result || lab?.qualifier ? false : true,
            group_carte: isEdit.value && !props.autoSaveOnMount ? props.data_cartes?.memo_carte_list?.[0]?.group_carte : null
          }))
        promisesEdit.push(labResultStore.submitLabResult({ lab_result: dataForSubmitLabResultNew }))
      }
    }
    // END UPDATE LAB RESULT

    // PET BIO INFO
    if (petBioPayload.id_pet_bio_info) {
      promisesEdit.push(
        mtUtils.callApiEx({
          method: selectOptions.reqMethod.PUT,
          url: `/pet_bio_info/${petBioPayload.id_pet_bio_info}`,
          params: {
            ...petBioPayload,
            id_pet: props.id_pet,
            id_customer: props.id_customer,
            datetime_measure: datetime,
            datetime_insert: datetime,
            group_carte: isEdit.value && !props.autoSaveOnMount ? props.data_cartes?.memo_carte_list?.[0]?.group_carte : null
          },
          silent: autoSave
        })
      )
    } else {
      if (
        petBioPayload.val_weight ||
        petBioPayload.val_temperature ||
        petBioPayload.val_pressure_systolic ||
        petBioPayload.val_pressure_diastolic ||
        petBioPayload.val_pressure_mean_arterial ||
        petBioPayload.val_blood_oxygen_level ||
        petBioPayload.val_blood_carbon_dioxide_level ||
        petBioPayload.val_respiration_rate ||
        petBioPayload.val_heartbeat_rate
      )
        promisesEdit.push(
          mtUtils.callApiEx({
            method: selectOptions.reqMethod.POST,
            url: `/pet_bio_info`,
            params: {
              ...petBioPayload,
              id_pet: props.id_pet,
              id_customer: props.id_customer,
              datetime_measure: datetime,
              datetime_insert: datetime,
              group_carte: isEdit.value && !props.autoSaveOnMount
                ? props.data_cartes?.memo_carte_list?.[0]?.group_carte
                : null
            },
            silent: autoSave
          })
        )
    }

    // CLINICAL FILE
    if (multipleImage.value.length > 0) {
      const idPetIllnessHistoryString = id_pet_illness_history?.value?.join(',') || ''
      multipleImage.value.forEach((item) => {
        item.datetime_receive = datetime
        item.id_pet_illness_history = idPetIllnessHistoryString
        item.group_carte = isEdit.value && !props.autoSaveOnMount
          ? props.data_cartes?.memo_carte_list?.[0]?.group_carte
          : null
        if (item.old === undefined || item.old === null || item.old === false) {
          item.old = true
          promisesEdit.push(clinicalFilesStore.submitClinicalFile(item))
        }
      })
      currentData.value.multipleImage = multipleImage.value
    }
    if (deleteImage.value?.length > 0) {
      deleteImage.value.forEach((id_clincal_file) => {
        promisesEdit.push(
          clinicalFilesStore.destroyClinicalFile(id_clincal_file)
        )
      })
      currentData.value.multipleImage = multipleImage.value
    }

    // CALL UPDATE API
    if (data.value.id_memo_carte != -1) {
      if (autoSave || !showSuccessMessage) {
        await mtUtils.promiseAllSilently([
          useMemoCarteStore().updateMemoCarte(data.value.id_memo_carte, {
            ...data.value
          }),
          ...promisesEdit
        ])
      } else {
        await mtUtils.promiseAllWithLoaderMsg(
          [
            useMemoCarteStore().updateMemoCarte(data.value.id_memo_carte, {
              ...data.value
            }),
            ,
            ...promisesEdit
          ],
          '更新しています...'
        )
      }
    } else {
      const med_conditions =
        props?.data_cartes?.medical_condition?.map((condition, index) => ({
          code_cli_common: condition.code_cli_common,
          id_pet: props.id_pet,
          id_customer: props.id_customer,
          id_employee_record: data.value.id_employee,
          datetime_record: datetime,
          flg_func1: true,
          code_func1: null,
          display_order: index
        })) || []

      const { id_memo_carte, ...filteredData } = data.value
      promisesEdit.push(
        memoCarteStore
          .submitMemoCarteV1({
            memo_carte: filteredData,
            med_condition_list: med_conditions
          })
          .then((response) => {
            data.value.id_memo_carte = response.data.data.id_memo_carte
          })
          .catch((error) => {
            console.error('API Error:', error)
          })
      )
    }

    currentData.value.data = data.value
    currentData.value.petBioInfoData = petBioInfoData.value

    currentData.value.id_pet_illness_history = id_pet_illness_history.value
    currentData.value.medConditionData.splice(
      0,
      currentData.value.medConditionData.length,
      ...medConditionData
    )

    // NEW SIMPLY LOGIC FOR MIDDLE REQUEST DETAIL AND UPDATE OTHER RELATED DATA
    await memoCarteStore.fetchCartesForMiddleRequestDetail({
      id_pet: petId,
      id_customer: customerId,
      group_carte: props.data_cartes?.memo_carte_list?.[0]?.group_carte
    })

    if (!autoSave) {
      // IF DATA IS RELATED TO CLINICAL FILE, THEN RELOAD LEFT SIDE
      if (multipleImage.value.length > 0 || deleteImage.value?.length > 0) {
        event_bus.emit('reloadLeft')
      }
    }

    if (!closeModal) {
      // UPDATE UI MED CONDITION DATA IF NON-CLOSE MODAL BUTTON IS CLICKED
      initMedCondition(
        memoCarteStore.getFilteredMemoCartesV1[dateFormat(datetime)].others[
          datetime
        ].medical_condition,
        true
      )
    }
    if (props.callBackRefresh) {
      event_bus.emit('refreshAllCartePerDate')
    }
    if (closeModal) emits('close')

    memoCarteStore.setCurrentPage({
      id_request: '',
      id_pet: '',
      id_customer: ''
    });

    return
  }

  let sdLabResultData = [] as any[]
  // SUBMIT LAB RESULT
  if (labResultList.value.length > 0) {
    const filterLabResult = labResultList.value.filter((lab) => lab?.val_result || lab?.qualifier)
    const isuList = [...new Set(filterLabResult.map((v) => v.id_item_service_unit))]
    const itemServiceUnitList = carteConfigStore.getCarteConfig.child_carte_config_list.filter((v) => isuList.includes(v.id_item_service_unit))
    if (itemServiceUnitList.length > 0) {
      await Promise.all(itemServiceUnitList.map(async (v) => {
        await itemServiceUnitStore.fetchItemServiceUnit(v.id_item_service_unit)
        const itemServiceUnit = itemServiceUnitStore.getItemServiceUnit

        const labResultDateTime = formatDateWithTime(getDateTimeNow(), 'YYYY/MM/DD HH:mm:ss')
        const dataForSubmitLabResult = filterLabResult
          .filter((lab) => lab?.id_item_service_unit == v.id_item_service_unit)
          .map((lab, index) => ({
            name_lab: lab?.lab?.name_lab,
            name_lab_en: lab?.lab?.name_lab_en,
            id_category2_lab: lab?.id_category2_lb1 || lab?.lab?.id_category2_lab,
            id_category2_lb2: lab?.id_category2_lb2,
            id_lab: parseInt(lab?.lab?.id_lab),
            qualifier: lab?.qualifier,
            val_result: lab?.val_result?.toString()?.replace(',', ''),
            id_pet: props.id_pet,
            id_pet_illness_history: id_pet_illness_history.value?.[0],
            id_employee_registered: data.value.id_employee,
            id_clinic: JSON.parse(localStorage.getItem('id_clinic')),
            id_lab_set: lab?.id_lab_set ? lab?.id_lab_set : null,
            name_unit_result: commonStore.getCommonUnitOptionList.find((item: Common) => item.id_common == lab?.id_cm_unit)?.name_common,
            id_cm_unit: lab?.id_cm_unit,
            seq_processed: index + 1,
            datetime_registered: labResultDateTime,
          }))

        const dataForSubmitServiceDetail = {
          datetime_service_start: getDateTimeNow(),
          datetime_service_end: getDateTimeNow(),
          id_pet: props.id_pet,
          id_request: props.id_request,
          id_customer: props.id_customer,
          id_pet_illness_history: id_pet_illness_history.value,
          id_item_service_unit: v?.id_item_service_unit,
          code_category2: itemServiceUnit?.code_category2,
          id_item_service: itemServiceUnit?.id_item_service,
          name_item_service: itemServiceUnit?.name_item_service
            ? itemServiceUnit?.name_item_service
            : itemServiceUnit?.name_service_item_unit,
          id_pet_illness_history: itemServiceUnit?.id_pet_illness_history,
          flg_complete: false,
          id_item_service_unit: itemServiceUnit?.id_item_service_unit,
          flg_surgery: itemServiceUnit?.flg_surgery,
          flg_schedule: itemServiceUnit?.flg_schedule,
          flg_anesthesia: itemServiceUnit?.flg_anesthesia,
          flg_pet_custody_control: itemServiceUnit?.flg_pet_custody_control,
          type_service: itemServiceUnit?.type_service
            ? itemServiceUnit?.type_service
            : itemServiceUnit?.id_item_service.type_service,
          id_clinic: data.value.id_clinic,
          lab_result_list: dataForSubmitLabResult
        }
        sdLabResultData = dataForSubmitServiceDetail
        // promises.push(labResultStore.submitLabResultByCartes(dataForSubmitServiceDetail))
        rightCenterMenuNeedUpdated = true
      }))
    }
  }

  if (
    payload &&
    (payload.memo_carte.memo_ass != '' ||
      payload.memo_carte.memo_obj != '' ||
      payload.memo_carte.memo_other != '' ||
      payload.memo_carte.memo_sbj != '' ||
      payload.med_condition_list.length > 0 ||
      payload.pet_bio_info)
  ) {
    promises.push(memoCarteStore.submitMemoCarteV1({ ...payload, service_detail: sdLabResultData }))
    let response
    if (!showSuccessMessage) {
      console.log('called A:');
      response = await mtUtils.promiseAllSilently(promises)
    } else {
      response = await mtUtils.promiseAllWithLoaderMsg(
        promises,
        '更新しています...'
      )
    }
    currentData.value.data = data.value
    currentData.value.medConditionData = medConditionData
    currentData.value.petBioInfoData = petBioInfoData.value
    currentData.value.id_pet_illness_history = id_pet_illness_history.value
    disableClinicalFileBtn.value = false
    isDragging.value = false
    await memoCarteStore.fetchMemoCarteV1({
      id_pet: petId,
      id_customer: customerId,
      page_size: 200
    })
    if (petBioPayload.val_weight) {
      usePetBioStore().fetchPetBio({
        id_pet: props.id_pet,
        id_customer: props.id_customer,
        fetch_weight: true
      })
    }
    event_bus.emit('reloadLeft')
    if (rightCenterMenuNeedUpdated) {
      event_bus.emit('searchMemoCarte')
      event_bus.emit('reloadRight', true)
    }

    if (!closeModal || autoSave) {
      // UPDATE UI MED CONDITION DATA IF NON-CLOSE MODAL BUTTON IS CLICKED
      initMedCondition(
        memoCarteStore.getFilteredMemoCartesV1[dateFormat(datetime)].others[
          datetime
        ].medical_condition,
        true
      )
      if (response && typeof response != 'string') {
        isEdit.value = true

        const id_memo_carte =
          response?.[response.length - 1]?.data?.data?.memo_carte?.id_memo_carte
        if (id_memo_carte) data.value.id_memo_carte = id_memo_carte

        const id_pet_bio_info =
          response?.[response.length - 1]?.data?.data?.pet_bio_info
            ?.id_pet_bio_info
        if (id_pet_bio_info)
          petBioInfoData.value.id_pet_bio_info = id_pet_bio_info
      }
    }

    if (!autoSave && showSuccessMessage) mtUtils.autoCloseAlert(aahMessages.success)
    if (closeModal) emits('close')
  }
}

const setChildRef = (index) => (el) => {
  if (el) {
    memoFieldsRef.value[index] = el
  } else {
    memoFieldsRef.value.splice(index, 1)
  }
}

const openFabricMemoCarteModal = async () => {
  await mtUtils.mediumPopup(FabricMemoCarteModal, {
    id_memo_carte: props.id_memo_carte,
    additional_image: data.value?.files,
    id_customer: data.value?.id_customer,
    id_pet: data.value?.id_pet,
    popup: {
      persistent: true
    }
  })
  if (fabricStore.getFabricOption.length > 0) {
    fabricStore.getFabricOption.forEach((v) => {
      const images = {
        ...v,
        id_pet_illness_history: data.value.id_pet_illness_history?.[0]
          ? data.value.id_pet_illness_history?.[0]
          : null,
        datetime_receive: formatDateWithTime(
          data.value.datetime_memo_carte,
          'YYYY/MM/DD HH:mm:ss'
        ),
        name_file: formatDateWithTime(
          getDateTimeNow(),
          'FileMemoCarte_YYYYMMDD_HHmmss.jpeg'
        ),
        type_file: 1,
        type_receive_format: 2,
        type_provider: 1
      }
      multipleImage.value.push(images)
      filePaths.value.push(v.file_path)
    })
    fabricStore.resetFabricOption()
  }
}

const colorClicked = (index) => {
  const edit = memoFieldsRef.value[index]
  edit.runCmd('foreColor', foreColor.value[index])
  edit.focus()
}

const ALLOWED_TYPES = [
  'image/',
  'video/',
  'audio/',
  'text/csv',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
]
// Helpers
const isAllowedFileType = (file: File): boolean => {
  return ALLOWED_TYPES.some(
    (type) => file.type.startsWith(type) || file.type.includes(type)
  )
}

const hashFile = async (file: File): Promise<string> => {
  const buffer = await file.arrayBuffer()
  return SparkMD5.ArrayBuffer.hash(buffer)
}
const isDuplicateFile = async (file: File): Promise<boolean> => {
  const hash = await hashFile(file)
  return fileHashes.has(hash)
}
const addFileHash = async (file: File): Promise<void> => {
  const hash = await hashFile(file)
  fileHashes.add(hash)
}

const determineFileType = (file: File): number => {
  if (file.type.startsWith('image/')) return 1
  if (file.type.startsWith('video/')) return 2
  return 99
}

const createPayload = (file: File): any => {
  let payload = {
    datetime_receive: formatDateWithTime(getDateTimeNow(), 'YYYY/MM/DD HH:mm:ss'),
    id_pet: props.id_pet,
    id_customer: props.id_customer,
    id_pet_illness_history: data.value.id_pet_illness_history?.[0],
    name_file: file.name,
    file_index: multipleImage.value.length,
    file_path: file,
    type_file: determineFileType(file),
    type_diagnostic_info: null,
    type_provider: null
  }
  payload.filePathUi = URL.createObjectURL(file)
  return payload
}

const onFileChange = async (e: any, fromDrag = false) => {
  let files
  if (fromDrag) {
    files = e
  } else {
    const target = e.target as HTMLInputElement
    files = target.files
  }
  if (!files?.length) return

  const fileList: File[] = Array.from(files)

  for (const file of fileList) {
    if (!isAllowedFileType(file)) {
      console.error(`File "${file.name}" has an unsupported file type: ${file.type}`)
      continue
    }

    if (await isDuplicateFile(file)) {
      console.warn(`File "${file.name}" has already been added.`)
      continue
    }

    let payload = createPayload(file)
    payload = clinicalFilesStore.addPrefixFileName(payload)

    previewImage.value = true
    multipleImage.value.push(payload)
    filePaths.value.push(URL.createObjectURL(file))
    await addFileHash(file)
  }
}

const onFileRemoved = (index: number, id_clinical_file: number) => {
  multipleImage.value.splice(index, 1)
  filePaths.value.splice(index, 1)
  deleteImage.value.push(id_clinical_file)
  currentData.value.multipleImage = multipleImage.value
}

const typeLabUnitName = (value) => {
  return commonStore.getCommonUnitOptionList.find(item => item.id_common == value)?.name_common
}

const fetchCommonCliRecords = async (code_func1, flg_func1, index: number) => {
  // ======= This is to check duplicated data =========
  let isCodeCommmonFetched: boolean = false
  if (
    clinicCommonList.value[index] &&
    clinicCommonList.value[index].length > 0
  ) {
    clinicCommonList.value[index].forEach((item) => {
      item.clinicCommonData.forEach((codeCliCommon) => {
        if (codeCliCommon.code_cli_common == code_func1) {
          isCodeCommmonFetched = true
        }
      })
    })
  }
  if (isCodeCommmonFetched) return false // ====== Return if duplicated
  
  disableCliCommon.value = true

  if (isEdit.value) {
    const group = clinicCommonStore.getAllCliCommonMedConditionOptionList.find((cli) => {
      return cli.code_func1 === code_func1
    }) ?? {}
    let clinicCommonData = [
      ...clinicCommonStore.getAllCliCommonMedConditionOptionList
        .filter(
          (v) =>
            v.code_cli_common === code_func1 &&
            v.date_start <= getDateToday() &&
            v.date_end >= getDateToday()
        )
        .sort((a, b) => a.display_order - b.display_order)
    ]

    if (!medConditionData[index]) medConditionData[index] = []
    const displayOrderMap = {};
    if (clinicCommonList.value[index]) {
      clinicCommonList.value[index].forEach(item => {
        item.clinicCommonData.forEach(data => {
          displayOrderMap[parseInt(data.code_cli_common)] = item.display_order;
        });
      });
    }
    medConditionData[index].forEach(item => {
      if (displayOrderMap[item.code_cli_common] !== undefined) {
        item.display_order = displayOrderMap[item.code_cli_common];
      }
    });
    medConditionData[index].push({
      memo_record: null,
      code_func1: null,
      text1: null,
      flg_func1: flg_func1,
      code_cli_common: code_func1,
      display_order: group?.display_order || 0
    })
    medConditionData[index] = sortBy(medConditionData[index], 'display_order')

    if (!clinicCommonList.value[index]) clinicCommonList.value[index] = []
    clinicCommonList.value[index].push({
      groupName: group.name_cli_common,
      flg_func1: group.flg_func1,
      display_order: group?.display_order || 0,
      clinicCommonData
    })
    clinicCommonList.value[index] = sortBy(clinicCommonList.value[index], 'display_order')

    disableCliCommon.value = false
    return
  }

  if (clinicCommonStore.getAllCliCommonMedConditionOptionList.length > 0) {
    const group = clinicCommonStore.getAllCliCommonMedConditionOptionList.find((cli) => {
      return cli.code_func1 == parseInt(code_func1)
    }) ?? []
    let clinicCommonData = [
      ...clinicCommonStore.getAllCliCommonMedConditionOptionList
        .filter(
          (v) =>
            v.code_cli_common == parseInt(code_func1) &&
            v.date_start <= getDateToday() &&
            v.date_end >= getDateToday()
        )
        .sort((a, b) => a.display_order - b.display_order)
    ]
    console.log('clinicCommonData', clinicCommonStore.getAllCliCommonMedConditionOptionList
        .map((v) => parseInt(v.code_cli_common)))

    if (!clinicCommonList.value[index]) clinicCommonList.value[index] = []
    clinicCommonList.value[index].push({
      groupName: group.name_cli_common,
      flg_func1: group.flg_func1,
      display_order: group?.display_order || 0,
      clinicCommonData
    })
    clinicCommonList.value[index] = sortBy(clinicCommonList.value[index], 'display_order')
    if (!medConditionData[index]) medConditionData[index] = []
    medConditionData[index].push({
      memo_record: null,
      code_func1: null,
      text1: null,
      flg_func1: flg_func1,
      code_cli_common: code_func1,
      display_order: group?.display_order || 0
    })
    medConditionData[index] = sortBy(medConditionData[index], 'display_order')
  }
  disableCliCommon.value = false
}

const deleteCliCommonRow = (index, idx, id_med_condition) => {
  clinicCommonList.value[index].splice(idx, 1)
  medConditionData[index].splice(idx, 1)
  removedConditionData.value.push(id_med_condition)
}

const showMemoField = (
  field: 'memo_sbj' | 'memo_obj' | 'memo_ass' | 'memo_other'
) => {
  switch (field) {
    case 'memo_sbj':
      return typeCarteConfigList.value[2]?.isChecked
    case 'memo_obj':
      return typeCarteConfigList.value[3]?.isChecked
    case 'memo_ass':
      return typeCarteConfigList.value[6]?.isChecked
    case 'memo_other':
      return typeCarteConfigList.value[7]?.isChecked
  }
}

const openTemplateTextModal = async (type: string, index: number | null = null) => {
  currentTemplate.value = type
  if (currentTemplate.value == 'text') {
    await textTemplateStore.fetchTemplates({ type_text_template: 21, no_pagination: true })
  } else if (currentTemplate.value == 'lab') {
    await textTemplateStore.fetchTemplates({ type_text_template: 45 })
    currentTemplateIndex.value = index
  }
  if (getTemplates.value.length) {
    textTemplatesList.value = sortBy(
      getTemplates.value,
      'display_order',
      'asc'
    ).map((template: any) => {
      return {
        title: template.memo_template,
        flg_title: template.flg_title,
        attr: {
          class: template.flg_title ? 'bg-grey-300' : ''
        },
        isSelected: false
      }
    })
  }
  addTemplateModalFlg.value = true
}

const handleUpdateMemo = (value: any) => {
  if (currentTemplate.value == 'text') {
    if (showMemoField(currentFocusedMemo.value))
      data.value[currentFocusedMemo.value] += ' ' + value.replace(/\n/g, '<br>')
  } else if (currentTemplate.value == 'lab') {
    if (currentTemplateIndex.value == null || currentTemplateIndex.value != -1)
      labResultList.value[currentTemplateIndex.value].qualifier = value
    currentTemplateIndex.value = null
  }
}

const handleMedCondition = (cliCodeCommon, index, idx, flg_func1) => {
  medConditionData[index][idx].type_med_condition =
    medConditionData[index][idx].type_med_condition == cliCodeCommon.code_func1
      ? null
      : cliCodeCommon.code_func1
  medConditionData[index][idx].code_cli_common = cliCodeCommon.code_cli_common
  medConditionData[index][idx].flg_func1 = flg_func1
  medConditionData[index][idx].text1 = cliCodeCommon.text1 ?? ''
}

const onDrop = (e: DragEvent) => {
  e.preventDefault()
  const files = Array.from(e.dataTransfer?.files || [])
  if (files.length > 0) {
    onFileChange(files, true)
  }
  isDragging.value = false
}

const onDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const onDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const onDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
}
const updateValue = (type) => {
  if (type == 'val_weight')
    petBioInfoData.value.val_weight = petBioInfoData.value.val_weight
      ? parseFloat(petBioInfoData.value.val_weight).toFixed(2)
      : ''
  if (type == 'val_temperature')
    petBioInfoData.value.val_temperature = petBioInfoData.value.val_temperature
      ? parseFloat(petBioInfoData.value.val_temperature).toFixed(1)
      : ''
}

const conversationStore = useConversationStore()
const AIRecordState = ref('idle')

const saveCurrentData = async (autoSave: boolean = false): Promise<{ payload: any; petBioPayload: any }> => {


  if (
    !data.value.id_employee ||
    !data.value.datetime_memo_carte ||
    !data.value.id_cli_common ||
    !id_pet_illness_history.value ||
    id_pet_illness_history.value.length === 0
  ) {
    // Show validation alert when user hasn't save and do not show when autosaving
    if (!autoSave) {
      await mtUtils.autoCloseAlert('必須項目を入力してください。')
    }
    return
  }
  const datetime = formatDateWithTime(
    data.value.datetime_memo_carte,
    'YYYY/MM/DD HH:mm:ss'
  )
  data.value.id_pet_illness_history = id_pet_illness_history.value
  const petBioPayload = { ...petBioInfoData.value }
  if (petBioPayload.type_body_weight == 1 && petBioPayload.val_weight) {
    petBioPayload.val_weight = petBioPayload.val_weight * 1000
  }

  const checkMemoFields = memoFields
    .map((v) => {
      if (data.value[v.field]) return true
      return false
    })
    .filter((v) => v)
  if (checkMemoFields.length > 0) {
    const memoFieldsExceed = memoFields.filter(
      (v) => data.value[v.field].replace(/<[^>]*>/g, '').length > 2000
    )
    if (memoFieldsExceed.length > 0) {
      let memoFieldsExceedNames = memoFieldsExceed
        .map((v) => v.label)
        .join(', ')
      return mtUtils.alert(
        `${memoFieldsExceedNames} は2000文字を超えています。`,
        'Error: MC000b'
      )
    }
  }

  if (petBioPayload.val_weight)
    petBioPayload.val_weight = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_weight)
    )
  if (petBioPayload.val_temperature)
    petBioPayload.val_temperature = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_temperature)
    )
  if (petBioPayload.val_pressure_systolic)
    petBioPayload.val_pressure_systolic = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_systolic)
    )
  if (petBioPayload.val_pressure_diastolic)
    petBioPayload.val_pressure_diastolic = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_diastolic)
    )
  if (petBioPayload.val_pressure_mean_arterial)
    petBioPayload.val_pressure_mean_arterial = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_mean_arterial)
    )
  if (petBioPayload.val_blood_oxygen_level)
    petBioPayload.val_blood_oxygen_level = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_blood_oxygen_level)
    )
  if (petBioPayload.val_blood_carbon_dioxide_level)
    petBioPayload.val_blood_carbon_dioxide_level = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_blood_carbon_dioxide_level)
    )
  if (petBioPayload.val_respiration_rate)
    petBioPayload.val_respiration_rate = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_respiration_rate)
    )
  if (petBioPayload.val_heartbeat_rate)
    petBioPayload.val_heartbeat_rate = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_heartbeat_rate)
    )

  let payload: any = {
    datetime_group_carte: datetime,
    memo_carte: {
      ...data.value,
      datetime_memo_carte: datetime,
      id_pet_illness_history: id_pet_illness_history.value
    },
    med_condition_list: sortBy(
      uniq(flatMap(medConditionData))
        .filter((v) => v && !v.id_med_condition)
        .map((item) => {
          const cliCommonTypeMemoCarte =
            getCliCommonTypeMemoCarteList.value.find(
              (v) => v.code_func1 == item?.code_cli_common
            )
          return {
            ...item,
            id_pet: props.id_pet,
            id_customer: props.id_customer,
            id_employee_record: data.value.id_employee,
            datetime_record: datetime,
            display_order: cliCommonTypeMemoCarte?.display_order || 0
          }
        }),
      'display_order'
    ),
    ...(carte_config_id.value ? { carte_config: { carte_config_id: carte_config_id.value, id_item_service_unit: id_item_service_unit.value } } : {})
  }
  // Pet bio validation
  const checkPetBio = Object.keys(petBioPayload)
    .map((v) => {
      if (
        !['id_clinic', 'id_pet', 'id_customer', 'type_body_weight'].includes(v)
      )
        if (petBioPayload?.[v]) return true
      return false
    })
    .filter((v) => v)

  if (
    petBioPayload.val_temperature ||
    petBioPayload.val_pressure_systolic ||
    petBioPayload.val_pressure_diastolic ||
    petBioPayload.val_pressure_mean_arterial ||
    petBioPayload.val_blood_oxygen_level ||
    petBioPayload.val_blood_carbon_dioxide_level ||
    petBioPayload.val_respiration_rate ||
    petBioPayload.val_heartbeat_rate ||
    convertWeightInG(
      petBioPayload.val_weight,
      petBioStore.getPetBio?.type_body_weight
    ) !=
    convertWeightInG(
      petBioStore.getPetBio?.val_weight,
      petBioStore.getPetBio?.type_body_weight
    )
  ) {
    if (checkPetBio.length > 0) {
      Object.assign(payload, {
        pet_bio_info: {
          ...petBioPayload,
          id_pet: props.id_pet,
          id_customer: props.id_customer,
          datetime_measure: datetime
        }
      })
    }
  }



  return {
    payload,
    petBioPayload
  }

}

const autoMemocarteAI = computed(() => {
  if (localStorage.getItem('id_clinic')) {
    const filterClinic = clinicStore.getClinics.find(
      (clinic) => clinic.id_clinic == localStorage.getItem('id_clinic')
    )
    return filterClinic?.flg_auto_memocarte_ai ?? false
  }
  return false
})

const typeMemoCarteBg = computed(() => {
  return (
    typeMemoCarte.value.find((carte) => {
      return carte.id_cli_common === data.value.id_cli_common
    })?.text1 ?? ''
  )
})

const handleRecordingClick = async () => {

  if (!checkServiceHours()) {
    return;
  }
  if (autoMemocarteAI.value && (!id_pet_illness_history.value || id_pet_illness_history.value.length === 0)) {
    mtUtils.autoCloseAlert("For auto-submit memo, please select a pet illness history first.")
    return
  }
  
  if (id_pet_illness_history.value && (data.value.memo_sbj || data.value.memo_obj || data.value.memo_ass || data.value.memo_other)) {
    submit(false, false, false)
  }

  if (localStorage.getItem('id_clinic')) {
    const filterClinic = clinicStore.getClinics.find(
      (clinic) => clinic.id_clinic == localStorage.getItem('id_clinic')
    )
    if (filterClinic) {
      flg_auto_memocarte_ai.value = filterClinic.flg_auto_memocarte_ai
    }
  }
  await requestMicrophoneAccess()
  let confirmationUtils = false
  if (
    conversationStore.flgRecording &&
    props.id_request === conversationStore.requestId
  ) {
    pauseRecording()
    await mtUtils
      .confirm(
        'There is an ongoing record, you sure want to create another one?',
        'Recording Confirmation',
        '削除',
        null,
        null,
        null,
        {
          show: false,
          callBackFun: Function
        },
        true
      )
      .then((confirmation) => {
        if (!confirmation) {
          confirmationUtils = false
          resumeRecording()
        } else {
          confirmationUtils = true
        }
      })
  } else {
    confirmationUtils = true
  }

  if (!confirmationUtils) return

  if (conversationStore.flgRecording) {
    pauseRecording()
  }



  const recordingData = {
    id_employee: data.value.id_employee,
    datetime_memo_carte: data.value.datetime_memo_carte,
    id_customer: props.id_customer,
    id_pet: props.id_pet,
    id_request: props.id_request,
    number_request: props.number_request,
    petSelected: petSelected.value,
    id_clinic: localStorage.getItem('id_clinic'),
    id_pet_illness_history: id_pet_illness_history.value
  }

  conversationStore.setCreateMemoCarteData(recordingData)
  customerStore.setCustomer(customerSelected.value)
  if (conversationStore.flgRecording) {
    saveCurrentRecord({
      id_conversation: conversationStore.conversationId,
      one_person_summarry: conversationStore.onePersonSummarry,
      status_message: 2,
      seconds: seconds.value
    })
  }

  const petStore = usePetStore()
  petStore.setPet(petSelected.value)

  const conversation_id = nanoid()
  conversationStore.setConversationId(conversation_id)
  conversationStore.requestId = props.id_request
  
  console.log("Connecting to WebSocket...")
  const isConnected = await connectWebSocket(conversation_id, petSelected.value.id_pet, data.value.id_employee)

  if(!isConnected) {
    mtUtils.autoCloseAlert('Failed to connect to WebSocket')
    return
  }

  resetStoreData()
  event_bus.emit('close-draggable-recording-modal')


  const popup = { isConfirmed: false }
  await mtUtils.countdownPopup(RecordingCountdownModal, { popup })

  if (popup.isConfirmed) {
    koekaruModalStore.openRecordingModal()
    conversationStore.setFlgRecording(true)
    
    // Wait for the modal component to mount before emitting the event
    await nextTick()
    setTimeout(() => {
      event_bus.emit('recording-start')
    }, 100)
    
    conversationStore.setCurrentMic(recordConfig.value.microphone)
    if (flg_auto_memocarte_ai.value) {
      let { payload } = await saveCurrentData()

      conversationStore.setCurrentMemoCarteData({ payload })

      memoCarteStore.setTempFormData({
        ...payload,
        conversation_id
      })
      conversationStore.setRecentMemoCarteList({
        ...payload,
        conversation_id
      })
    } else {
      const currentData = {
        data: data.value,
        petBioInfoData: petBioInfoData.value,
        medConditionData: medConditionData,
        petSelected: petSelected.value,
        customerSelected: customerSelected.value,
        clinicCommonList: clinicCommonList.value,
        multipleImage: multipleImage.value,
        idPetIllnessHistory: id_pet_illness_history.value,
      }

      conversationStore.setCurrentMemoCarteData(currentData)

      memoCarteStore.setTempFormData({
        ...currentData,
        conversation_id
      })
      conversationStore.setRecentMemoCarteList({
        ...currentData,
        conversation_id
      })
    }
    conversationStore.setSource('create_memo_carte')
    conversationStore.onePersonSummarry = true
    conversationStore.setCreateMemoCarteModal(true)
    closeModal()
  }
}
const openRecordingConfig = async () => {
  await requestMicrophoneAccess()
  await mtUtils.smallPopup(configRecordingModal, {
    id_employee: data.value.id_employee,
    showQuestionTemplate: false,
  })
}
async function requestMicrophoneAccess() {
  try {
    const permissionStatus = await navigator.permissions.query({ name: 'microphone' });

    if (permissionStatus.state !== 'granted') {
      await navigator.mediaDevices.getUserMedia({ audio: true });
    } 

    await listAudioInputs();
  } catch (error) {
    console.error('Error requesting microphone access:', error);
  }
}

async function listAudioInputs() {
  try {
    // Always fetch the devices and update the microphone list
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioDevices  = devices.filter((device) => device.kind === 'audioinput')
    
    const formattedMics = audioDevices.map(mic => {
      let label = mic.label || `Microphone ${mic.deviceId.slice(0, 5)}...`
      
      if (isExternalMic(label)) {
        label = mic.deviceId === 'default' 
          ? '外部接続マイク (デフォルト)' 
          : '外部接続マイク'
      } else {
        if (mic.deviceId === 'default') {
          label = `${label} (デフォルト)`
        }
      }

      return {
        label: label,
        value: mic.deviceId
      }
    }).sort((a, b) => {
      if (a.value === 'default') return -1
      if (b.value === 'default') return 1
      return 0
    })
    michrophoneList.value = [...formattedMics]
    microphonesListDefault.value = [...formattedMics];

    // Check if a microphone is selected in local storage
    const selectedMicFromStorage = localStorage.getItem('selectedMicrophone');
    const matchingMic = michrophoneList.value.find(
      (mic) => mic.value === selectedMicFromStorage
    );

    if (matchingMic) {
      // Use the microphone from local storage if it matches the list
      await nextTick();
      recordConfig.value.microphone = matchingMic.value;
    } else {
      // Select the first microphone from the list if no match is found
      await nextTick();
      recordConfig.value.microphone = michrophoneList.value[0]?.value || null;
    }

    // Save the selected microphone to local storage
    if (recordConfig.value.microphone) {
      localStorage.setItem('selectedMicrophone', recordConfig.value.microphone);
    }
  } catch (error) {
    console.error("Error fetching microphone inputs:", error);
  }
}
const getKoekaruQuestion = async () => {
  try {
    if (questionTemplatesList.value.length > 0) {
      questionTemplatesList.value = []
      questionTemplatesListDefault.value = []
    }
    const questionResponse = await koekaruApi.get('/questions')
    const questionTemplate = questionResponse.data.data.map((question: any) => {
      return {
        label: question.name_template,
        value: question.question_id,
        ...question
      }
    })
    questionTemplatesList.value = [
      ...questionTemplate
    ]
    questionTemplatesListDefault.value.push(
      ...questionTemplate
    )
    if (
      questionTemplatesList.value &&
      questionTemplatesList.value.length &&
      questionTemplatesList.value.length > 0
    ) {
      recordConfig.value.id_question = questionTemplatesList.value.find(
        (v) => v.value
      )?.value
    }
    AIRecordState.value = 'ready'
  } catch (error) {
    console.error('Error fetching Koekaru questions:', error)

  }
}

const resetStoreData = () => {
  ['ai_summary', 'ai_transcript_chats', 'ai_summary_generated',
    'ai_flg_recording', 'memo_cart_content'].forEach((key) => {
      localStorage.removeItem(key)
    })

  conversationStore.setFlgRecording(false)
  conversationStore.setSummaryGenerated(false)
  conversationStore.setSummaryGenerating(false)
  conversationStore.setTranscriptChats([])
  conversationStore.setSummary([])
  seconds.value = 0
  tempFullTranscript.value = ''
  tempTranscript.value = ''
}

const parseSummary = (summary: string) => {
  const sections = {
    memo_sbj: '', // 主観 (Subjective)
    memo_obj: '', // 客観 (Objective)
    memo_ass: '', // 評価 (Assessment)
    memo_other: '' // 計画他 (Plan)
  }

  // Split summary berdasarkan section
  const sectionTexts = summary.split('<br/><br/>')

  sectionTexts.forEach((section) => {
    // Extract title dan content
    const titleMatch = section.match(/<b>## (.+?)<\/b><br\/>(.+)/)
    if (!titleMatch) return

    const [, title, content] = titleMatch
    const bulletPoints = content
      .split('<br/>')
      .map((text) => `• ${text}`)
      .join('<br/>')

    // Map ke section yang sesuai
    switch (title.trim()) {
      case '主観':
        sections.memo_sbj = `<p><strong>## 要確認</strong></p><p>${bulletPoints}</p><p>---</p>`
        break
      case '客観':
        sections.memo_obj = `<p><strong>## 要確認</strong></p><p>${bulletPoints}</p><p>---</p>`
        break
      case '評価':
        sections.memo_ass = `<p><strong>## 要確認</strong></p><p>${bulletPoints}</p><p>---</p>`
        break
      case '計画他':
        sections.memo_other = `<p><strong>## 要確認</strong></p><p>${bulletPoints}</p><p>---</p>`
        break
    }
  })

  return sections
}

const findTypeMedCondition = (type: any) => {
  const findType = typeMedConditionColor.find((t) => t.value == type)
  return findType
}

event_bus.on('close-create-carte-modal', () => {
  closeModal()
})

const initMedCondition = (medCon = [], forceUpdate = false) => {
  clinicCommonList.value = groupBy(
    medCon?.map((medical_condition) => {
      const clinicCommonData = [
        ...clinicCommonStore.getAllCliCommonMedConditionOptionList
          .filter((v) => v.code_cli_common == medical_condition.code_cli_common)
          .sort((a, b) => a.display_order - b.display_order)
      ]
      const cliCommonTypeMemoCarte = getCliCommonTypeMemoCarteList.value?.find(
        (v) => v.code_func1 == medical_condition?.code_cli_common
      )

      return {
        ...medical_condition,
        order: medical_condition.flg_func1 ? 0 : 1,
        groupName: cliMedCondition(medical_condition.code_cli_common)
          ?.name_cli_common,
        clinicCommonData,
        display_order: cliCommonTypeMemoCarte?.display_order
      }
    }),
    'order'
  )

  // Filter the data in clinicCommonList.value
  Object.keys(clinicCommonList.value).forEach((key: number) => {
    clinicCommonList.value[key] = sortBy(clinicCommonList.value[key], 'display_order')
  })

  forEach(clinicCommonList.value, (v, index) => {
    forEach(v, (cliCodeCommon, idx) => {
      if (cliCodeCommon) {
        const cliCommonTypeMemoCarte = cliCodeCommon?.clinicCommonData?.find(
          (v) => v.code_func1 == cliCodeCommon?.type_med_condition
        )
        if (!medConditionData[index]) medConditionData[index] = []
        if (!medConditionData[index][idx] || forceUpdate)
          medConditionData[index][idx] = {
            ...cliCodeCommon,
            display_order: cliCommonTypeMemoCarte?.display_order,
            type_med_condition: cliCodeCommon?.type_med_condition,
            text1:
              cliCodeCommon?.clinicCommonData.find(
                (v) => v.code_func1 == cliCodeCommon?.type_med_condition
              )?.text1 || null,
            code_cli_common: cliCodeCommon?.code_cli_common,
            flg_func1: cliCodeCommon?.flg_func1
          }
      }
    })
  })
}

let autoSaveMillSecs = 10000
let autoSaveTimer = null // Auto save timer ID
let lastChanged = -1
let callAutoSave = false

const onUserActivity = () => {
  lastChanged = Date.now()
  callAutoSave = true
}

const autoSaveMemoCarte = async () => {
  const elapsed = Date.now() - lastChanged
  if (elapsed < autoSaveMillSecs) {
    console.log(
      `CreateMemoCarte: user has been active in the past ${elapsed} ms.`
    )
  } else if (callAutoSave) {
    console.log("CreateMemoCarte: Attempting to auto-save...")
    await submit(false, true)
    if (
      !data.value.id_employee ||
      !data.value.datetime_memo_carte ||
      !data.value.id_cli_common ||
      !id_pet_illness_history.value ||
      id_pet_illness_history.value.length === 0
    ) {
      console.log("CreateMemoCarte: Autosave not called due to validation.");
    } else {
      console.log("CreateMemoCarte: Autosave called");
      callAutoSave = false
    }
  }
}

const copyMemoCarte = () => {
  // Create a temporary container for plain text
  let tempDiv = document.createElement('div')

  tempDiv.textContent =
    'S: 主観' + '\n' + removeHtmlTag(data.value.memo_sbj) + '\n\n'
  tempDiv.textContent +=
    'O: 客観' + '\n' + removeHtmlTag(data.value.memo_obj) + '\n\n'
  tempDiv.textContent +=
    'A: 評価' + '\n' + removeHtmlTag(data.value.memo_ass) + '\n\n'
  tempDiv.textContent +=
    'P: 計画 / 処置他' + '\n' + removeHtmlTag(data.value.memo_other)

  let updatedText = tempDiv.textContent

  copyText(updatedText, 'カルテをコピーしました！')
}

const duplicateMemoCarte = () => {
  props.duplciateCart.value = true
  closeModal()
}

const sumbitDuplicateCart = async () => {
  if (
    !id_pet_illness_history.value ||
    id_pet_illness_history.value.length === 0
  ) {
    return
  }

  const currentDatetime = formatDateWithTime(
    getDateTimeNow(),
    'YYYY/MM/DD HH:mm:ss'
  )

  data.value.id_pet_illness_history = id_pet_illness_history.value
  const petBioPayload = { ...petBioInfoData.value }
  if (petBioPayload.type_body_weight == 1 && petBioPayload.val_weight) {
    petBioPayload.val_weight = petBioPayload.val_weight * 1000
  }

  if (petBioPayload.val_weight)
    petBioPayload.val_weight = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_weight)
    )
  if (petBioPayload.val_temperature)
    petBioPayload.val_temperature = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_temperature)
    )
  if (petBioPayload.val_pressure_systolic)
    petBioPayload.val_pressure_systolic = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_systolic)
    )
  if (petBioPayload.val_pressure_diastolic)
    petBioPayload.val_pressure_diastolic = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_diastolic)
    )
  if (petBioPayload.val_pressure_mean_arterial)
    petBioPayload.val_pressure_mean_arterial = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_pressure_mean_arterial)
    )
  if (petBioPayload.val_blood_oxygen_level)
    petBioPayload.val_blood_oxygen_level = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_blood_oxygen_level)
    )
  if (petBioPayload.val_blood_carbon_dioxide_level)
    petBioPayload.val_blood_carbon_dioxide_level = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_blood_carbon_dioxide_level)
    )
  if (petBioPayload.val_respiration_rate)
    petBioPayload.val_respiration_rate = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_respiration_rate)
    )
  if (petBioPayload.val_heartbeat_rate)
    petBioPayload.val_heartbeat_rate = formatNumberWithDecimals(
      parseFloat(petBioPayload.val_heartbeat_rate)
    )

  const apiPromises = []

  if (
    petBioPayload.val_weight ||
    petBioPayload.val_temperature ||
    petBioPayload.val_pressure_systolic ||
    petBioPayload.val_pressure_diastolic ||
    petBioPayload.val_pressure_mean_arterial ||
    petBioPayload.val_blood_oxygen_level ||
    petBioPayload.val_blood_carbon_dioxide_level ||
    petBioPayload.val_respiration_rate ||
    petBioPayload.val_heartbeat_rate
  )
    delete petBioPayload.id_pet_bio_info
  delete petBioPayload.datetime_insert
  delete petBioPayload.datetime_update
  delete petBioPayload.flg_delete
  delete petBioPayload.id_employee_insert
  delete petBioPayload.id_employee_update

  apiPromises.push(
    mtUtils.callApiEx({
      method: selectOptions.reqMethod.POST,
      url: `/pet_bio_info`,
      params: {
        ...petBioPayload,
        id_pet: props.id_pet,
        id_customer: props.id_customer,
        datetime_measure: currentDatetime,
        datetime_insert: currentDatetime
      }
    })
  )

  data.value.datetime_memo_carte = currentDatetime
  let { id_memo_carte, ...filteredData } = data.value
  let medConditionData = props.data_cartes.medical_condition.map((medCondition) => {
    return {
      ...medCondition,
      datetime_record: currentDatetime,
      type_med_condition: medCondition.type_med_condition.toString()
    }
  })

  medConditionData.forEach((medCondition) => {
    delete medCondition.id_med_condition
    delete medCondition.datetime_insert
    delete medCondition.datetime_update
    delete medCondition.id_employee_insert
    delete medCondition.id_employee_update
  })

  let payload = {
    med_condition_list: medConditionData,
    memo_carte: filteredData
  }

  const response = await mtUtils.promiseAllWithLoaderMsg(
    [
      memoCarteStore.submitMemoCarteV1(payload),
      ...apiPromises
    ],
    '更新しています...'
  )

  mtUtils.autoCloseAlert('カルテを複製しました！\nこのまま編集してください。')
  await memoCarteStore.fetchMemoCarteV1({
    id_pet: props.id_pet,
    id_customer: props.id_customer,
    page_size: 200
  })

  if (response && Array.isArray(response)) {
    isEdit.value = true

    initMedCondition(
      response?.[0]?.data?.data?.med_condition_list,
      true
    )

    const id_memo_carte = response?.[0]?.data?.data?.memo_carte?.id_memo_carte
    if (id_memo_carte) data.value.id_memo_carte = id_memo_carte

    const id_pet_bio_info = response?.[1]?.id_pet_bio_info
    if (id_pet_bio_info) petBioInfoData.value.id_pet_bio_info = id_pet_bio_info
  }
}

const petWeightData = ref()
const fetchPetBioWeightData = async () => {
  try {
    const filterData = {
      id_pet: props.id_pet,
      id_customer: props?.id_customer
    }
    await petBioStore.fetchPetBio(filterData)
    if (!getPetBios.value || getPetBios.value.length === 0) return
    petWeightData.value = getPetBios.value
      .filter((bio) => bio.val_weight)
      .slice(0, 2)
  } catch (error) {
    await mtUtils.autoCloseAlert('データの取得中にエラーが発生しました')
  }
}

const petWeightInfoLabel = (data: any) => {
  if (!data) return '-'
  const weight =
    data.type_body_weight === 1
      ? (data.val_weight / 1000).toFixed(2) + ' kg'
      : parseFloat(data.val_weight).toFixed(2) + ' g'
  return `${weight} (${formatDateTime(data.datetime_measure)})`
}

const setWeightData = (data: any) => {
  if (data.type_body_weight === 1) {
    petBioInfoData.value.val_weight = data.val_weight / 1000
  } else {
    petBioInfoData.value.val_weight = data.val_weight
  }
  petBioInfoData.value.val_temperature = data.val_temperature
  petBioInfoData.value.type_body_weight = data.type_body_weight
  petBioInfoData.value.val_heartbeat_rate = data.val_heartbeat_rate
  petBioInfoData.value.val_respiration_rate = data.val_respiration_rate
}

const openMemoCarteComparisonModal = async () => {
  await mtUtils.popup(ViewLargeMemoCarteComparison, {
    id_customer: props.id_customer,
    id_pet: props.id_pet,
    datetimeInsert: props.datetimeInsert
  })
  updateCurrentCarte()
}

const updateCurrentCarte = () => {
  let updatedCarte = memoCarteStore.getFilteredMemoCartesV1?.[props.date_insert].others?.[props.datetimeInsert]
  let updatedClinicalFiles = memoCarteStore.getFilteredMemoCartesV1?.[props.date_insert]?.clinical_file_list.filter((file: ClinicalFile) => file.datetime_receive === props.datetimeInsert)
  if (updatedCarte) {
    data.value = {
      id_pet: updatedCarte?.memo_carte_list?.[0]?.id_pet || '',
      id_request: updatedCarte?.memo_carte_list?.[0]?.id_request || '',
      id_customer: updatedCarte?.memo_carte_list?.[0]?.id_customer || '',
      id_employee:
        updatedCarte?.memo_carte_list?.[0]?.id_employee ||
        defaultEmployee,
      datetime_memo_carte:
        updatedCarte?.memo_carte_list?.[0]?.datetime_memo_carte ||
        getDateTimeNow(),
      id_clinic:
        updatedCarte?.memo_carte_list?.[0]?.id_clinic ||
        Number(localStorage.getItem('id_clinic')) ||
        '',
      memo_sbj: updatedCarte?.memo_carte_list?.[0]?.memo_sbj || '',
      memo_obj: updatedCarte?.memo_carte_list?.[0]?.memo_obj || '',
      memo_ass: updatedCarte?.memo_carte_list?.[0]?.memo_ass || '',
      memo_other: updatedCarte?.memo_carte_list?.[0]?.memo_other || '',
      id_cli_common:
        updatedCarte?.memo_carte_list?.[0].id_cli_common || -1,
      illnessHistoryOptions:
        updatedCarte?.memo_carte_list?.[0]?.illnessHistoryOptions ||
        null,
      type_input: updatedCarte?.memo_carte_list?.[0]?.type_input || 2,
      id_memo_carte:
        updatedCarte?.memo_carte_list?.[0]?.id_memo_carte || -1
    }

    id_pet_illness_history.value = [
      ...(updatedCarte?.memo_carte_list?.[0]?.id_pet_illness_history ||
        [])
    ]
    let val_weight

    if (updatedCarte?.pet_bio?.val_weight) {
      val_weight = convertWeightInG(
        updatedCarte?.pet_bio?.val_weight,
        updatedCarte?.pet_bio?.type_body_weight
      )
      if (typeof val_weight == 'string') {
        val_weight = parseFloat(val_weight).toFixed(2)
      }
    }
    petBioInfoData.value = {
      id_pet: updatedCarte?.pet_bio?.id_pet || null,
      id_customer: updatedCarte?.pet_bio?.id_customer || null,
      val_weight: val_weight || null,
      type_body_weight: updatedCarte?.pet_bio?.type_body_weight ?? 1,
      val_temperature: updatedCarte?.pet_bio?.val_temperature
        ? parseFloat(updatedCarte?.pet_bio?.val_temperature).toFixed(1)
        : '',
      val_respiration_rate:
        updatedCarte?.pet_bio?.val_respiration_rate || null,
      val_heartbeat_rate:
        updatedCarte?.pet_bio?.val_heartbeat_rate || null,
      val_pressure_systolic:
        updatedCarte?.pet_bio?.val_pressure_systolic || null,
      val_pressure_diastolic:
        updatedCarte?.pet_bio?.val_pressure_diastolic || null,
      val_pressure_mean_arterial:
        updatedCarte?.pet_bio?.val_pressure_mean_arterial || null,
      val_blood_oxygen_level:
        updatedCarte?.pet_bio?.val_blood_oxygen_level || null,
      val_blood_carbon_dioxide_level:
        updatedCarte?.pet_bio?.val_blood_carbon_dioxide_level || null,
      id_clinic:
        updatedCarte?.pet_bio?.id_clinic ||
        localStorage.getItem('id_clinic') ||
        '',
      id_pet_bio_info: updatedCarte?.pet_bio?.id_pet_bio_info || null,
      datetime_measure: updatedCarte?.pet_bio?.datetime_measure
        ? formatDateWithTime(
          updatedCarte?.pet_bio?.datetime_measure,
          'YYYY/MM/DD HH:mm:ss'
        )
        : ''
    }
    if (updatedClinicalFiles) {
      multipleImage.value.length = 0
      filePaths.value.length = 0
      updatedClinicalFiles.forEach((v: ClinicalFile) => {
        const newClinical = { ...v, old: true }
        multipleImage.value.push(newClinical)
        filePaths.value.push(newClinical.file_path)
      })
    }

    if (clinicCommonStore.getAllCliCommonMedConditionOptionList.length > 0) {
      initMedCondition(updatedCarte?.medical_condition, true)
    }

    currentData.value = {
      data: JSON.parse(JSON.stringify(data.value)),
      petBioInfoData: JSON.parse(JSON.stringify(petBioInfoData.value)),
      medConditionData: JSON.parse(JSON.stringify(medConditionData)),
      petSelected: JSON.parse(JSON.stringify(petSelected.value)),
      customerSelected: JSON.parse(JSON.stringify(customerSelected.value)),
      clinicCommonList: JSON.parse(JSON.stringify(clinicCommonList.value)),
      multipleImage: JSON.parse(JSON.stringify(multipleImage.value)),
      id_pet_illness_history: JSON.parse(JSON.stringify(id_pet_illness_history.value))
    }
  }
}

const generatePetCartePdf = async (mode: 'download' | 'print') => {
  mtUtils.popup(UpdatePdfPetCarteSetting, {
    generatePdfAndClose: true,
    dateInsert: props.date_insert,
    datetimeInsert: props.datetimeInsert,
    mode
  })
}

const handleScreenChange = () => {
  showHeaderIconsText.value = window.innerWidth >= MIN_WIDTH_FOR_ICONS_TEXT
}

const toggleOpenLeftSidebar = () => {
  isOpenLeftSidebar.value = !isOpenLeftSidebar.value
  
  if (!isOpenLeftSidebar.value) {
    viewOnlyMemoCarte.value = false
    selectedMemo.value = ''
  }
}

const setMemoCarteContent = async (memo_group: MemoCarteType) => {
  viewOnlyMemoCarte.value = true
  selectedMemo.value = memo_group?.id_memo_carte

  await mtUtils.promiseAllWithLoader([
    memoCarteStore.fetchMemoCartesForLeftCreateMemoCarteDetail({ group_carte: memo_group?.memo?.additional_info?.group_carte })
  ])

  if (memoCarteStore.getCreateMemoCarteLeftSidebar?.length > 0) {
    previewOtherCarte.value = memoCarteStore.getCreateMemoCarteLeftSidebar
  }
}

const openBulkUpdateModal = async () => {
  const selectedFiles = multipleImage.value.filter((file) => !!file.checked).length
  if(!selectedFiles > 0) {
    return mtUtils.alert('臨床ファイルを1つ以上選択してください')
  }
  mtUtils.mediumPopup(UpdateBulkClinicalDiagnosticProvider)
}

type BulkSelectedTypes = {
  diagnostics: number[];
  provider: number | null
}
const handleBulkSelectedTypes = (selectedTypes: BulkSelectedTypes) => {
  multipleImage.value.forEach((file) => {
    if(file.checked) {
      file.type_diagnostic_info = selectedTypes.diagnostics
      file.type_provider = selectedTypes.provider
    }
  })
}

const getTypeDiagnosticInfo = (typeDiagnostics: string | number[]) => {
  let selectedTypeDiagnostics
  if(Array.isArray(typeDiagnostics)) selectedTypeDiagnostics = typeDiagnostics
  else selectedTypeDiagnostics = typeDiagnostics.split(',')
  
  if(selectedTypeDiagnostics.length > 0){
    return selectedTypeDiagnostics.map((diagnostic) => typeDiagnosticInfo.find((typeDiagnostic) => typeDiagnostic.value == diagnostic).label).join(', ')
  }
  return ''
}

const checkedAllFiles = () => {
  multipleImage.value.forEach((file) => file.checked = true)
}

const scrollToMemoField = (index, scrollTo = 'memo') => {
  if (currentMemoCarteScrollAreaRef.value && memoFieldsHeaderRef.value[index]) {
    const targetElement = memoFieldsHeaderRef.value[index]
    const targetPosition = targetElement.offsetTop + 260

    currentMemoCarteScrollAreaRef.value.setScrollPosition('vertical', targetPosition, 300)
  }

  if (currentMemoCarteScrollAreaRef.value && labResultListRef.value && scrollTo === 'lab') {
    const targetElement = labResultListRef.value[index]
    const targetPosition = targetElement.offsetTop + 260

    currentMemoCarteScrollAreaRef.value.setScrollPosition('vertical', targetPosition, 300)
  }
}
const scrollToPosition = (x, y) => {
  if (currentMemoCarteScrollAreaRef.value) {
    currentMemoCarteScrollAreaRef.value.setScrollPosition('vertical', y, 300)
  }
}

const carteConfigClicked = async (index: number) => {
  let carteConfigId = null
  if(index === 1) carteConfigId = authUser.id_carte_config_default1
  else if(index === 2) carteConfigId = authUser.id_carte_config_default2
  else if(index === 3) carteConfigId = authUser.id_carte_config_default3

  carte_config_id.value = carteConfigId

  getCarteConfigData()
}

const getCarteConfigData = async (onMountedEdit = false) => {
  await carteConfigStore.fetchCarteConfigById(carte_config_id.value)
  if (carteConfigStore.getCarteConfig) {
    const medConList = carteConfigStore.getCarteConfig?.child_carte_config_list?.filter((v) => v.id_cli_cm_med_review)
    if(medConList &&medConList.length > 0 && !onMountedEdit) {
      medConList.forEach((v) => {
        const medConData = getCliCommonTypeMemoCarteList.value.find((x) => x.id_cli_common == v.id_cli_cm_med_review)
        if (medConData) fetchCommonCliRecords(medConData.code_func1, medConData.flg_func1, medConData.flg_func1 ? 0 : 1)
      })
    }

    const labSetList = carteConfigStore.getCarteConfig?.child_carte_config_list?.filter((v) => v.id_item_service_unit)
    if (labSetList && labSetList.length > 0) {
      if (categoryStore.getCategoriesLB2.length == 0) {
        categoryStore.fetchCategories({ flg_for_lab: true, code_category_prefix: 'LB2_' }, 'LB2')
      }
      labSetList.forEach(async (v) => {
        id_item_service_unit.value = v.id_item_service_unit
        await itemServiceUnitStore.fetchItemServiceUnit(v.id_item_service_unit)
        if (itemServiceUnitStore.getItemServiceUnit) {
          await labSetStore.fetchLabSets({ id_category2_lb2: itemServiceUnitStore.getItemServiceUnit.list_test })
          labSetStore.getLabSets.forEach((labSet) => {
            if (!labResultList.value.find((v) => v.id_lab_set == labSet.id_lab_set))
              labResultList.value.push({
                ...labSet,
                id_item_service_unit: v.id_item_service_unit
              })
          })
          labResultList.value = sortBy(labResultList.value, ['lab_set.display_order', 'display_order'])
        }
      })
    }

    const textTemplateSchemeList = carteConfigStore.getCarteConfig?.child_carte_config_list?.filter((v) => v.type_carte_source == 3)
    if (textTemplateSchemeList && textTemplateSchemeList.length > 0 && !onMountedEdit) {
      
      textTemplateSchemeList.forEach(async (v) => {
        await textTemplateStore.fetchTemplate(v.id_text_template)
        if (textTemplateStore.getTemplate) {
          if (textTemplateStore.getTemplate.type_text_template == 100) {
            schemaTextTemplateList.value.push(textTemplateStore.getTemplate)
          } else if (textTemplateStore.getTemplate.type_text_template == 21) {
            if (showMemoField('memo_sbj'))
              data.value['memo_sbj'] += ' ' + textTemplateStore.getTemplate.memo_template.replace(/\n/g, '<br>')
          }
        }
      })
    }

  }
  
  mtUtils.autoCloseAlert('Carte Config successfully applied.')
}

const getCategoryName = (id_category: number) => {
  return categoryStore.getCategoriesLB2.find((v) => v.id_category == id_category)?.name_category
}

const useTemplate = (item: TextTemplateType) => {
  mtUtils.mediumPopup(FabricMemoCarteModal, {
    id_memo_carte: null,
    id_customer: props.id_customer,
    id_pet: props.id_pet,
    isDirectSubmit: false,
    id_pet_illness_history: [id_pet_illness_history.value],
    imageUrl: item.img_file_path_template
  }).then(() => {
    if(item) {
      if (fabricStore.getFabricOption.length > 0) {
        fabricStore.getFabricOption.forEach((v) => {
          const images = {
            ...v,
            id_pet_illness_history: data.value.id_pet_illness_history?.[0]
              ? data.value.id_pet_illness_history?.[0]
              : null,
            datetime_receive: formatDateWithTime(
              data.value.datetime_memo_carte,
              'YYYY/MM/DD HH:mm:ss'
            ),
            name_file: formatDateWithTime(
              getDateTimeNow(),
              'FileMemoCarte_YYYYMMDD_HHmmss.jpeg'
            ),
            type_file: 1,
            type_receive_format: 2,
            type_provider: 1
          }
          multipleImage.value.push(images)
          filePaths.value.push(v.file_path)
        })
        fabricStore.resetFabricOption()
      }
    }
  })
}

onMounted(async () => {
  if (employeeRef.value) {
    employeeRef.value.$el.focus();
  }
  if (dateRef.value) {
    dateRef.value.$el.focus();
  }
  if (pullDownRef.value) {
    pullDownRef.value.$el.focus();
  }
  if (diseaseRef.value) {
    diseaseRef.value.$el.focus();
  }

  try {
    // Temporarily disable the autosave feature while waiting for the data-saving logic to be refactored.
    // Commenting out the autosave timer and related logic.
    // console.log('CreateMemoCarte: create autosave timer')
    // nextTick(() => {
    //   watch(
    //     [
    //       data,
    //       id_pet_illness_history,
    //       petBioInfoData,
    //       medConditionData,
    //       petSelected,
    //       customerSelected,
    //       multipleImage
    //     ],
    //     () => {
    //       onUserActivity()
    //     },
    //     { deep: true }
    //   )
    // })
    // autoSaveTimer = setInterval(autoSaveMemoCarte, autoSaveMillSecs)
    if (props.date_insert && props.date_insert != '') isEdit.value = true
    await cliCommonStore.fetchPreparationCliCommonList({
      code_cli_common: [11, 14]
    })

    // SET DEFAULT VAL WEIGHT
    petBioInfoData.value.val_weight = null

    data.value.id_cli_common = typeMemoCarte.value?.[0]?.id_cli_common
    if (isEdit.value) {
      data.value = {
        id_pet: props.data_cartes?.memo_carte_list?.[0]?.id_pet || '',
        id_request: props.data_cartes?.memo_carte_list?.[0]?.id_request || '',
        id_customer: props.data_cartes?.memo_carte_list?.[0]?.id_customer || '',
        id_employee:
          props.data_cartes?.memo_carte_list?.[0]?.id_employee ||
          defaultEmployee,
        datetime_memo_carte:
          props.data_cartes?.memo_carte_list?.[0]?.datetime_memo_carte ||
          getDateTimeNow(),
        id_clinic:
          props.data_cartes?.memo_carte_list?.[0]?.id_clinic ||
          Number(localStorage.getItem('id_clinic')) ||
          '',
        memo_sbj: props.data_cartes?.memo_carte_list?.[0]?.memo_sbj || '',
        memo_obj: props.data_cartes?.memo_carte_list?.[0]?.memo_obj || '',
        memo_ass: props.data_cartes?.memo_carte_list?.[0]?.memo_ass || '',
        memo_other: props.data_cartes?.memo_carte_list?.[0]?.memo_other || '',
        id_cli_common:
          props.data_cartes?.memo_carte_list?.[0].id_cli_common || -1,
        illnessHistoryOptions:
          props.data_cartes?.memo_carte_list?.[0]?.illnessHistoryOptions ||
          null,
        type_input: props.data_cartes?.memo_carte_list?.[0]?.type_input || 2,
        id_memo_carte:
          props.data_cartes?.memo_carte_list?.[0]?.id_memo_carte || -1
      }

      id_pet_illness_history.value = [
        ...(props.data_cartes?.memo_carte_list?.[0]?.id_pet_illness_history ||
          [])
      ]
      let val_weight

      if (props.data_cartes?.pet_bio?.val_weight) {
        val_weight = convertWeightInG(
          props.data_cartes?.pet_bio?.val_weight,
          props.data_cartes?.pet_bio?.type_body_weight
        )
        if (typeof val_weight == 'string') {
          val_weight = parseFloat(val_weight).toFixed(2)
        }
      }
      petBioInfoData.value = {
        id_pet: props.data_cartes?.pet_bio?.id_pet || null,
        id_customer: props.data_cartes?.pet_bio?.id_customer || null,
        val_weight: val_weight || null,
        type_body_weight: props.data_cartes?.pet_bio?.type_body_weight ?? 1,
        val_temperature: props.data_cartes?.pet_bio?.val_temperature
          ? parseFloat(props.data_cartes?.pet_bio?.val_temperature).toFixed(1)
          : '',
        val_respiration_rate:
          props.data_cartes?.pet_bio?.val_respiration_rate || null,
        val_heartbeat_rate:
          props.data_cartes?.pet_bio?.val_heartbeat_rate || null,
        val_pressure_systolic:
          props.data_cartes?.pet_bio?.val_pressure_systolic || null,
        val_pressure_diastolic:
          props.data_cartes?.pet_bio?.val_pressure_diastolic || null,
        val_pressure_mean_arterial:
          props.data_cartes?.pet_bio?.val_pressure_mean_arterial || null,
        val_blood_oxygen_level:
          props.data_cartes?.pet_bio?.val_blood_oxygen_level || null,
        val_blood_carbon_dioxide_level:
          props.data_cartes?.pet_bio?.val_blood_carbon_dioxide_level || null,
        id_clinic:
          props.data_cartes?.pet_bio?.id_clinic ||
          localStorage.getItem('id_clinic') ||
          '',
        id_pet_bio_info: props.data_cartes?.pet_bio?.id_pet_bio_info || null,
        datetime_measure: props.data_cartes?.pet_bio?.datetime_measure
          ? formatDateWithTime(
            props.data_cartes?.pet_bio?.datetime_measure,
            'YYYY/MM/DD HH:mm:ss'
          )
          : '',
        flg_panting:
          props.data_cartes?.pet_bio?.flg_panting || false
      }
      if (props.clinical_file?.length > 0) {
        props.clinical_file.forEach((v) => {
          const newClinical = { ...v, old: true }
          multipleImage.value.push(newClinical)
          filePaths.value.push(newClinical.file_path)
        })
      }

      const code_cli_common = props.code_cli_common || [11, 14]
      if (clinicCommonStore.getAllCliCommonMedConditionOptionList.length === 0) {
        await clinicCommonStore.fetchMedConditionCliCommonList({
          code_cli_common: code_cli_common
        })
      }

      if (clinicCommonStore.getAllCliCommonMedConditionOptionList.length > 0) {
        initMedCondition(props?.data_cartes?.medical_condition)
      }
    }

    petSelected.value = customerStore.getPet
    customerSelected.value = customerStore.getCustomer

    const typeCarteConfigVal = clinicStore.getClinic?.type_carte_config
    typeCarteConfigList.value = typeCarteConfig.map((el) => {
      return {
        ...el,
        isChecked: isBitSet(typeCarteConfigVal, el.value)
      }
    })

    data.value.id_customer = props.id_customer
    data.value.id_pet = props.id_pet
    if (props.id_request) data.value.id_request = props.id_request

    if (getIllnessHistorys.value.length === 0) {
      await illnessHistoryStore.fetchIllnessHistorys({
        id_pet: petSelected.value?.id_pet,
        id_customer: customerSelected.value?.id_customer
      })
    }
    if (props.id_memo_carte === -1) {
      if (
        getLeftSideIllnessHistoryList.value &&
        getLeftSideIllnessHistoryList.value.length > 0
      ) {
        let defaultIllnessHistory = getLeftSideIllnessHistoryList.value
          .filter((v: IllnessHistoryType) => v.id_pet_illness_history)
          .map((v: IllnessHistoryType) => v.id_pet_illness_history)

        data.value.id_pet_illness_history = defaultIllnessHistory
        if (!isEdit.value) {
          id_pet_illness_history.value = [...defaultIllnessHistory]
        }
      }
    }
    // Updating q-editor toolbar names to JP
    const observerCallback = (mutationList, observer) => {
      for (let mutation of mutationList) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (
              node.nodeType === 1 &&
              (node.matches('[role="menu"]') ||
                node.matches('.q-editor__toolbar-group'))
            ) {
              changeToggleDropdownNames()
            }
          })
        }
      }
    }
    observer = new MutationObserver(observerCallback)
    observer.observe(document.body, { childList: true, subtree: true })

    event_bus.on('update-memo-field', (payload, currentData) => {
      if (payload.summary) {
        if (currentData) {
          data.value = currentData.data
          petBioInfoData.value = currentData.petBioInfoData
          medConditionData = currentData.medConditionData
          petSelected.value = currentData.petSelected
          customerSelected.value = currentData.customerSelected
          clinicCommonList.value = currentData.clinicCommonList
          multipleImage.value = currentData.multipleImage
          id_pet_illness_history.value = currentData.idPetIllnessHistory
        }

        const splitContentByHash = payload.summary.split('<b>').map((section, index) => 
          index === 0 ? section.trim() : `<b>${section.trim()}`
        ).filter((section) => section)

        const [memoSbj, memoObj, memoAss, memoOther] = splitContentByHash;
        
        // Helper function to check if content is empty (only contains title and <br/>)
        const isContentEmpty = (content) => {
          const withoutTitle = content.replace(/<b>##\s*(主観|客観|評価|計画他)\s*<\/b>/g, '');
          const withoutBr = withoutTitle.replace(/<br\/?>/g, '');
          return !withoutBr.trim();
        };

        // Helper function to process content
        const processContent = (content) => {
          return isContentEmpty(content) ? '' : content;
        };

        // Combine and process content
        data.value = {
          ...data.value,
          memo_sbj: processContent(data.value.memo_sbj + (data.value.memo_sbj ? '<br/>' + memoSbj : memoSbj)),
          memo_obj: processContent(data.value.memo_obj + (data.value.memo_obj ? '<br/>' + memoObj : memoObj)),
          memo_ass: processContent(data.value.memo_ass + (data.value.memo_ass ? '<br/>' + memoAss : memoAss)),
          memo_other: processContent(data.value.memo_other + (data.value.memo_other ? '<br/>' + memoOther : memoOther))
        };
      }
    })

    event_bus.on('onSelectBulkFileTypes', handleBulkSelectedTypes)

    // fetch weight history
    await fetchPetBioWeightData()

    // 乃口 チェリー ちゃん
    // 松本 梅子 ちゃん

    conversationStore.setCreateMemoCarteModal(true)

    currentData.value = {
      data: JSON.parse(JSON.stringify(data.value)),
      petBioInfoData: JSON.parse(JSON.stringify(petBioInfoData.value)),
      medConditionData: JSON.parse(JSON.stringify(medConditionData)),
      petSelected: JSON.parse(JSON.stringify(petSelected.value)),
      customerSelected: JSON.parse(JSON.stringify(customerSelected.value)),
      clinicCommonList: JSON.parse(JSON.stringify(clinicCommonList.value)),
      multipleImage: JSON.parse(JSON.stringify(multipleImage.value)),
      id_pet_illness_history: JSON.parse(JSON.stringify(id_pet_illness_history.value))
    }
    window.addEventListener('resize', handleScreenChange)
    if (props.autoSaveOnMount) {
      nextTick(() => {
        sumbitDuplicateCart()
      })
    }
     if(isEdit.value) {
      const sanitizeFieldContent = (content, fieldName) => {
      
        if (!content) return '';

        // First, check if content only contains HTML tags and whitespace
        const textContent = content
          .replace(/<[^>]*>/g, '')  // Remove all HTML tags
          .replace(/&nbsp;/g, ' ')  // Replace &nbsp; with space
          .replace(/\s+/g, ' ')     // Replace multiple spaces with single space
          .trim();                  // Remove leading/trailing whitespace

        if (!textContent) return ''; // If no meaningful content, return empty string

        // Check if content has actual text beyond the title and <br/> tags
        const hasActualContent = content
          .replace(/<b>##\s*(主観|客観|評価|計画他)\s*<\/b>/g, '') // Remove title
          .replace(/<br\/?>/g, '')  // Remove all <br/> tags
          .replace(/\s+/g, '')      // Remove all whitespace
          .length > 0;

        if (!hasActualContent) {
          return '';
        }

        // If  have actual content, clean up the format
        let cleanedContent = content
          .replace(/^\s*<br\/?>\s*/, '')  // Remove leading <br/>
          .replace(/\s*<br\/?>\s*$/, '')  // Remove trailing <br/>
          .trim();

        return cleanedContent;
      };

      data.value = {
        ...data.value,
        memo_sbj: sanitizeFieldContent(props.data_cartes?.memo_carte_list?.[0]?.memo_sbj || '', 'memo_sbj'),
        memo_obj: sanitizeFieldContent(props.data_cartes?.memo_carte_list?.[0]?.memo_obj || '', 'memo_obj'),
        memo_ass: sanitizeFieldContent(props.data_cartes?.memo_carte_list?.[0]?.memo_ass || '', 'memo_ass'),
        memo_other: sanitizeFieldContent(props.data_cartes?.memo_carte_list?.[0]?.memo_other || '', 'memo_other')
      };

      // LAB RESULT INIT DATA{
      if (props.data_cartes?.lab_result_list) {
        const labResult = flatMap(props.data_cartes?.lab_result_list, (serviceDetail) => {
          // Extract all lab result arrays from the nested structure
          return flatMap(values(serviceDetail), (datetimeObj) => {
            return flatMap(values(datetimeObj), (labResultArray) => {
              return Array.isArray(labResultArray) ? labResultArray : []
            })
          })
        })
        carte_config_id.value = labResult?.find((v) => v.carte_detail)?.carte_detail?.carte_config_list?.carte_config_id
        labResultList.value = sortBy(labResult, 'display_order')
        getCarteConfigData(true)
      }
     }

  } catch (error) {
    console.error('Error in onMounted:', error);
  }

  // AI record setup 
  await initRecordAI()
})

onBeforeUnmount(() => {
  event_bus.off('update-memo-field')
  event_bus.off('close-create-carte-modal')
  event_bus.off('onSelectBulkFileTypes', handleBulkSelectedTypes)
  conversationStore.setCreateMemoCarteModal(false)
  if (autoSaveTimer != null) {
    console.log('CreatteMemoCarte clear autosave timer')
    clearInterval(autoSaveTimer)
    autoSaveTimer = null
  }
  window.removeEventListener('mousemove', onUserActivity)
  window.removeEventListener('click', onUserActivity)
  window.removeEventListener('keydown', onUserActivity)
  window.removeEventListener('touchstart', onUserActivity)
  window.removeEventListener('resize', handleScreenChange)
})

const initRecordAI = async () => {
  try {
    await koekaruApi.get(`/koekaru-availability`)
    if (!secretKey) {
      await getInstance(useClinicStore().getClinic?.code_clinic)
      await getKoekaruQuestion()
    }
    else if (questionTemplatesList.value.length === 0) {
      await getKoekaruQuestion()
    }
    else if (secretKey && questionTemplatesList.value.length !== 0) {
      AIRecordState.value = 'ready'
    }

  } catch (error) {
    console.error('Error fetching Koekaru availability:', error);
    AIRecordState.value = 'not_available'

  }
}

const handleOpenViewAllCartePerDateList = () => {
  openViewAllCartePerDateList(customerSelected.value, petSelected.value)
}

const handleCopyButtonClicked = async (newData: { field: string, content: any }) => {
  if (newData.field === 'memo_sbj') {
    data.value.memo_sbj += data.value.memo_sbj ? '<br/>' + newData.content : newData.content
    scrollToMemoField(0)
    mtUtils.autoCloseAlert('S: 主観を反映しました！')
  } else if (newData.field === 'memo_obj') {
    data.value.memo_obj += data.value.memo_obj ? '<br/>' + newData.content : newData.content 
    mtUtils.autoCloseAlert('O: 客観を反映しました！')
    scrollToMemoField(1)
  } else if (newData.field === 'memo_ass') {
    data.value.memo_ass += data.value.memo_ass ? '<br/>' + newData.content : newData.content 
    mtUtils.autoCloseAlert('A: 診察を反映しました！')
    scrollToMemoField(2)
  } else if (newData.field === 'memo_other') {
    data.value.memo_other += data.value.memo_other ? '<br/>' + newData.content : newData.content 
    mtUtils.autoCloseAlert('P: その他を反映しました！')
    scrollToMemoField(3)
  } else if (newData.field === 'memo_sbj_med' || newData.field === 'memo_obj_med') {
    const newDataContent = newData.content
      ?.filter((v: any) => {
        if (newData.field === 'memo_sbj_med')
          return !medConditionData?.[0]?.map((x) => x.code_cli_common).includes(v.code_cli_common)
        else if (newData.field === 'memo_obj_med')
          return !medConditionData?.[1]?.map((x) => x.code_cli_common).includes(v.code_cli_common)
      })
      ?.map((v: any, key: number) => {
        delete v.id_med_condition
        delete v.datetime_insert
        delete v.datetime_update
        delete v.id_employee_insert
        delete v.id_employee_update
        return { ...v, key }
      })

    let medConData = [] as any[]
    if (newData.field === 'memo_sbj_med') {
      if (!medConditionData[0]) medConditionData[0] = []
      medConData = [...medConditionData[0], ...(medConditionData[1] ? medConditionData[1] : []), ...newDataContent]
      scrollToMemoField(0)
    } else if (newData.field === 'memo_obj_med') {
      if (!medConditionData[1]) medConditionData[1] = []
      medConData = [...medConditionData[1], ...(medConditionData[0] ? medConditionData[0] : []), ...newDataContent]
      scrollToMemoField(1)
    }

    if (medConData.length > 0) medConData = orderBy(medConData, 'code_cli_common', 'asc')
    
    initMedCondition(medConData, true)
    mtUtils.autoCloseAlert('診療評価を反映しました！')
  } else if (newData.field === 'pet_bio') {
    let val_weight = null
    if (newData.content.val_weight) {
      val_weight = convertWeightInG(
        newData.content.val_weight,
        newData.content.type_body_weight
      )
    }
    petBioInfoData.value = {...newData.content, val_weight: val_weight}
    scrollToPosition(0, 0)
    mtUtils.autoCloseAlert('生体情報を反映しました！')
  } else if (newData.field === 'type_carte_ih') {
    data.value.id_pet_illness_history = newData.content.id_pet_illness_history
    data.value.id_cli_common = newData.content.id_cli_common
    id_pet_illness_history.value = [...newData.content.id_pet_illness_history]
    scrollToPosition(0, 0)
    mtUtils.autoCloseAlert('区分と既往歴を反映しました！')
  } else if (newData.field === 'lab_result') {
    const labResult = flatMap(newData.content, (serviceDetail) => {
      // Extract all lab result arrays from the nested structure
      return flatMap(values(serviceDetail), (datetimeObj) => {
        return flatMap(values(datetimeObj), (labResultArray) => {
          return Array.isArray(labResultArray) ? labResultArray : []
        })
      })
    })
    const carteDetail = labResult?.find((v) => v.carte_detail)
    if (carteDetail) {
      await carteConfigStore.fetchCarteConfigById(carteDetail.carte_detail.carte_config_list.carte_config_id)
      carte_config_id.value = carteDetail.carte_detail.carte_config_list.carte_config_id
      id_item_service_unit.value = carteConfigStore.getCarteConfig?.child_carte_config_list?.find((v) => v.id_item_service_unit)?.id_item_service_unit
    }

    labResultList.value = labResult.map((v) => ({ ...v, id_item_service_unit: id_item_service_unit.value }))

    scrollToMemoField(0, 'lab')
    mtUtils.autoCloseAlert('検査結果を反映しました！')
  }
}

const koekaruModalStore = useKoekaruModalStore()
</script>
<template>
  <MtModalHeader @closeModal="closeModal(false)" class="memo-carte-header col-auto">
    <q-toolbar-title class="text-grey-900 title3 bold row items-center header-title">
      <!-- カルテ：
      <span class="cursor-pointer" @click="copyText(data.number_memo_carte)">
        {{ data?.number_memo_carte }}
        <q-icon class="blue" name="content_copy" />
      </span> -->
      <span v-if="data.id_pet" class="q-ml-md" style="min-width: 0;">
        <MtPetInfoDynamic :data="data" class="ellipsis" />
      </span>
    </q-toolbar-title>
    <div class="header-menu-btn-sec">
      <q-btn flat style="left: 10px;" @click="handleOpenViewAllCartePerDateList">
        <span>日</span>
      </q-btn>
      <q-btn class="q-mx-sm" flat @click="openTemplateTextModal('text')">
        <q-icon class="q-mr-sm" name="playlist_add" />
        <span v-if="showHeaderIconsText">テンプレ</span>
      </q-btn>
      <q-btn class="q-mr-sm" flat unelevated @click="openFabricMemoCarteModal">
        <q-icon class="q-mr-sm" name="add_photo_alternate" />
        <span v-if="showHeaderIconsText">シェーマ</span>
      </q-btn>
      <q-btn v-if="!!props.data_cartes?.memo_carte_list?.length" class="q-mr-sm" flat @click="duplicateMemoCarte">
        <q-icon class="q-mr-sm" name="copy_all" />
        <span v-if="showHeaderIconsText">複製</span>
      </q-btn>
      <q-btn v-if="!!props.data_cartes?.memo_carte_list?.length" class="q-mr-sm" flat @click="copyMemoCarte">
        <q-icon class="q-mr-sm" name="content_copy" />
        <span v-if="showHeaderIconsText">コピー</span>
      </q-btn>
      <q-btn v-if="!!props.data_cartes?.memo_carte_list?.length" class="q-mr-sm" flat
        @click="openMemoCarteComparisonModal">
        <q-icon class="q-mr-sm" name="compare" />
        <span v-if="showHeaderIconsText">比較</span>
      </q-btn>
      <q-btn v-if="props?.id_memo_carte != -1" class="q-mr-sm" unelevated @click="openCreateTaskModal">
        <q-icon class="q-mr-sm" name="add" />
        <span v-if="showHeaderIconsText">タスク</span>
      </q-btn>
      <q-btn v-if="props?.id_memo_carte != -1" class="q-mr-sm" flat round unelevated
        @click="openMemoCarteMoveableModal">
        <q-icon name="chrome_reader_mode" />
      </q-btn>
      <q-btn v-if="isEdit" class="q-mr-sm" flat icon="more_horiz" round @click="openMenu" />
    </div>
  </MtModalHeader>
  <q-card-section class="content file-container col" :class="{ 'drag-over': isDragging }">
    <q-icon v-if="isDragging" class="upload-icon" size="xl" name="cloud_upload" />
    <div class="row q-gutter-md" @dragenter.prevent="onDragEnter" @dragleave.prevent="onDragLeave"
      @dragover.prevent="onDragOver" @drop.prevent="onDrop">
      <div :class="isOpenLeftSidebar ? 'col-2' : 'hidden'">
        <div class="full-width text-right">
          <q-btn flat align="right" dense @click="toggleOpenLeftSidebar" class="full-width">
            <q-icon :name="isOpenLeftSidebar ? 'chevron_left' : 'chevron_right'" class="q-mr-sm" dense size="18px" /> 参照を閉じる
          </q-btn>
        </div>
        <LeftCreateMemoCarteModal v-if="isOpenLeftSidebar" @setMemoCarteContent="setMemoCarteContent" :id_pet="id_pet"
          :selectedMemo="selectedMemo" />
      </div>
      <div v-if="isOpenLeftSidebar && viewOnlyMemoCarte" class="col-3">
        <q-scroll-area :style="scrollAreaHeight(100, 210)">
          <PreviewOtherCreateMemoCarteModal
            :data="previewOtherCarte"
            :id_pet="id_pet"
            :id_customer="id_customer"
            @on-copy-button-clicked="handleCopyButtonClicked"
          />
        </q-scroll-area>
      </div>
      <div class="col col-grow">
        <q-scroll-area ref="currentMemoCarteScrollAreaRef" :style="scrollAreaHeight(100, 210)" class="q-pr-md">
          <q-btn v-if="!isOpenLeftSidebar" flat @click="toggleOpenLeftSidebar" class="bg-accent-100">
            過去カルテを参照 <q-icon name="chevron_right" class="q-ml-sm" size="18px" />
          </q-btn>
          <div>
            <div class="row items-center q-gutter-md q-mb-md">
              <div class="col">
                <div class="row items-center q-col-gutter-md">
                  <div class="col-3">
                    <InputEmployeeOptGroup v-model:selected="data.id_employee" label="記入者"
                      show-select-default-employee ref="employeeRef"
                      @update:select-default-employee="selectDefaultEmployee" required
                      :rules="[aahValidations.validationRequired]" />
                  </div>
                  <div class="col-3">
                    <MtFormInputDate v-model:date="data.datetime_memo_carte"
                      label="メモカルテ記録日時" ref="dateRef" required :rules="[aahValidations.validationRequired]" />
                  </div>
                  <div class="col-3">
                    <MtFormPullDown v-model:selected="data.id_cli_common"
                      :options="typeMemoCarte" label="カルテ区分" ref="pullDownRef" custom-option required
                      :rules="[aahValidations.validationRequired]" autofocus :style="{'background': typeMemoCarteBg}">
                      <template #selectedItem="{ slotProps }">
                        <q-item-label>
                          {{ slotProps.opt.label }}
                        </q-item-label>
                      </template>
                      <template #option="{ slotProps }">
                        <q-item v-bind="slotProps.itemProps" :class="`bg-${slotProps.opt.text1}`"
                          :style="{ backgroundColor: slotProps.opt.text1 }">
                          <q-item-section>
                            {{ slotProps.opt.label }}
                          </q-item-section>
                        </q-item>
                      </template>
                    </MtFormPullDown>
                  </div>
                  <div class="col-3">
                    <!-- props {{ id_pet_illness_history }}
                      data {{ data.id_pet_illness_history }} -->
                    <MtFormMultipleSelection
                      ref="diseaseRef"
                      v-model="id_pet_illness_history"
                      :options="getIllnessHistorys"
                      :option-label="(v: IllnessHistoryType): string => {
                        return v.name_disease ? v.name_disease : v.name_disease_free
                      }"
                      option-value="id_pet_illness_history"
                      required :rules="[aahValidations.validationRequired]" label="現疾患・既往歴"
                      show-quick-illness-history
                    />
                  </div>
                </div>
              </div>
            </div>
            <!-- PREVIEW OR INPUT BIG MEMO CARTE (TYPE_CARTE = 2) -->
            <div v-if="typeCarteConfigList[0]?.isChecked">
              <h6>生体情報</h6>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-3">
                  <MtFormInputNumber label="体重" decimalSize="2"
                    v-model:value="petBioInfoData.val_weight" @blur="updateValue('val_weight')" />
                </div>

                <div class="col-9" style="display: flex; align-items: center; gap: 16px">
                  <div class="">
                    <MtInputForm type="radio" :items="typeBodyWeight"
                      v-model="petBioInfoData.type_body_weight" required :rules="[aahValidations.validationRequired]" />
                  </div>
                  <div class="" v-if="petWeightData && petWeightData.length > 0" v-for="weight in petWeightData">
                    <q-btn :label="petWeightInfoLabel(weight)" flat no-caps size="12px"
                      class="bg-grey-200 text-grey-800 q-py-none" @click="setWeightData(weight)">
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-col-gutter-md q-mb-md">
              <template v-if="
                typeCarteConfigList[0]?.isChecked ||
                typeCarteConfigList[1]?.isChecked
              ">
                <div class="col-lg-3 col-md-3 col-sm-6 field-right-text temp">
                  <MtFormInputNumber label="体温 T" decimalSize="1"
                    v-model:value="petBioInfoData.val_temperature" @blur="updateValue('val_temperature')" />
                </div>
              </template>
              <div class="col-lg-3 col-md-3 col-sm-6 field-right-text bpm" v-if="typeCarteConfigList[1]?.isChecked">
                <MtFormInputNumber mode="number" label="心拍 P"
                  v-model:value="petBioInfoData.val_heartbeat_rate" />
              </div>
              <template v-if="
                typeCarteConfigList[0]?.isChecked ||
                typeCarteConfigList[1]?.isChecked
              ">
                <div class="col-lg-3 col-md-3 col-sm-6 field-right-text respiration-amount">
                  <MtFormInputNumber mode="number" label="呼吸数 R"
                    v-model:value="petBioInfoData.val_respiration_rate" />
                </div>
              </template>
              <div class="col-lg-3 col-md-3 col-sm-6 field-right-text">
                <MtInputForm
                  v-model="petBioInfoData.flg_panting"
                  :items="[{ label: 'パンティング 🅟' }]"
                  type="checkbox"
                />
              </div>
            </div>
            <!-- <template v-if="typeCarteConfigList[1]?.isChecked">
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-3 col-md-3 col-sm-6 field-right-text mmHg">
                  <MtFormInputNumber
                    mode="dosage"
                    label="収縮期血圧"
                    decimalSize="1"
                    v-model:value="petBioInfoData.val_pressure_systolic"
                  />
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 field-right-text mmHg">
                  <MtFormInputNumber
                    mode="dosage"
                    label="拡張期血圧"
                    decimalSize="1"
                    v-model:value="petBioInfoData.val_pressure_diastolic"
                  />
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 field-right-text mmHg">
                  <MtFormInputNumber
                    mode="dosage"
                    label="平均動脈圧"
                    decimalSize="1"
                    v-model:value="petBioInfoData.val_pressure_mean_arterial"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div
                  class="col-lg-3 col-md-3 col-sm-6 field-right-text blood-percent"
                >
                  <MtFormInputNumber
                    mode="dosage"
                    label="血中酸素濃度"
                    decimalSize="1"
                    v-model:value="petBioInfoData.val_blood_oxygen_level"
                  />
                </div>
                <div
                  class="col-lg-3 col-md-3 col-sm-6 field-right-text blood-percent"
                >
                  <MtFormInputNumber
                    mode="dosage"
                    label="血中二酸化炭素濃度"
                    decimalSize="1"
                    v-model:value="petBioInfoData.val_blood_carbon_dioxide_level"
                  />
                </div>
              </div>
            </template> -->
            <!-- <div class="q-mb-md" v-if="typeCarteConfigList[4]?.isChecked">
              <h6>診療所見</h6>
              
            </div> -->
            <div v-if="authUser.id_carte_config_default1 || authUser.id_carte_config_default2 || authUser.id_carte_config_default3" class="row">
              <div class="flex justify-between items-center full-width">
                <div class="flex items-center">
                  <q-btn
                    color="primary"
                    label="Carte Config 1"
                    :style="viewOnlyMemoCarte ? 'font-size: 10px;' : ''"
                    class="q-mr-md"
                    @click="carteConfigClicked(1)"
                    :disable="!authUser.id_carte_config_default1"
                  />
                  <q-btn
                    color="primary"
                    label="Carte Config 2"
                    :style="viewOnlyMemoCarte ? 'font-size: 10px;' : ''"
                    class="q-mr-md"
                    @click="carteConfigClicked(2)"
                    :disable="!authUser.id_carte_config_default2"
                  />
                  <q-btn
                    color="primary"
                    label="Carte Config 3"
                    :style="viewOnlyMemoCarte ? 'font-size: 10px;' : ''"
                    class="q-mr-md"
                    @click="carteConfigClicked(3)"
                    :disable="!authUser.id_carte_config_default3"
                  />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="flex justify-between items-center full-width">
                <div class="flex items-center">
                  <h6 class="q-ma-none">メモカルテ</h6>
                  <div class="col relative-position">
                    <q-toggle label="ツールバー" class="q-mt-sm" v-model="flgEditorToolbar"
                      color="primary" />
                  </div>
                </div>
                <div>
                  <!-- AI record  -->
                  <div class="flex items-center" v-if="AIRecordState === 'ready'">
                    <!-- start one person summary record -->
                    <div
                      class="recording-btn bg-primary flex column justify-center items-center cursor-pointer custom-mic-button q-mr-sm"
                      @click="handleRecordingClick()">
                      <q-img src="@/assets/img/request/dictation_mic.png" fit="none" width="16px" height="16px" />
                      <span class="text-white custom-text-mic">新規</span>
                    </div>
                    <!-- AI config  -->
                    <div class="recording-btn flex column justify-center items-center cursor-pointer"
                      style="margin-top: 0; margin-left: 0;" @click="openRecordingConfig()">
                      <q-btn flat dense label="設定"></q-btn>
                    </div>

                  </div>
                  <div class="flex items-center" v-else-if="AIRecordState === 'not_available'">
                    ※ 声カル時間外
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col relative-position">
                <div v-for="(memo, index) in memoFields" :key="memo.field" :ref="setMemoFieldRef">
                  <template v-if="showMemoField(memo.field)">

                    <!-- LAB RESULT LIST -->
                    <template v-if="index === 2">
                      <div ref="labResultListRef">
                        <div v-if="labResultList.length > 0">
                          <div class="q-mb-md">
                            {{ getCategoryName(labResultList[0]?.lab?.id_category2_lab) }}
                            <q-btn color="primary" label="編集" @click="openLabResultDetailModal(labResultList[0], labResultList, true)" />
                          </div>
                          <MtTable2
                            :columns="labResultColumns"
                            :rows="labResultList"
                            :rowsBg="true"
                            flat
                            :style="{overflow: 'auto', width: '50%'}"
                            :style-table-on-modal="true"
                            no-data-message="登録がありません。"
                            no-result-message="該当のデータが見つかりませんでした"
                          >
                            <template v-slot:body="{ row, index }">
                              <template v-if="row.flg_above_blank_row">
                                <q-tr>
                                <q-td colspan="10" class="bg-grey-300 full-width" style="height: 10px"></q-td>
                                </q-tr>
                              </template>
                              <q-tr>
                                <td
                                  class="cursor-pointer items-center"
                                  style="padding: 0px 15px !important;"
                                  v-for="col in labResultColumns"
                                  :class="row.is_deleted === true ? 'bg-yellow' : ''"
                                >
                                  <div>
                                    <div v-if="col.field === 'name_lab'">
                                      <div class="flex no-wrap full-width" :class="row.flg_indent ? 'q-pl-md' : ''">
                                        <div class="no-wrap text-weight-bold q-mr-md">
                                          {{ (row?.lab?.name_lab_en ? row?.lab?.name_lab_en : row?.name_lab_en)?.replace('%',' ') }}
                                        </div>
                                        <div class="no-wrap">
                                          {{
                                            row?.lab?.name_lab ? (row?.lab?.name_lab_en == row?.lab?.name_lab ? '' : row?.lab?.name_lab)
                                              : (row?.name_lab_en == row?.name_lab ? '' : row?.name_lab)
                                          }}
                                        </div>
                                      </div>
                                    </div>
                                    <div v-else-if="col.field === 'qualifier'">
                                      <MtFormInputText
                                        v-model="row.qualifier"
                                        class="q-pa-none q-mr-xs"
                                      >
                                        <template v-slot:append>                      
                                          <q-icon name="add" class="cursor-pointer" @click="openTemplateTextModal('lab', index)" />                      
                                        </template>
                                      </MtFormInputText>
                                    </div>
                                    <div v-else-if="col.field === 'val_result'">
                                      <MtFormInputText
                                        v-model="row.val_result"
                                        input-class="text-right"
                                      />
                                    </div>
                                  </div>
                                </td>
                              </q-tr>
                            </template>
                          </MtTable2>
                        </div>
                      </div>
                    </template>
                    <!-- END LAB RESULT LIST -->

                    <div :class="index !== 0 ? 'q-mt-md' : ''" class="text-weight-bold q-mb-sm">
                      {{ memo.label }}
                    </div>
                    <div class="q-my-md btn-container">

                      <!-- MED CONDITION -->
                      <template 
                        v-if="index === 0 || index === 1"
                        v-for="cliCodeCommon in sortBy(getCliCommonTypeMemoCarteList.filter(
                          (v) => {
                            if (index === 0) return v.flg_func1
                            return !v.flg_func1
                          }), ['display_order'])"
                        :key="cliCodeCommon.id_cli_common"
                      >
                        <q-btn color="primary" :label="cliCodeCommon.name_cli_common" :style="viewOnlyMemoCarte ? 'font-size: 10px;' : ''" class="q-mr-md" @click="
                            fetchCommonCliRecords(
                              cliCodeCommon.code_func1,
                              cliCodeCommon.flg_func1,
                              index
                            )
                            " />
                      </template>
                      <!-- END MED CONDITION -->

                    </div>
                    <template v-if="(index === 0 || index === 1) && clinicCommonList[index]"
                      v-for="(codeCommonList, idx) in clinicCommonList[index]" :key="idx">
                      <div class="row no-wrap items-center" :class="idx !== 0 ? 'q-mt-md' : ''">
                        <div class="col-auto">
                          <div class="field-label-free-color-small bg-grey-300 text-black q-pb-xs">
                            {{ codeCommonList.groupName }}
                          </div>
                        </div>
                        <div class="col">
                          <q-btn-group outline spread class="btn-options items-center">
                            <template v-for="cliCodeCommon in codeCommonList.clinicCommonData"
                              :key="cliCodeCommon.id_cli_common">
                              <q-btn :outline="medConditionData[index][idx].type_med_condition !=
                                cliCodeCommon.code_func1
                                " :label="cliCodeCommon.name_cli_common" :class="medConditionData[index][idx].type_med_condition ==
                                  cliCodeCommon.code_func1
                                  ? 'btn-border'
                                  : ''
                                  " @click="
                                    handleMedCondition(
                                      cliCodeCommon,
                                      index,
                                      idx,
                                      codeCommonList.flg_func1
                                    )
                                    " :style="{
                                      backgroundColor:
                                        medConditionData[index][idx]
                                          .type_med_condition ==
                                          cliCodeCommon.code_func1
                                          ? findTypeMedCondition(
                                            medConditionData[index][idx].text1
                                          )?.backgroundColor || '#0369a1'
                                          : '#0369a1',
                                      color:
                                        medConditionData[index][idx]
                                          .type_med_condition ==
                                          cliCodeCommon.code_func1
                                          ? findTypeMedCondition(
                                            medConditionData[index][idx].text1
                                          )?.color ?? 'white'
                                          : '',
                                      fontSize: viewOnlyMemoCarte ? '10px' : ''
                                    }" />
                            </template>
                          </q-btn-group>
                        </div>
                        <div class="col-5 flex items-center">
                          <MtInputForm type="text" class="q-mx-lg" label="補足メモ・所見"
                            autogrow style="flex: 1" v-model="medConditionData[index][idx].memo_record" />
                          <q-icon name="highlight_off" size="20px" color="red" class="cursor-pointer" @click="
                            deleteCliCommonRow(
                              index,
                              idx,
                              medConditionData[index][idx].id_med_condition
                            )
                            " />
                        </div>
                      </div>
                    </template>

                    <q-editor :toolbar="[
                      ['left', 'center', 'right', 'justify'],
                      ['bold', 'italic', 'strike', 'underline'],
                      ['undo', 'redo'],
                      ['token'],
                      [
                        {
                          label: $q.lang.editor.formatting,
                          icon: $q.iconSet.editor.formatting,
                          list: 'no-icons',
                          options: ['p', 'h2', 'h3', 'h4', 'h5']
                        }
                      ]
                    ]" @paste="onPasteEditor" :ref="setChildRef(index)" toolbar-bg="primary" toolbar-text-color="white"
                      toolbar-toggle-color="accent-700" class="editor q-mt-md"
                      :class="[!flgEditorToolbar ? 'hide-toolbar' : '']" v-model="data[memo.field]"
                      @focus="currentFocusedMemo = memo.field" @input="sanitizeInput(memo.field)">
                      <template v-slot:token>
                        <q-color @click="colorClicked(index)" v-model="foreColor[index]" no-header no-footer
                          default-view="palette" :palette="[
                            '#000000',
                            '#FF0000',
                            '#0000FF',
                            '#008000',
                            '#505050'
                          ]" unelevated class="q-mt-sm bg-primary color-picker" />
                      </template>
                    </q-editor>
                    <div :class="data[memo.field]?.replace(/<[^>]*>/g, '').length > 2000
                      ? 'text-red'
                      : ''
                      " class="flex justify-end q-mb-sm caption2 regular text-grey-600">
                      {{ data[memo.field]?.replace(/<[^>]*>/g, '').length }} / 2000
                    </div>
                  </template>
                </div>
              </div>
            </div>

            <div class="flex justify-start items-center" style="gap: 16px">
              <q-card v-for="(item, index) in schemaTextTemplateList" :key="index" class="grid-item flex align-center justify-center" @click="useTemplate(item)">
                <q-img
                  loading="lazy"
                  class=" rounded-borders"
                  fit="cover"
                  width="260px"
                  height="260px"
                  img-class="fetch-list-img"
                  aspect-ratio="1"
                  :src="item.img_file_path_template"  alt="Item Image" />

              </q-card>
            </div>
          </div>
        </q-scroll-area>
      </div>
      <div :class="isOpenLeftSidebar ? 'hidden' : 'col-2'">
        <q-scroll-area :style="scrollAreaHeight(100, 210)">
          <div>
            <div v-if="typeCarteConfigList[8]?.isChecked" class="row">
              <div class="full-width">
                <template
                  v-for="(file, index) in multipleImage.sort((a, b) => a?.name_file?.localeCompare(b?.name_file))"
                  :key="index">
                  <!-- other file type -->
                  <div class="relative-position full-width" v-if="file && file?.type_file && file.type_file == 99">
                    <MtFormCheckBox
                      class="absolute file-select-checkbox"
                      v-model:checked="file.checked"
                    />
                    <div class="absolute diagnostic-info q-pa-sm ellipsis q-ml-sm" v-if="file.type_diagnostic_info">
                      {{ getTypeDiagnosticInfo(file.type_diagnostic_info) }}
                    </div>
                    <div class="absolute diagnostic-info no-tag q-pa-sm q-ml-sm" v-else>未設定</div>
                    <PdfThumbnail
                      :pdfPath="file.filePathUi || file.file_path"
                    />
                    <q-badge color="red" floating transparent class="cursor-pointer"
                      @click="onFileRemoved(index, file.id_clinical_file)">
                      <q-icon name="close" />
                    </q-badge>
                  </div>
                  <!-- video file type -->
                  <div v-else-if="file && file?.type_file && file.type_file == 2" class="relative-position">
                    <MtFormCheckBox
                      class="absolute file-select-checkbox"
                      v-model:checked="file.checked"
                    />
                    <div class="absolute diagnostic-info q-pa-sm ellipsis" v-if="file.type_diagnostic_info">
                      {{ getTypeDiagnosticInfo(file.type_diagnostic_info) }}
                    </div>
                    <div class="absolute diagnostic-info no-tag q-pa-sm" v-else>未設定</div>
                    <video controls style="max-width: 100%; border-radius: 10px;">
                      <source :src="file.filePathUi || file.file_path" type="video/mp4" />
                    </video>

                    <q-badge class="cursor-pointer" color="red" floating transparent
                      @click="onFileRemoved(index, file.id_clinical_file)">
                      <q-icon name="close" />
                    </q-badge>
                  </div>
                  <!-- image file type -->
                  <div v-else class="relative-position">
                    <MtFormCheckBox
                      class="absolute file-select-checkbox"
                      v-model:checked="file.checked"
                    />
                    <div class="absolute diagnostic-info q-pa-sm ellipsis" v-if="file.type_diagnostic_info">
                      {{ getTypeDiagnosticInfo(file.type_diagnostic_info) }}
                    </div>
                    <div class="absolute diagnostic-info no-tag q-pa-sm" v-else>未設定</div> 
                    <img 
                      :src="file.filePathUi || file.file_path"
                      spinner-color="white"
                      :style="{
                        backgroundColor: 'gray',
                        width: '100%',
                        height: '200px',
                        borderRadius: '10px',
                        objectFit: 'cover'
                      }"
                    />
                    <q-badge color="red" floating class="cursor-pointer" transparent
                      @click="onFileRemoved(index, file.id_clinical_file)">
                      <q-icon name="close" />
                    </q-badge>
                  </div>
                </template>
              </div>
              <template v-if="multipleImage.length">
                <div class="flex justify-between full-width">
                  <div class="text-blue cursor-pointer" @click="checkedAllFiles">一括選択</div>
                  <div class="text-blue cursor-pointer" @click="openBulkUpdateModal">ファイル設定</div>
                </div>
              </template>
              <q-btn :disable="disableClinicalFileBtn" class="full-width" style="height: 200px"
                color="grey-300" text-color="grey-800" unelevated @click="$refs.fileInput.click()">
                <q-icon name="add" size="90px" />
              </q-btn>
              <input ref="fileInput" multiple style="display: none" type="file" @change="onFileChange($event)" />
            </div>
          </div>
        </q-scroll-area>
      </div>
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal(true)">
        <span>キャンセル</span>
      </q-btn>
      <q-btn class="q-ml-md" color="primary" unelevated @click="submit(true)">
        <span>保存して閉じる</span>
      </q-btn>
      <q-btn class="q-ml-md" color="primary" unelevated @click="submit(false)">
        <span>保存</span>
      </q-btn>
    </div>
  </q-card-section>
  <!-- </div> -->

  <AddTextTemplateModal v-model:value="addTemplateModalFlg" :data="data" :options="textTemplatesList"
    modelTitle="メモカルテ テンプレート" @update:memo="handleUpdateMemo" />
  <q-dialog v-model="petCartePdfConfirmationDialog">
    <q-card class="q-pa-lg">
      <q-card-actions class="justify-center">
        <q-btn label="キャンセル" text-color="primary" outline color="white" v-close-popup />
        <q-btn label="PDFダウンロード" color="primary" @click="generatePetCartePdf('download')" v-close-popup />
        <q-btn label="印刷" color="primary" @click="generatePetCartePdf('print')" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<style lang="scss" scoped>
.temp::after {
  content: '℃';
}

.mmHg::after {
  content: 'mmHg';
}

.blood-percent::after {
  content: '%';
}

.respiration-amount::after {
  content: '回/分';
}

.bpm::after {
  content: '回/分 (bpm)';
}

:deep(.q-editor__content) {
  padding: 16px;
}

.editor {
  line-height: 1.7;

  :deep(.q-editor__toolbar-group) {
    &:last-child {
      margin-left: -90px;
    }
  }
}

.hide-toolbar {
  :deep(.q-editor__toolbars-container) {
    display: none;
  }
}

.btn-border {
  border-top: 1px solid #9e9e9e;
  border-bottom: 1px solid #9e9e9e;
  border-right: 1px solid #9e9e9e;

  .q-btn--outline:first-child:before {
    border-left: 1px solid #9e9e9e;
  }
}

.btn-options {
  border-radius: 20px;

  .q-btn--outline:before {
    border: 1px solid #9e9e9e;
  }

  .q-btn--outline:not(:first-child):before {
    border-left: 0;
  }
}

.file-container {
  border: 2px dashed transparent;
  transition: border-color 0.3s ease;
  position: relative;

  &.drag-over {
    border-color: $blue;
    background-color: rgb(62, 127, 255, 0.15);
  }

  .upload-icon {
    position: absolute;
    color: $blue;
    z-index: 2;
    background-color: rgb(62, 127, 255, 0.25);
    border-radius: 100%;
    padding: 8px;
    left: 50%;
    bottom: 12px;
  }
}

.custom-mic-button-container {
  width: 100%;
  justify-items: right;
}

.ai-container-btn {
  position: absolute;
  right: 5px;
  top: 20px;
}

.custom-mic-button {
  border-radius: 50%;
  padding: 9px 5px;
  height: 55px;
  width: 55px;
}

.custom-text-mic {
  font-size: 12px;
}

.btn-container {
  width: 100%;
  text-align: left;
}

.btn-container button {
  display: inline-block;
  margin-bottom: 10px;
}

.header-title {
  flex-wrap: nowrap;
}

.file-select-checkbox {
  z-index: 999;
  :deep(.q-checkbox__bg) {
    width: 30px;
    height: 30px;
    background: $primary;
  }
}

.diagnostic-info {
  z-index: 999;
  background: rgba(0, 0, 0, .8);
  border-radius: 5px;
  color: #fff;
  bottom: 10px;
  left: 10px;
  max-width: 95%;
  &.no-tag {
    background: rgba(255, 255, 255, .8);
    color: red;
  }
}

.memo-carte-header {
  @media screen and (max-width: 1024px) {
    .q-btn {
      padding: 4px 4px;

      :deep(.q-icon) {
        font-size: 16px;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .q-btn {
      padding: 4px 0;
      margin-right: 2px;

      :deep(.q-icon) {
        font-size: 12px;
      }
    }

    .q-btn--round {
      min-width: 1.5em;
    }

    .q-toolbar__title,
    :deep(.pet-name),
    :deep(.pet-kana-name) {
      font-size: 10px !important;
    }
  }
}
</style>
