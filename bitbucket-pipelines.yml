pipelines:
  pull-requests:
    '**':
      - step:
          name: Pre-Build
          size: 2x
          image: node:18-alpine
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max-old-space-size=4096"
            - echo "Starting memory monitoring..."
            - |
              while true; do
                echo "----------- Memory Usage -----------"
                free -m
                echo "------------------------------------"
                sleep 5
              done &
            - echo "Clearing the npm cache..."
            - npm cache clean --force
            - npm ci  # Install dependencies from package-lock.json
            - echo "API_URL=$API_URL" >> .env  # Manually add API_URL to .env
            - npx vite build --mode dev --no-sourcemap
