<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import { getDateToday } from '@/utils/aahUtils'
import useIllnessHistoryStore from '@/stores/illness-history'
import useCustomerStore from '@/stores/customers'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import OptionModal from '@/components/OptionModal.vue'
import { Platform } from 'quasar'
import { typeDiagnosticInfo } from '@/utils/enum'
import { ClinicalFile } from '@/types/types'
import dayjs from 'dayjs'
import FabricMemoCarteModal from '@/pages/memoCarte/FabricMemoCarteModal.vue'
import MtPetInfo from '@/components/MtPetInfo.vue'
import usePetStore from '@/stores/pets'

const emits = defineEmits(['close'])
const props = withDefaults(defineProps<{
  data: object;
  onCompleteCallback: Function;
  onTitleClick: Function;
  enableTitleClickFlg: boolean;
}>(), {
  data: () => ({}),
  onCompleteCallback: () => {},
  onTitleClick: () => {},
  enableTitleClickFlg: false,
})
const illnessHistoryStore = useIllnessHistoryStore()
const customerStore = useCustomerStore()
const petStore = usePetStore()

const petList: any = ref([])
const data = ref({
  id_clinical_file: null,
  id_customer: null,
  id_pet: null,
  file_path: null,
  id_pet_illness_history: [],
  type_provider: 1,
  name_file: null,
  type_file: 1,
  type_receive_format: 2,
  type_diagnostic_info: null,
  memo_file_storage: null,
  id_employee_supplier: null,
  name_supplier_other: null,
  datetime_receive: getDateToday(),
  id_clinic: null,
  flg_delete: false
})
const filePaths = ref<Array<string | ArrayBuffer | null>>([])
const multipleData = ref<ClinicalFile[]>([
  {
    id_clinical_file: null,
    id_customer: null,
    id_pet: null,
    file_path: null,
    id_pet_illness_history: [],
    type_provider: 1,
    name_file: null,
    type_file: 1,
    type_receive_format: 2,
    type_diagnostic_info: null,
    memo_file_storage: null,
    id_employee_supplier: null,
    name_supplier_other: null,
    datetime_receive: getDateToday(),
    id_clinic: null
  }
])
const currentMultipleData = ref(0)

const uploadNew = ref(false)
const doSubmit = ref(false)
const f_status = ref('unchanged')
const previewImage = ref(false)
const isDragging = ref(false)
const selectedTypeDiagnostics = ref([])
const isEdit = ref(false)
const petName = ref('')
const uploader = ref(null)
const { getCustomer } = storeToRefs(customerStore)

const closeModal = () => {
  emits('close')
}
const openMenu = async () => {
  let menuOptions = [
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm(aahMessages.delete_ask, aahMessages.delete)
        .then((confirmation) => {
          if (confirmation) {
            let key = props.data.fileKey
            petStore
              .updatePet(props.data.id_pet, props.data.id_customer, {
                [key]: ''
              })
              .then(async () => {
                await customerStore.selectCustomer(props.data.id_customer, true)
                customerStore.selectPet(props.data.id_pet)
                mtUtils.autoCloseAlert(aahMessages.success)
                closeModal()
              })
          }
        })
    }
  }
}
const init = (newData: ClinicalFile = null) => {
  petList.value = getCustomer.value?.pets
  // data.value = JSON.parse(JSON.stringify(newData ? newData : props.data))
  multipleData.value[currentMultipleData.value] = JSON.parse(
    JSON.stringify(newData ? newData : props.data)
  )

  // if image is exist from api response
  if (props.data?.file_path) {
    doSubmit.value = true
    previewImage.value = true
    isEdit.value = true
    filePaths.value[currentMultipleData.value] = props.data?.file_path
  }

  // if image is exist from api response
  if (newData?.file_path) {
    doSubmit.value = true
    previewImage.value = true
    isEdit.value = true
    filePaths.value[currentMultipleData.value] = newData?.file_path
  }

  if (props.data?.type_diagnostic_info) {
    if (props.data?.type_diagnostic_info.length > 0) {
      props.data?.type_diagnostic_info.map((item) => {
        let a = typeDiagnosticInfo.find((option) => option.value == item)
        selectedTypeDiagnostics.value.push(a)
      })
    } else {
      const type_diagnostic = typeDiagnosticInfo.find((option) => option.value == props.data?.type_diagnostic_info)
      selectedTypeDiagnostics.value.push(type_diagnostic)
    }
  }

  if(newData) {
    if (newData?.type_diagnostic_info) {
      selectedTypeDiagnostics.value.length = 0
      if (newData?.type_diagnostic_info.length > 0) {
        newData.type_diagnostic_info.map((item) => {
          let a = typeDiagnosticInfo.find((option) => option.value == item)
          selectedTypeDiagnostics.value.push(a)
        })
      } else {
        const type_diagnostic = typeDiagnosticInfo.find((option) => option.value == newData?.type_diagnostic_info)
        selectedTypeDiagnostics.value.push(type_diagnostic)
      }
    }
    else {
      selectedTypeDiagnostics.value.length = 0
    }
  }
  petName.value = props.data?.id_pet
  if (props.data?.id_pet_illness_history) {
    const id_pet_illness_history_list = [...data.value.id_pet_illness_history]
    data.value.id_pet_illness_history.length = 0
    illnessHistoryStore.getIllnessHistorys.forEach((item: any) => {
      id_pet_illness_history_list.forEach((id: string) => {
        if (id == item.id_pet_illness_history) {
          data.value.id_pet_illness_history.push(item.id_pet_illness_history)
        }
      })
    })
  }
  data.value.id_clinic = data.value.id_clinic
  if (!isEdit.value) {
    multipleData.value[currentMultipleData.value].type_file = 1
    multipleData.value[currentMultipleData.value].type_provider = 1
    multipleData.value[currentMultipleData.value].type_receive_format = 2
    multipleData.value[currentMultipleData.value].datetime_receive =
      dayjs().format('YYYY/MM/DD HH:mm:ss')
  }
}

const openEditModal = async () => {
  await mtUtils.fullHeightPopup(FabricMemoCarteModal, {
    id_memo_carte: null,
    id_customer: data.value?.id_customer,
    id_pet: data.value?.id_pet,
    isDirectSubmit: true,
    id_pet_illness_history: [illnessHistoryStore.getIllnessHistory?.id_pet_illness_history],
    imageUrl: filePaths.value[currentMultipleData.value],
    isEdit: true,
    id_clinical_file: data.value?.id_clinical_file
  })
  closeModal()
}

const maxBoundary = 360;
const clamp = (value: number, min: number, max: number) => {
  return Math.min(Math.max(value, min), max)
};

const panImage = (index: number, event: MouseEvent) => {
  const img = imageStates[index]
  if (img.dragging) {
    img.translateX = clamp(event.clientX - img.startX, -maxBoundary, maxBoundary);
    img.translateY = clamp(event.clientY - img.startY, -maxBoundary, maxBoundary);
  }
};
const stopPan = (index: number) => {
  const img = imageStates[index]
  img.dragging = false;
  document.removeEventListener("mousemove", (e) => panImage(index, e));
  document.removeEventListener("mouseup", () => stopPan(index));
};


// Comparison Module
const isCompareView = ref(false)

onMounted(async () => {
  init()
})
</script>
<template>
  <section class="column bg-black full-height clinical-files">
    <MtModalHeader class="col-auto" style="display: none" @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
        PDF Preview
      </q-toolbar-title>
      <q-btn v-if="isEdit" flat round @click="openMenu" class="q-mx-md">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
      <q-btn v-if="isEdit" flat round @click="openEditModal" class="q-mx-md">
        <q-icon size="xs" name="edit" />
      </q-btn>
    </MtModalHeader>

    <q-card-section class="col row gap-4 full-height text-white q-pa-none relative-position">
      <div
        class="row justify-between items-start q-pa-sm absolute-top"
        style="z-index: 2;"
      >
        <section class="col flex">
          <q-card
            style="
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              -webkit-backdrop-filter: blur(4px);
              border-radius: 8px;
            "
          >
            <q-card-section class="q-pa-sm" style="opacity: 1">
              <MtPetInfo :enable-title-click-flg="props.enableTitleClickFlg" :on-title-click="props.onTitleClick" version="v2" class="ellipsis full-width" />
            </q-card-section>
          </q-card>
        </section>
        <section class="col-auto text-right">
          <q-btn
            v-if="isEdit"
            class="q-mx-md"
            flat
            round
            @click="openMenu"
          >
            <q-icon size="xs" color="white" name="more_horiz" />
          </q-btn>
          <q-btn
            flat
            round
            @click="closeModal"
          >
            <q-icon size="xs" color="white" name="close" />
          </q-btn>
        </section>
      </div>
      <!-- Clinical File Content -->
      <section
        v-if="previewImage"
        class="col"
        :class="{'self-end row': isCompareView }"
        :style="{
          height: isCompareView ? 'calc(100% - 76px)' : '100%'
        }"
      >
        <div
          id="left-file__compare"
          class="col full-height row justify-center items-center relative-position"
          :class="{
            'q-mx-auto': isCompareView
          }"
        >
          <iframe
            :src="(() => {
              if(Platform.is.ipad && filePaths[currentMultipleData]?.includes('.pdf')) {
                return `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(filePaths[currentMultipleData])}`
              } else {
                return filePaths[currentMultipleData]
              }
            })()"
            style="
              position: absolute;
              bottom: 0;
            "
            :style="{
              height: isCompareView ? '100%' : '88%',
              width: isCompareView ? '100%' : '60%'
            }"
            frameborder="0"
          ></iframe>
        </div>
      </section>
    </q-card-section>
  </section>
</template>
<style lang="scss" scoped>
.upload-section {
  border: 1px dotted $grey-500;
  padding: 0;
  height: auto;
  cursor: pointer;
}

.rpd {
  width: 100%;
}

.rpd > img {
  width: 100%;
}

.q-uploader.hide-preview :deep(.q-uploader__list) {
  display: none;
}

.preview-button-prev {
  position: absolute;
  bottom: 50%;
  left: 12px;
  color: $white;
  background-color: $black;
  opacity: 0.4;
  z-index: 2;
}
.preview-button-next {
  position: absolute;
  bottom: 50%;
  right: 12px;
  color: $white;
  background-color: $black;
  opacity: 0.4;
  z-index: 2;
}
.clinical-files {
  border: 2px dashed transparent;
  transition: border-color 0.3s ease;
  position: relative;
  background-color: black;

  &.drag-over {
    border: 2px dashed $blue;
    background-color: lightblue !important;
  }

  .upload-icon {
    position: absolute;
    color: $blue;
    z-index: 2;
    background-color: rgb(62, 127, 255, 0.25);
    border-radius: 100%;
    padding: 8px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%)
  }
}

.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-to, .fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.card-form__scroll {
  :deep(.q-scrollarea__content) {
    display: flex;
    flex-direction: column;
  } 
}
.column-scroll__container {
  :deep(.q-scrollarea__container) {
    flex: 10000 1 0%;
    min-height: 0%;
    max-height: 100%
  }
}

.img-wrapper {
  width: 60%;
  height: 100%;
  transform-origin: center;
  overflow: hidden;
  touch-action: none;
}
</style>
