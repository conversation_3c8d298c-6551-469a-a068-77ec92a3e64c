export const menuHelperContents = {
  customerPetList: {
    title: '検索ヘルパー',
    content: '<ul><li>検索対象：オーナー、ペット、電話番号、住所</li><li>診察券番号の検索は「0000-」最後に"-"を追加してエンターキー</li><li>電話番号の検索は「0000t」最後に"t"を追加してエンターキー</li></ul>'
  },
  requestDetail: {
    title: '',
    content: ''
  },
  employeeList: {
    title: '',
    content: ''
  },
  empInfoList: {
    title: '',
    content: ''
  },
  breedDiseaseList: {
    title: '',
    content: ''
  },
  categoryList: {
    title: '',
    content: ''
  },
  labList: {
    title: '検査項目マスタ',
    content: '<ul><li>検査項目の基準値を動物種・性別・年齢で設定できます。</li></ul>'
  },
  commonList: {
    title: '',
    content: ''
  },
  cliCommonList: {
    title: '',
    content: ''
  }
}

export const itemServiceHelperContents = {
  itemServiceViewPage: {
    title: '商品・サービスヘルパー',
    content: `
      <ul class="normal-content">
        <li>商品・サービス（略して商品）は親子関係で管理を行います。<br/>例えば、<strong>親商品</strong>に<span class="text-marker">「予防接種ワクチン」</span>、<strong>子商品</strong>に<span class="text-marker">「バンガード®プラス 5/CV」</span>などの商品バリエーションを追加します。<br/>このような階層で子商品を追加すると、商品を整理することができ、商品検索時に便利です。</li>
        <li>
          <span class="text-marker">商品データは4階層</span>で管理されています。<br/>
          <strong>
            第一階層：<span class="text-grey-400 q-mr-sm">ー</span>商品またはサービス<br/>
            第二階層：<span class="text-grey-400 q-mr-sm">ーー</span>区分<br/>
            第三階層：<span class="text-grey-400 q-mr-sm">ーーー</span>大分類<br/>
            第四階層：<span class="text-grey-400 q-mr-sm">ーーーー</span>中分類
          </strong><br/>
          編集することは可能ですが、重要なマスタデータの為、変更操作は慎重に行ってください。
        </li>
        <li>
          商品区分によって、リクエスト上でのオーダーセクション（治療サービス、処方箋、注射・点滴）で商品検索を行うことができます。<br/>
            <strong><span class="text-marker">治療サービス</span></strong>：商品区分が「医薬品」以外の全ての商品<br/>
            <strong><span class="text-marker">処方箋</span></strong>：商品区分が「医薬品」かつ、分類（大分類または中分類）が「注射」を除く商品<br/>
            <strong><span class="text-marker">注射・点滴</span></strong>：商品区分が「医薬品」かつ、分類（大分類または中分類）から「注射」に該当する商品<br/>
        </li>
      </ul>
    `
  },
  itemServiceViewPage2: {
    title: '商品検索の活用',
    content: `
      <ul class="normal-content">
        <li>
          検索時には以下を対象として検索します。<br/>
          ・サービス商品名<br/>
          ・サービス商品名カナ<br/>
          ・診療明細表示名<br/>
          ・医薬品有効成分名<br/>
          ・大・中分類名<br/>
          ・検索語句1-3、子商品名（商品名・包装単位名）
        </li>
        <li>検索語句を工夫して検索しやすくしてください。検索語句が3つ以上必要な場合には、キーワードの後にスペースを入れて次のキーワードを含めてください。各フィールドに100文字まで入力できます。</li>
      </ul>
    `
  },
  itemServiceViewPage3: {
    title: '診療明細の表示名',
    content: `
      <ul class="normal-content">
        <li>会計時の診療明細の名称を定義できます。</li>
        <li>
          <span class="text-marker">デフォルト名は「診療明細表示名」</span>ですが、以下を<a href="/SearchClinicList?tab=会計">病院設定 会計タブ</a>で指定した場合にはオーダー分類により別の会計明細名を指定できます。<br/>
          ・親商品名<br/>
          ・子商品名<br/>
          ・大分類名<br/>
          ・中分類名<br/>
          ・商品区分名
        </li>
      </ul>
    `
  },
  itemServiceViewPage4: {
    title: '品名包装単位',
    content: `
      <ul class="normal-content">
        <li><span class="text-marker">品名包装単位</span>とは、<span class="text-marker">子商品</span>です。</li>
        <li>親商品のみ登録ではオーダーへの追加は出来ません。<br/>必ず品名包装単位も追加します。</li>
        <li class="no-list-style">
          <ul>
            <li>
              例① <strong>親商品</strong>に<span class="text-marker">「予防接種ワクチン」</span>、<strong>子商品</strong>に<span class="text-marker">「バンガード®プラス 5/CV」</span>や<span class="text-marker">「バンガード®プラス CPV」</span>などの商品バリエーションを追加します。
            </li>
            <li>
              例② <strong>親商品</strong>に<span class="text-marker">「入院」</span>、<strong>子商品</strong>に<span class="text-marker">「日帰り入院 ~10kg未満」</span>や<span class="text-marker">「ICU入院 大型」</span>などの商品バリエーションを追加します。
            </li>
          </ul>          
        </li>
        <li>品名包装単位名は検索対象です。</li>
        <li>品名包装単位名で料金を指定します。</li>
        <li>医薬品の品名包装単位名の場合、有効成分量を指定します。</li>
      </ul>
    `
  },
  itemServiceViewPage5: {
    title: 'オプション',
    content: `
      <ul class="normal-content">
        <li>オーダー商品の検索時に本商品のオプションとして<span class="text-marker">同時に表示される商品</span>を登録できます。</li>
        <li>同類のオーダー（治療サービス/処方箋/注射・点滴）の商品しか利用できません。</li>
      </ul>
    `
  },
  itemServiceViewPage6: {
    title: '対象動物',
    content: `
      <ul class="normal-content">
        <li>
          オーダー追加時に<span class="text-marker">対象動物で商品の絞込</span>ができます。<br/>
          例えば、ウサギの診療時には、ウサギ専用の商品のみを検索することができます。
        </li>
      </ul>
    `
  },
  itemServiceViewPage7: {
    title: 'TOP30',
    content: `
      <ul class="normal-content">
        <li>オーダー追加時に利用します。</li>
        <li>クイックに照会したい商品のみ適用します。</li>
        <li>半角数字を1-30で入力してください。<br/>小さな数字を上位表示します。</li>
        <li>30以上で入力しても表示します。</li>
      </ul>
    `
  },
  itemServiceViewPage8: {
    title: '服用量の計算',
    content: `
      <ul class="normal-content">
        <li>医薬品をオーダーとして登録する際、以下の4通りの方法が医薬品単位で設定できます。</li>
        <li>①<span class="text-marker">早見表</span>：体重範囲を指定し、品名包装単位に服用量を指定できます。早見表を設定すると体重に応じて自動計算を行います。</li>
        <li>②<span class="text-marker">perキロ（可変）</span>：体重に応じた適用力価の範囲を指定します。オーダー追加時に体重に応じて自動で服用力価を設定します。</li>
        <li>③<span class="text-marker">perヘッド（可変）</span>：1個体に適用する力価範囲を指定します。オーダー追加時に自動で服用力価を設定します。</li>
        <li>④<span class="text-marker">数量指定</span>：オーダー追加時に手動で数量を指定します。自動計算は適用されません。</li>
        <li>上記、①ー③を医薬品に指定する際、表示ページ（保存後の次のページ）で詳細データを追加・編集できます。</li>
      </ul>
    `
  },
  itemServiceViewPage9: {
    title: '処方料金の計算区分について',
    content: `
      <div>処方箋や注射のオーダーを会計に登録する際、<span class="text-marker">売上量が 1 に満たない場合の計算（繰り上げ）</span>をどう処理するかを指定できます。</div>
      <ul class="normal-content">
        <li>①<b>無処理（そのまま）</b><br/>
            オーダーで登録された 1 未満の数字をそのまま会計に適用します。例えば、医薬品Aの数量が 0.25 の場合には、会計明細の数量も 0.25 になります。<br/>
            <span class="btn-label-light-grey q-mr-md">例 1</span>オーダー <b>0.25</b>　➡　会計 <span class="text-marker"><b>0.25</b></span></li>
        <li>②<b>1(錠)未満を1(錠)へ繰上</b><span class="btn-label-1 q-ml-md">服用毎に適用</span><br/>
            投与/服用1回当たりの（数）量が1未満の場合、その数量を 1 にします。例えば、医薬品Aの1回当たりの数量が0.25の場合には、会計明細の初期値は（1回当たりで）数量を 1 に繰り上げます。<br/>
            <span class="btn-label-light-grey q-mr-md">例 1</span>オーダー 1回当たり 0.25 BID 2日間 <b>総量 1</b>　➡　会計 <span class="text-marker"><b>4</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 2</span>オーダー 1回当たり 1.5 BID 2日間 <b>総量 6</b>　➡　会計 <span class="text-marker"><b>8</b></span></li>
        <li>③<b>総量に1(錠)未満が生じる場合、1(錠)へ繰上</b><span class="btn-label-1 q-ml-md">総量に適用</span><br/>
            投与/服用1回当たりの（数）量は無関係で、服用総量に1未満の数量が含まれる場合、その 1 未満の数量を 1 に繰り上げます。例えば、医薬品Aの1回当たりの数量が0.5、TID 3日間の場合には、会計明細の初期値が 5 になります。<br/>
            <span class="btn-label-light-grey q-mr-md">例 1</span>オーダー 1回当たり 0.5 TID 3日間 <b>総量 4 + 1/2</b>　➡　会計 <span class="text-marker"><b>5</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 2</span>オーダー 1回当たり 0.5 BID 4日間 <b>総量 6</b>　➡　会計 <span class="text-marker"><b>6</b></span></li>
        <li>④<b>1/2以下を1/2(錠)へ繰上</b><span class="btn-label-1 q-ml-md">服用毎に適用</span><br/>
            投与/服用1回当たりの（数）量を常に1/2単位でカウントします。例えば、医薬品Aの1回当たりの数量が1/2未満の0.25の場合には1/2へ繰り上げ、0.75の場合には 1 へ繰り上げます。<br/>
            <span class="btn-label-light-grey q-mr-md">例 1</span>オーダー 1回当たり 1/3 BID 2日間 <b>総量 1 + 1/3</b>　➡　会計 <span class="text-marker"><b>2</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 2</span>オーダー 1回当たり 1/2 BID 2日間 <b>総量 2</b>　➡　会計 <span class="text-marker"><b>2</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 3</span>オーダー 1回当たり 2/3 BID 2日間 <b>総量 2 + 2/3</b>　➡　会計 <span class="text-marker"><b>4</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 4</span>オーダー 1回当たり 1 + 1/3 BID 2日間 <b>総量 5 + 1/3</b>　➡　会計 <span class="text-marker"><b>6</b></span></li>
        <li>⑤<b>1/2以下を1/2(錠)へ繰上、1/2以上はそのまま</b><span class="btn-label-1 q-ml-md">服用毎に適用</span><br/>
            投与/服用1回当たりの（数）量が 1/2未満の値を含む場合、その数量を 1 にします。例えば、医薬品Aの1回当たりの数量が0.33 (1/3) の場合には、会計明細の初期値は（1回当たりで）数量を 1 に繰り上げます。<br/>
            <span class="btn-label-light-grey q-mr-md">例 1</span>オーダー 1回当たり 1/3 BID 2日間 <b>総量 1 + 1/3</b>　➡　会計 <span class="text-marker"><b>2</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 2</span>オーダー 1回当たり 1/2 BID 2日間 <b>総量 2</b>　➡　会計 <span class="text-marker"><b>2</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 3</span>オーダー 1回当たり 2/3 BID 2日間 <b>総量 2 + 2/3</b>　➡　会計 <span class="text-marker"><b>2 + 2/3</b></span><br/>
            <span class="btn-label-light-grey q-mr-md">例 4</span>オーダー 1回当たり 1 + 1/3 BID 2日間 <b>総量 5 + 1/3</b>　➡　会計 <span class="text-marker"><b>5 + 1/3</b></span></li>
        <li>新規の医薬品を登録する際、処方料金の計算区分の初期値を設定する場合には「<a href="../SearchClinicList?tab=%E5%87%A6%E6%96%B9%E7%AE%8B" target="_blank">病院・施設</a>」設定画面から行えます。</li>
      </ul>
    `
  },
    itemServiceViewPage10: {
    title: '早見表',
    content: `
      <div class="q-ma-md q-mb-lg">
        <h4>早見表の活用メリット📌</h4>
        <ol class="normal-content">
          <li><b>簡単、誰でも同じ結果</b><br/>
              医薬品を選ぶだけで<span class="text-marker"> 1秒で完了</span>。誰が登録しても同じ結果で難しさナシ。</li>
          <li><b>ヒューマンエラー防止</b><br/>
              手動計算が不要で、<span class="text-marker"> 投与量ミスを大幅軽減 </span>。早見表を見れば、選択した体重と薬剤の組み合わせがひと目でわかる。</li>
          <li><b>スピーディ</b><br/>
              オーダー登録時にワンクリックで呼び出せるので、<span class="text-marker"> 時間短縮に大きく貢献 </span>。複数の子商品（薬剤バリエーション）がある場合も、横並びで比較OK。</li>
          <li><b>種別ごとの最適化</b><br/>
              「犬」「猫」「全種」など<span class="text-marker"> 動物種を分けて管理 </span>できるので、同じ体重・同じ薬でも、種別に応じた投与量を自動切り替え。</li>
        </ol>
      </div>
      <div class="text-center"><img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_7.png" width="85%" /></div>
      <div class="q-ma-md">
        <h4>登録手順💡</h4>
        <ol class="normal-content">
          <li><b>商品設定</b><br/>
              この画面の一つ前の画面（サービス・商品）の右上の 鉛筆✏️アイコンをクリックし編集画面に入ります。<br/>
              「服用量の計算」という登録項目の「早見表」をチェックして保存してください。</li>
          <li><b>子商品（品名包装単位）の登録（図の①）</b><br/>
              「品名（包装単位）＋」ボタンから医薬品のバリエーションを登録します。<br/>
              表示順を設定すると見やすい並び順へ変更できます。</li>
          <li><b>体重範囲の登録（図の②）</b><br/>
              「体重範囲＋」ボタンをクリックします。下限・上限をグラム単位で入力（例： 5kg → 5000g）。<br/>
              保存ボタンを押下すると早見表の体重範囲が反映されます。重複・漏れのない連続範囲を登録します。<br/>
              表示順を設定すると見やすい並び順へ変更できます。</li>
          <li><b>投与量セルの編集（図の③）</b><br/>
              子商品と体重範囲の交点セルをクリックします。ポップアップに投与量を入力（例：0.2mL、1/2錠、1錠）。<br/>
              保存ボタンで表へ数値が反映されます。再編集もワンクリックでOKです。</li>
          <li><b>動物種の設定</b><br/>
              体重範囲を指定する際にポップアップ上のプルダウンから対象動物種を選択してください。<br/>
              未選択時は「全種」として共通設定に。「全種」と「犬」の両方がある場合は、「犬」の表が優先適用されます。</li>
        </ol>
      </div>
    `
  },
    itemServiceViewPage11: {
    title: '可変量',
    content: `
      <div class="q-ma-md q-mb-lg">
        <h4>可変量の活用メリット📌</h4>
        <div>「<span class="text-marker"><b> per kg </b></span>」と「<span class="text-marker"><b> per head </b></span>」の二種類の可変量の登録が可能です。<br/>
        早見表の利用ができるのがベストですが、体重や治療目的に応じて有効成分量を自由に変更する場合に有用な自動計算方法です。</div>
        <ol class="normal-content">
          <li><b>ヒューマンエラーの低減</b><br/>
             手動による計算ミスやケアレスエラーを防ぎ、適正投与量を自動的に算出することで、<span class="text-marker"> 誤投与リスクを大幅に抑制 </span>します。<br/>
             これにより医療過誤の可能性が減り、患者安全性が向上します。</li>
          <li><b>業務効率の向上</b><br/>
              手計算せずに済むため、カルテ記入や投薬準備の時間を大幅に短縮します。<br/>
              錠剤で処方する場合には錠剤分割オプションまで考慮して、例えば「この薬は1/3分割を考慮」など追加条件を出せる為、<span class="text-marker"> 単なる可変量計算ではなく、薬局の作業効率アップ </span>まで含め、現場のワークフローが改善されます。</li>
          <li><b>種別ごとの最適化</b><br/>
              「犬」「猫」「全種」など<span class="text-marker"> 動物種を分けて管理 </span>できるので、同じ体重・同じ薬でも、種別に応じた投与量を自動切り替えします。</li>
        </ol>
      </div>
      <div class="text-center"><img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_8.png" width="85%" /></div>
      <div class="q-ma-md">
        <h4>登録手順💡</h4>
        <ol class="normal-content">
          <li><b>商品設定</b><br/>
              この画面の一つ前の画面（サービス・商品）の右上の 鉛筆✏️アイコンをクリックし編集画面に入ります。<br/>
              「服用量の計算」という登録項目の「per キロ (可変)」または「per ヘッド (可変)」をチェックして保存してください。</li>
          <li><b>子商品（品名包装単位）の登録（図の①）</b><br/>
              「品名（包装単位）＋」ボタンから医薬品の子商品を登録します。<br/>
              この際、有効成分も登録してください。</li>
          <li><b>有効成分 範囲の登録（図の②）</b><br/>
            可変 有効成分量 範囲として以下の4項目を設定してください。
            <ol class="normal-content">
              <li>下限・上限の有効成分範囲</li>
              <li>単位</li>
              <li>範囲内の初期値</li>
              <li>適用する動物種</li>
            </ol>
          </li>
          <li><b>動物種の設定</b><br/>
              未選択時は「全種」として共通設定に。「全種」と「犬」の両方がある場合は、「犬」の表が優先適用されます。</li>
        </ol>
      </div>
    `
  },
}

export const itemServiceUnitHelperContents = {
  itemServiceUnitViewPage1: {
    title: '有効成分の設定・便利な方法',
    content: `
      <div class="q-ma-md q-mb-lg">
        <h4 class="q-mt-none">可変量と有効成分 💡</h4>
        <div class="q-mb-md">
          医薬品のマスタで可変量と有効成分含有量（<span class="text-marker">力価 <small>※1</small></span>）を事前に登録しておくと、オーダー登録時に<span class="text-marker">参考投与量を算出</span>できます。<br/>
          <span class="caption1 regular text-grey-600">※1 … 力価は通常ワクチンなどの生物学的効果の単位として利用されますが、ここでは有効成分の含有量レートと同義として説明します。</span>
        </div>
        <div class="q-mb-md">
          力価計算が必要になる<span class="text-marker">錠剤や粉剤、注射・点滴</span>などの製剤に使用します。
        </div>
      </div>
      <div class="q-ma-md q-mb-lg">
        <h4 class="q-mt-none">使用例 ☝🏼</h4>
        <div class="text-center q-mb-md"><img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_1.png" width="80%" /></div>
        <div class="q-mb-md">
          例えば、医薬品Aは1錠に有効成分Aが<span class="text-marker"> 10mg </span>含有されている製剤だとします。
        </div>
        <ol class="normal-content">
          <li><b>有効成分のレート（力価）を決める</b><br/>
             獣医師の指示で、有効成分Aを<span class="text-marker"> 1mg/kg (力価) </span>で投与するとします。</li>
          <li><b>必要な有効成分量が求まる</b><br/>
              犬の体重から必要量を算出すると、 1mg/kg x 5kg = 5mg となり、有効成分Aは<span class="text-marker"> 5mgの必要 </span>となります。</li>
          <li><b>必要錠数が求まる</b><br/>
              本剤は1錠に有効成分Aが10mg含まれるため、<span class="text-marker"> 5mg/(10mg/錠) = 0.5錠 </span>となり、1/2錠を投与すれば5mgを得ることができます。</li>
        </ol>
        <div class="q-mb-md">
          このような自動計算を行う場合に有効成分の設定が有用です。
        </div>
      </div>
      <div class="q-ma-md q-mb-lg">
        <h4 class="q-mt-none">利用方法 🔰</h4>
        <div class="q-mb-md">
          有効成分を活用するには、<span class="text-marker">2ステップの事前登録</span>が必要です。
        </div>
        <ol class="normal-content">
          <li>品名包装単位（子商品）に<strong>有効成分</strong>を登録する</li>
          <li>商品（親商品）に<strong>可変量 ( per Kg または per head )</strong> を登録する<br/>
            <small class="text-grey-700">
              医薬品の可変量は一つ前の画面で設定が可能です。<br/>
              ご利用前には医薬品の編集ページで「服用量の計算」セクションで「per キロ (可変)」や「per ヘッド (可変)」にチェックを入れてください。
            </small>
          </li>
          <li>オーダー（処方箋または注射薬）を登録する際に、<span class="text-marker">「可変量」を選択</span>すると、有効成分データを使用して計算します。<br/>
            <img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_2.png" width="60%">
          </li>
        </ol>
      </div>
      <div class="q-ma-md q-mb-lg">
        <h4 class="q-mt-none">補足情報 👀</h4>
        <div class="q-mb-md">
          1つの医薬品に複数の子商品バリエーション（有効成分登録）がある場合、最適量の求め方には<span class="text-marker">2通りの設定</span>があります。
        </div>
        <ol class="normal-content">
          <li><b>子商品を跨がない（子商品1つで最適解を求める）</b><br/>
             子商品を跨がない方法では、いずれか1種類の子商品だけを用いて有効成分の最適解を決定します。</li>
          <li><b>子商品を跨ぐ（組み合わせで最適解を求める）</b><br/>
              複数の子商品を組み合わせて、目標の有効成分量により近づけたい場合に用います。</li>
        </ol>
        <div class="q-mb-md">
          「子商品を跨がない」設定がデフォルトです。この設定は病院マスタ <a href="/SearchClinicList?tab=処方箋" target="_blank">病院・施設の「処方箋」</a>タブから指定できます。<br/>
          現状では個別の医薬品での設定はできず、全体設定のみとなります。
        </div>
        <div class="q-my-md">
          設定によって得られる最適解が変わる例として、以下を考えます。
        </div>
        <ul class="normal-content">
          <li>有効成分の投与量目標：0.7mg/kg</li>
          <li>犬の体重：10kg</li>
          <li>錠剤分割オプション：1錠、1/2錠 のみ許容</li>
          <li>子商品（アポキル）の含有量バリエーション：3.6mg／5.4mg／16mg の3種類</li>
        </ul>
        <div class="q-my-md">
          目標とする総有効成分量は<strong class="q-ml-md">0.7mg/kg × 10kg = 7.0mg (<span class="text-marker"> 目標は7.0mg </span>)</strong> <br/> 
          この目標値を、前述の2つの方法で計算すると得られる回答が異なる場合があります。
        </div>
        <ol class="normal-content">
          <li><b>子商品を跨がない場合の解</b><br/>
             いずれか1種類の子商品 錠剤（全錠または半錠）だけを用いて投与量を決定します。<br/>
              <ul class="normal-content">
                <li>アポキル 3.6を 2錠 ➡ 有効成分 7.2mg <span class="btn-label-light-grey q-mr-md"> 差 <span class="text-weight-bold text-darkred">+ 0.2mg 🏆</span> </span></li>
                <li>アポキル 3.6を 1 + 1/2錠 ➡ 有効成分 5.4mg <span class="btn-label-light-grey q-mr-md"> 差 - 1.6mg </span></li>
                <li>アポキル 5.4を 1錠 ➡ 有効成分 5.4 <span class="btn-label-light-grey q-mr-md"> 差 - 1.6mg </span></li>
                <li>アポキル 16を 1/2錠 ➡ 有効成分 8 <span class="btn-label-light-grey q-mr-md"> 差 + 1.2mg </span></li>
              </ul>
            この設定で得られる値： アポキル 3.6 = 2錠（有効成分量は7.2mg）</li>
          <li><b>子商品を跨ぐ場合の解</b><br/>
              複数の子商品の全錠と1/2錠を考慮し、目標 7.0mg により近い組み合わせを探します。<br/>
              <ul class="normal-content">
                <li>アポキル 3.6を 2錠 ➡ 有効成分 7.2mg <span class="btn-label-light-grey q-mr-md"> 差 <span class="text-weight-bold text-darkred">+ 0.2mg 🏆</span> </li>
                <li>アポキル 3.6を 1 + 1/2錠 ➡ 有効成分 5.4mg  <span class="btn-label-light-grey q-mr-md"> 差 - 1.6mg </span></li>
                <li>アポキル 3.6を 1/2錠, 5.4を1錠 ➡ 有効成分 7.2 <span class="btn-label-light-grey q-mr-md"> 差 <span class="text-weight-bold text-darkred">+ 0.2mg 🏆</span> </li>
                <li>アポキル 5.4を 1錠 ➡ 有効成分 5.4  <span class="btn-label-light-grey q-mr-md"> 差 - 1.6mg </span></li>
                <li>アポキル 5.4を 1 + 1/2錠 ➡ 有効成分 5.4  <span class="btn-label-light-grey q-mr-md"> 差 + 1.1mg </span></li>
                <li>アポキル 16を 1/2錠 ➡ 有効成分 8  <span class="btn-label-light-grey q-mr-md"> 差 + 1mg </span></li>
              </ul>
            この設定で得られる値： アポキル 3.6を 1/2錠, 5.4を1錠（有効成分量は7.2mg）</li>
        </ol>
      </div>
    `
  },
  itemServiceUnitViewPage2: {
    title: '製剤の重量を活用する',
    content: `
      <div class="q-px-md">注射薬で使用すると、<span class="text-marker"><b>量り売り単位を自動で換算</b></span>できます。この機能の活用で入力ミスや計算誤差を防ぐことができます。</div>
      <ul class="normal-content">
        <li>
        例えば、セレニア注（20mL）を「<span class="text-marker"><b>1mLあたり4,000円</b></span> (量り売り) 」で販売する方法を考えます。仮に、以下を条件とします。
          <ul class="q-mb-sm">
            <li class="q-mt-sm">犬の体重： 4.5kg</li>
            <li class="q-mt-sm">投与量： 1kgあたり0.1mL</li>
            <li class="q-mt-sm">売上量としてカウントするユニット数は1mL</li>
          </ul>
        </li>
        <li>目標投与量は 0.1mL/kg x 4.5kg = <b>0.45mL</b> </li>
        <li>計算により、「0.45mL」を投与するのであれば、売上単位としては 0.45mL ÷ 1mL = 「<b>0.45 ユニット</b>」となります。</li>
        <li>販売金額は 0.45ユニット x 4,000円 = 1,800円となります。</li>
      </ul>
      <div class="text-center"><img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_3_.png" width="50%" /></div>
      <div class="text-center q-my-md"><img src="http://www.vetty-helper.motocle2.com/master-medicine/helper_medicine_6.jpg" width="80%" /></div>
      <div class="q-pa-md"><span class="text-marker"><b>投与量を入力</b></span>するだけで「<span class="text-marker"><b>何ユニット分を販売するか</b></span>」が自動的に計算され、売上数量が即時に反映されます。</div>

    `
  },
}

export const filterCustomerPetDmHelperContents = {
  customerPetViewPage: {
    title: 'DMリスト',
    content: `
      <ul class="normal-content">
        <li>このページでは<span class="text-marker">「次回来院の予定日」</span>があるペット、<span class="text-marker">「予防系オーダー」</span>を実施したペットを検索し、検索結果よりCSVの抽出が可能です。</li>
        <li>詳細条件を設定すると、詳細の絞り込みができます。</li>
        <li>予防に関わらず、すべてのペット、動物種、年齢、最終来院日等で絞り込みを行う場合には、メニュー 31 「<a href="/SearchCustomerList">オーナー・ペット検索</a>」のCSV出力を利用してください。</li>
      </ul>
    `
  }
}

export const statusBoardHelperContents = {
  statusBoardViewPage: {
    title: 'ステータスボード ヘルパー',
    content: `
      <ul class="normal-content">
        <li>院内ステータスを<span class="text-marker">左から右</span>へ管理する<strong>ステ－タスボード(SB)</strong>です（自動更新）。</li>
        <li><span class="text-marker"><strong>表示期間</strong></span> RQ期間（<span class="text-marker">開始日～終了日</span>）内のステ－タスを表示している為、この期間の変更でステ－タス表示日を変更できます。</li>
        <li><span class="text-marker"><strong>絞り込み</strong></span> 日付、担当者、動物種、オーナーで絞り込みができます。</li>
        <li><span class="text-marker"><strong>メモ</strong></span> 当日の重要事項の記録にステ－タスメモを利用できます。例えば、「いただきもの」や「クーポンを渡す」など、<span class="text-marker">当日の大事なやり取りを記載</span>しておくことで<span class="text-marker">会計の対応漏れを防ぐ</span>ことができます。</li>
        <li><span class="text-marker"><strong>ステ－タスのみ追加</strong></span> RQ作成をしないでもステ－タスのみ作成できます。オーナーが<span class="text-marker">フードのピックアップのみで来院</span>される場合などに便利です。</li>
        <li><span class="text-marker"><strong>複製機能</strong></span> ペット1頭につき、複数のステ－タスを作成できます。<span class="text-marker">診療科や検査単位でステ－タス管理</span>ができます。<br/>受付画面で複数の先生を指名して受付を作成すると、先生単位でステ－タスが自動作成されます。</li>
        <li><span class="text-marker"><strong>ステ－タスの自動移動</strong></span> 以下の操作に対して<span class="text-marker">自動でステ－タスを移動</span>させることができます。病院マスタ（メニュー: M4-1）から設定してください。<br/>
          <p class="caption1 regular" style="line-height:24px">
            ① RQ作成時： RQを新規で作成時にどこにステ－タスを表示させるか<br/>
            ② 受付/「受付済」： 受付画面で、受付ステ－タスが「整理券」から「受付済」に変わったタイミングで移動<br/>
            ③ 受付/「呼出済」： 受付画面で、受付ステ－タスが「呼出済」に変わったタイミングで移動<br/>
            ④ 処方依頼： 処方箋オーダーを登録し、処方箋画面で「調剤指示」のチェックボックスにチェックをつけたタイミングで移動<br/>
            ⑤ RQ終了時： RQを完了(オーダー追加が完了し、次は会計)した場合に、会計ステ－タスのどこに移動させるか<br/>
            ⑥ 外注結果受信時： 外注検査結果を受信した場合、RQとステ－タスを自動作成。ステ－タスをどこに表示させるか（現状はIDEXX外注検査のみ連携可）
          </p>
        </li>
        <li><span class="text-marker"><strong>RQページから操作</strong></span> RQページの<span class="text-marker">ペット名の下部</span>にステ－タスの表示があります。そのステ－タス名をタップするとRQからSBを開きます。この操作でステ－タス編集が簡単に行えます。</li>
      </ul>
    `
  }
}

export const queueTicketHelperContents = {
  queueTicketViewPage1: {
    title: '受付・整理券 ヘルパー',
    content: `
  <div class="q-px-md">
    <!-- 概要 -->
    <section class="q-ma-md q-mb-lg">
      <h4 class="q-my-none">概要 💡</h4>
      <ul class="normal-content q-mb-md">
        <li>「受付画面」は、<span class="text-marker text-weight-bold">オーナーが来院してから診察を開始するまで</span>の一連の対応を行うページです。</li>
        <li>当日の受付だけを管理するページです。</li>
        <li>連携機能は、自動受付（タブレット受付）、myVettyからのWeb整理券（順番予約制）、リクエストの自動生成、ステ－タスボード、待合室画面への呼び出し表示です。</li>
        <li>受付機能はmyVetty機能や自動受付機能と連携させるとより効率的です。</li>
        <li>診察開始後の病院全体のステ－タス管理には<a href="/SearchStatusBoardList">ステ－タスボード</a>をご利用ください。</li>
      </ul>
    </section>

    <!-- 画面の見方 -->
    <section class="q-ma-md q-mb-lg">
      <h4 class="q-mt-none">レイアウト 🖥️</h4>
      <div class="row q-col-gutter-md q-mx-md">
        <div class="col-6 text-center">
          <img src="http://www.vetty-helper.motocle2.com/checkin/helper_qt_1.png" width="90%" />
        </div>
        <div class="col-6">
          <ol class="normal-content q-mb-md">
            <li><b>受付メニュー</b><br/>
            受付データの絞り込み、スリム化表示、新規受付の登録などができます。
            </li>
            <li><b>来院目的</b><br/>
            受付中の来院目的の一覧を表示しています。来院目的の横に🕐アイコンが表示されている場合、呼出予定時刻を表示する設定になります。<a href="/SearchQueueTicketPurposeList">来院目的のマスタページ</a>から表示名、色、予定時刻表示、順序などを設定できます。
            <li><b>受付表</b><br/>
            オーナーの来院を効率よく管理します。オーナー1名あたり1受付（複数ペット許容）、またはペット1頭あたり1受付、など運用によって使い分けが可能です。
            <li><b>担当者</b><br/>
            診察等の担当者が指定されている場合、担当者の一覧を表示します。担当者名をクリックすると該当の受付を絞り込めます。
            </li>
          </ol>
        </div>
      </div>
    </section>

    <!-- フィールド説明 -->
    <section class="q-ma-md q-mb-lg">
      <h4 class="q-mt-none q-mb-sm">受付の管理ステ－タス 🔍</h4>
      <div>受付で利用するステ－タスは6つあります。</div>
      <div class="q-pa-md">
        <table class="helper-table-qt-1">
          <thead>
            <tr>
              <th class="th-status">受付ステータス</th>
              <th class="th-action">機能・アクション</th>
            </tr>
          </thead>
          <tbody>
            <tr class="row-ticket">
              <td class="td-status">
                <div class="status-title">整理券</div>
                <div class="status-sub">当日の整理券を発行した直後</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>受付を登録した直後のステータス</li>
                  <li>オーナーは myVetty Web 整理券からも発行可能</li>
                  <li>この時点では受付に対して<span class="text-marker">自動でリクエストを紐づけない</span></li>
                </ul>
              </td>
            </tr>
            <tr class="row-accepted">
              <td class="td-status">
                <div class="status-title">受付済</div>
                <div class="status-sub">待合室で待機中の状態</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>オーナーが来院した際に利用するステータス</li>
                  <li>「受付済」ステータスで設定に応じて<span class="text-marker">リクエストをオーナーまたはペットに紐づける</span>（病院設定次第）</li>
                  <li>QRコードチェックインで「整理券」→「受付済」に自動更新</li>
                  <li>整理券がないオーナーがチェックインした場合も「受付済」として作成</li>
                  <li>右端の拡声器アイコン📢をクリックすると、待合室画面に呼び出し画面を表示し、ステ－タスを「呼出済」に変更します</li>
                </ul>
              </td>
            </tr>
            <tr class="row-called">
              <td class="td-status">
                <div class="status-title">呼出済</div>
                <div class="status-sub">診察室への入室タイミング</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>診察室へオーナーを呼び出した際のステータス</li>
                  <li>ここまでステータスが移動できた場合、受付画面の目的は達成</li>
                  <li>このステ－タス以降はステ－タスボードで全体管理</li>
                </ul>
              </td>
            </tr>
            <tr class="row-absent">
              <td class="td-status">
                <div class="status-title">不在</div>
                <div class="status-sub">呼び出したが不在</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>呼び出したが不在だった場合に利用</li>
                  <li>自動不在を設定で可能にしている場合に活用： ステータスが「整理券」のまま、受付の処理番号が「1」になってから指定時刻（例: 15分など）が経過すると、自動で「不在」ステ－タスへ移動</li>
                  <li>不在から「受付済」などへ手動で戻すことが可能</li>
                </ul>
              </td>
            </tr>
            <tr class="row-cancel">
              <td class="td-status">
                <div class="status-title">キャンセル</div>
                <div class="status-sub">オーナーによるキャンセル</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>オーナーが myVetty から自らの整理券を「キャンセル」した</li>
                  <li>ステ－タスは「キャンセル」から別へ変更可</li>
                </ul>
              </td>
            </tr>
            <tr class="row-trash">
              <td class="td-status">
                <div class="status-title">ゴミ箱</div>
                <div class="status-sub">不要になった受付</div>
              </td>
              <td class="td-action">
                <ul class="action-list">
                  <li>不要になった当日の受付はゴミ箱へ</li>
                </ul>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- 処理番号の決まり方 -->
    <section class="q-ma-md q-mb-lg">
      <h4 class="q-my-none">処理番号の決まり方</h4>
      <div class="row q-col-gutter-md">
        <div class="col-6 text-center">
          <img src="http://www.vetty-helper.motocle2.com/checkin/helper_qt_2.png" width="100%" />
        </div>
        <div class="col-6">
          <ul class="normal-content q-mb-md">
            <li>
                <b>処理番号とは？</b><br/>
                受付番号がオーナー向けの番号であるのに対し、処理番号は院内で使用する番号です。  
                院内での対応順番を管理するために用い、受付ステータスが「整理券」「受付済」の場合のみ表示されます。
            </li>
            <li>
                <b>処理番号の決まり方</b><br/>
                受付データに登録された担当者（医師・トリマー）ごとに順番に付番されます。  
                新しく登録された受付が、その担当者にとっての最後の処理番号となります。  
                例えばA先生とB先生がいる場合、それぞれ別の連番が振られます。
            </li>
            <li>
                <b>処理番号の変更</b><br/>
                同じ担当者内で受付をドラッグ＆ドロップすると、処理番号を入れ替えられます。  
                入れ替え後は、該当する受付以降のすべての番号が再調整されます。
                任意の番号に変更はできません。
            </li>
            <li>
                <b>複数の来院目的・担当者・ペットがある場合</b><br/>
                複数の来院目的や担当者、ペットが同時に登録されている場合は、  
                最初に登録されたペットの担当者を基準に、順番に処理番号を付与します。
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 予定時刻 -->
    <section class="q-ma-md q-mb-lg">
      <h4>呼出予想時刻（myVetty利用時）</h4>
      <div class="row justify-center q-col-gutter-md">
        <div class="col-12 col-md-10 col-lg-8 text-center">
          <img src="http://www.vetty-helper.motocle2.com/checkin/helper_qt_3.png" style="display: block; margin: 0 auto; max-width: 100%;" />
        </div>
      </div>
      <div class="row q-col-gutter-md">
        <div class="col-12">
          <ul class="normal-content q-mb-md">
            <li>
                <b>呼出予想時刻</b><br/>
                myVettyを利用しているオーナーは、自分の呼出時刻の目安を確認できます。<br/>
                表示された時刻により、来院の計画をより明確に立てられます。<br/>
                呼出予定時刻はあくまで目安であることを強調しています。<br/>
                病院マスタから呼出予定時刻の使用・不使用を設定できます。
            </li>
            <li>
                <b>計算方法は？</b><br/>
                呼出予定時刻は、同一担当者に配番される処理番号の順番で算出しています。<br/>
                受付の想定対応時間（分）を登録することで、次回呼出予定時刻が自動計算されます。
            </li>
            <li>
                <b>来院目的別に設定</b><br/>
                病院マスタで来院目的ごとの想定対応時間（分）をデフォルト設定できます。<br/>
                これにより、診察や予防など目的に応じた対応時間を個別に管理可能です。<br/>
                呼出予定時刻を表示したくない来院目的は、「呼出予定時刻を使用する」のチェックを外すことで非表示にできます。
            </li>
          </ul>
        </div>
      </div>
    </section>
  </div>
    `
  }
}

export const reservationHelperContents1 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>時間指定予約</strong><br>
          時間指定で、スケジュールに組み込みます。最小単位は予約基本設定で設定した単位になります。
        </li>
        <li>
          <strong>時間帯予約</strong><br>
          指定した時間帯内で何枠予約を取るのか設定します。他商品との重複予約は考慮されませんので、余裕を持った数値を入力してください。
        </li>
        <li>
          <strong>仮予約</strong><br>
          時間指定の予約を仮予約の状態で受け付けられます。オーナー様と希望日時のすり合わせを行い、予約の確定を行います。
        </li>
        <li>
          <strong>時間指定なし</strong><br>
          ホテルなどの時間指定を行わない予約の場合に利用してください。
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents2 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>時間指定予約</strong><br>
          お迎え時間の算出の際に、作業合計時間に+αする時間を設定してください。
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents3 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>時間帯予約</strong><br>
          指定した時間帯内で何枠予約を取るのか設定します。他商品との重複予約は考慮されませんので、余裕を持った数値を入力してください。
          <br>
          予約時間を任意の単位で分割し、予約枠を設定します。
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents5 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>診療時間内の予約にする</strong><br>
          オンにすると、診療時間外の時間は予約を取ることはできません。
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents6 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>トークルームの利用</strong><br>
          オンにすると、予約後にオーナー様とメッセージのやり取りができるトークルームを開きます。
          <br>
          仮予約や事前に写真の共有などを有する場合に利用します。
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents7 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>予約不可の場合の表示</strong><br>
          システム上、予約が埋まっている状態の際に、myVettyでなんと表示するか短いテキストで設定します。
          <br><br>
          基本は、「×」または「TEL」を設定します
        </li>
      </ul>
    `
  }
}

export const reservationHelperContents8 = {
  reservationViewPage: {
    title: '予約ヘルパー',
    content: `
      <ul class="normal-content" style="list-style:none; padding:0; margin:0">
        <li>
          <strong>追加質問を利用</strong><br>
          オンにすると、予約画面に選択式の設問を設定することができます。
        </li>
      </ul>
    `
  }
}
export const labSetModalHelperContents = {
  title: '手入力検査 ヘルパー',
  content: `
    <ul class="normal-content">
      <li>院内ステータスを<span>左から右</span>ドラッグ&ドロップで並べ替えや不要項目の削除ができます。</li>
      <li><span>行をクリックすると対象の検査項目の基準値設定や説明文を追加できます。</li>
      <li><span>また、右上の＋ボタンから別の手入力検査項目を追加することもできます。</li>
    </ul>
  `
}
