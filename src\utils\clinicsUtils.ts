import useClinicStore from '@/stores/clinics'
import { ClinicType } from '@/types/types'
import { storeToRefs } from 'pinia'

export const getClinicShortNameLabel = (idClinic: number) => {
  const clinicStore = useClinicStore()
  const { getClinics } = storeToRefs(clinicStore)
  const foundClinic: ClinicType | undefined = getClinics.value?.find((clinic: ClinicType) => {
    return clinic?.id_clinic === idClinic
  })
  return getClinics.value?.length > 1 ? foundClinic?.name_clinic_short : ''
}
