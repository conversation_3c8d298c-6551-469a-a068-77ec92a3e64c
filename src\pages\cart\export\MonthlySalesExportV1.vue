<script lang="ts" setup>
import MtModalHeader from '@/components/MtModalHeader.vue'
import { ref, computed, onMounted, shallowRef, defineAsyncComponent } from 'vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import GetPdfMonthlySalesReportV1 from '@/pages/cart/GetPdfMonthlySalesReportV1.vue'
import useCartStore from '@/stores/carts'
import useClinicStore from '@/stores/clinics'
import { dateFormat, getDateByFormat, getDateToday, changeMonth, getMonthStartAndEnd } from '@/utils/aahUtils'
import MtInputForm from '@/components/form/MtInputForm.vue'
import { formattedPrice } from '@/utils/helper'
import mtUtils from '@/utils/mtUtils'
import * as Encoding from 'encoding-japanese';

const DailySalesExportV1 = defineAsyncComponent(
  () => import('@/pages/cart/export/DailySalesExportV1.vue')
)

const cartStore = useCartStore()
const clinicStore = useClinicStore()

const emits = defineEmits(['close'])

const currentDate = ref(getDateToday())
const selectedMonthLabel = ref('今月')
const selectedMonth = ref(getDateByFormat(getDateToday(), 'YYYY/MM'))

const disableExport = ref(true)
const flgCompleted = ref(1);

const baseColumns = [
  { name: 'date_cart', label: '日付', field: 'date_cart', align: 'left' },
  { name: 'total_carts', label: '件数', field: 'total_carts', align: 'right' },
  {
    name: 'total_no_tax_amount',
    label: '診療金額（税抜）',
    field: 'total_no_tax_amount',
    align: 'right',
    tooltip: '1日分のすべての会計における診療金額（税抜）の合計です。'
  },
  {
    name: 'total_tax_amount',
    label: '消費税',
    field: 'total_tax_amount',
    align: 'right',
    tooltip: '1日分のすべての会計における消費税の合計金額です。'
  },
  {
    name: 'total_temp_cash_sales',
    label: '売上外',
    field: 'total_temp_cash_sales',
    align: 'right',
    tooltip: `1日分のすべての会計において、売上に計上されない金額の合計です 。<br />
              ※例：狂犬病の預かり金など、会計上売上に含まれない項目が該当します。`
  },
  {
    name: 'total_amount_insured',
    label: '保険負担額',
    field: 'total_amount_insured',
    align: 'right',
    tooltip: '1日分のすべての会計における保険負担額の合計です。'
  },
  {
    name: 'total_sales_amount',
    label: '診療金額（税込）',
    field: 'total_sales_amount',
    align: 'right',
    tooltip: `1日すべての会計の診療金額の合計です。<br />
              診療金額（税込）= 診療金額（税抜）+ 消費税 - 売上外`
  },
  {
    name: 'total_actual_sales',
    label: '売上',
    field: 'total_actual_sales',
    align: 'right',
    tooltip: `1日分の売上金額です。<br />
              売上 = 入金額 + 前受金の消し込金額 + 保険負担額 - 前受金の入金額 - 一般返金額（前受金の返金は含まれません）- 売上外額`
  },
  {
    name: 'total_valid_payment_received',
    label: '入金',
    field: 'total_valid_payment_received',
    align: 'right',
    tooltip: `1日すべての会計の入金額の合計です。<br />
              ※ この入金額は「前受金」の入金も含まれています。`
  },
  {
    name: 'total_temp_cash_payment',
    label: '売上外入金',
    field: 'total_temp_cash_payment',
    align: 'right',
    tooltip: `1日分の売上外の分の入金額の合計です。`
  },
  {
    name: 'total_cash_payment_received',
    label: '入金内訳-現金',
    field: 'total_cash_payment_received',
    align: 'right',
    tooltip: `1日分のすべての会計における入金額のうち、現金による入金の合計金額です。<br />
              ※前受金として現金で入金された金額も含まれます。`
  },
  {
    name: 'total_creditcard_payment_received',
    label: '-クレジット',
    field: 'total_creditcard_payment_received',
    align: 'right',
    tooltip: `1日分のすべての会計における入金額のうち、クレジットカードによる入金の合計金額です。<br />
              ※前受金としてクレジットカードで入金された金額も含まれます。`
  },
  {
    name: 'total_other_payment_received',
    label: '-その他',
    field: 'total_other_payment_received',
    align: 'right',
    tooltip: `1日分のすべての会計における入金額のうち、現金およびクレジットカード以外の支払方法による入金の合計金額です。<br />
              ※前受金として入金された金額も含まれます。`
  },
  {
    name: 'total_upfront_received',
    label: '前受入金',
    field: 'total_upfront_received',
    align: 'right',
    tooltip: `1日分のすべての会計における、前受金として受け取った入金額の合計です。`
  },
  {
    name: 'total_upfront_used',
    label: '前受精算',
    field: 'total_upfront_used',
    align: 'right',
    tooltip: `1日分のすべての会計において精算された前受金の合計額です。`
  },
  {
    name: 'total_refund_payment',
    label: '返金合計',
    field: 'total_refund_payment',
    align: 'right',
    tooltip: `1日分のすべての返金処理における返金額の合計です。<br />
              ※この返金額には、前受金からの返金額も含まれます。`
  },
  {
    name: 'total_non_upfront_refund',
    label: '返金',
    field: 'total_non_upfront_refund',
    align: 'right',
    tooltip: `1日分のすべての返金の合計です。<br />
              ※この返金額には、前受金の返金額は含まれていません。`
  },
  {
    name: 'total_upfront_refund',
    label: '前受返金',
    field: 'total_upfront_refund',
    align: 'right',
    tooltip: `1日分のすべての会計において、一部精算済みの前受金の未使用分を返金した金額の合計です。<br />
              例：1,000円を前受金として受け取り、600円を精算した場合、残りの400円を返金 → この400円が前受返金の金額です。`
  },
  {
    name: 'total_account_receivable',
    label: '未収金',
    field: 'total_account_receivable',
    align: 'right',
    tooltip: `1日分のすべての会計における、未収金の合計金額です。<br />
              ※この未収金額は、請求額に対してまだ支払われていない金額（未回収分）を示しています。`
  },
  {
    name: 'total_paid_ar',
    label: '未収精算',
    field: 'total_paid_ar',
    align: 'right',
    tooltip: `1日分のすべての会計において、未収金に対して実際に支払われた回収額の合計です。`
  },
  {
    name: 'total_amount_loss',
    label: '損金',
    field: 'total_amount_loss',
    align: 'right',
    tooltip: `1日分のすべての会計において損金として処理された金額の合計です。`
  }
];

const columns = shallowRef([])
const rows = ref([]);
const sum = ref({
  total_carried_over_ar: 0,
  total_account_receivable: 0,
  total_paid_ar: 0,
  total_carried_over_upfront_received: 0,
})
const payment_method_names = ref([]);

const closeModal = () => {
  emits('close')
}

const openDailySummary = async (date) => {
  await mtUtils.popup(DailySalesExportV1, {
    params: {
      date_start: date,
      date_end: date
    }
  })
}

const generateColumns = (includeDynamic = false) => {
  // Initialize with static columns
  const columns = [...baseColumns];

  // Optionally add dynamic payment method columns
  if (includeDynamic) {
    Object.entries(payment_method_names.value).forEach(([methodId, methodName]) => {
      columns.push({
        name: `pm_${methodId}`,
        label: methodName,  // Use the name of the payment method for CSV header
        field: `pm_${methodId}`,
        align: 'right',
      });
    });
  }

  return columns;
};

const buildSummarySum = (summaryRows) => {
  const total = {}
  const excluded = ['date_cart']

  for (const row of summaryRows) {
    for (const key in row) {
      const value = row[key]
      if (typeof value === 'number' && !excluded.includes(key)) {
        total[key] = (total[key] || 0) + value
      }
    }
  }

  return total
}


const exportCSV = (includeDynamic = false) => {
  // const csvColumns = generateColumns(includeDynamic);
  const tableColumn = columns.value.map(col => col.label);
  const tableRows = rows.value.map(row => {
    return columns.value.map(col => {
      return row[col.field];
    });
  });

  // Prepare the header information with specific column placement
  const headerInfo = [
    ['月計', '', selectedMonth.value.substr(0, 7).replace('-', '/'), '', '', '', '', '', '', clinicStore.getClinic.name_clinic_display]
  ];

  const csvContent = [
    ...headerInfo.map(row => row.join(',')), // Add header information
    '',
    tableColumn.join(','), // Add the column headers as the next row
    ...tableRows.map(row => row.join(',')) // Add each row of data
  ].join('\n');

  // Convert CSV content to Shift-JIS encoding
  const shiftJISArray = Encoding.stringToCode(csvContent);
  const shiftJIS = Encoding.convert(shiftJISArray, {
    to: 'SJIS',
    from: 'UNICODE',
  });
  const uint8Array = new Uint8Array(shiftJIS);
  const blob = new Blob([uint8Array], { type: 'text/csv;charset=shift-jis;' });

  // Generate the file name using dateFormat and getDateToday
  const formattedDate = dateFormat(getDateToday(), 'YYYYMMDD');
  const formattedSelectedMonth = getDateByFormat(
    `${selectedMonth.value}/01`,
    'YYYY年MM月分'
  );
  const fileName = `領収月計_${formattedSelectedMonth}_${clinicStore.getClinic.name_clinic_display}(出力${formattedDate}).csv`;

  // Create a link to download the CSV file
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', fileName);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const copytoclipboard = () => {
  const tableColumn = columns.value.map(col => col.label);
  const tableRows = rows.value.map(row => {
    return columns.value.map(col => {
      return row[col.field];
    });
  });

  // Prepare the header information with specific column placement
  const headerInfo = [
    ['月計', '', selectedMonth.value.substr(0, 7).replace('-', '/'), '', '', '', '', '', '', clinicStore.getClinic.name_clinic_display]
  ];

  const clipboardData = [
    tableColumn.join('\t'), // Add the column headers as the next row
    ...tableRows.map(row => row.join('\t')) // Add each row of data
  ].join('\n');



  // Create a link to download the CSV file
  navigator.clipboard.writeText(clipboardData).then(
    () => {
      mtUtils.autoCloseAlert('コピーしました！')
    },
    (err) => {
      console.error('Error copying CSV content to clipboard', err)
    }
  )
};


const exportPDF = () => {
  mtUtils.pdfRender(GetPdfMonthlySalesReportV1, {
    resultList: [...rows.value],
    date_start: selectedMonth.value //.substr(0, 7).replace('-', '/')
  })
};

const onChangeDate = async (prefix: string) => {
  let selectedDate = `${selectedMonth.value}/01`
  selectedMonth.value = changeMonth(selectedDate, prefix)
  let newDate = `${selectedMonth.value}/01`
  if (
    getDateByFormat(currentDate.value, 'YYYY-MM') ==
    getDateByFormat(selectedMonth.value, 'YYYY-MM')
  ) {
    selectedMonthLabel.value = '今月'
  } else {
    selectedMonthLabel.value = getDateByFormat(
      newDate,
      'YYYY年MM月'
    )
  }
  await fetchSalesSummary(); // Re-fetch the data on change
}

const isNextCalendarMonthDisabled = computed(() => {
  const currentMonth = getDateByFormat(currentDate.value, 'YYYY-MM')
  let monthReq = `${selectedMonth.value}/01`
  const selectedMonthValue = getDateByFormat(monthReq, 'YYYY-MM')
  return selectedMonthValue >= currentMonth
})

const dateRange = computed(() => {
  return getMonthStartAndEnd(selectedMonth.value)
})

const fetchSalesSummary = async () => {
  // Create queryParams object to hold all request parameters
  const queryParams = {
    ...dateRange.value,
    flg_completed: flgCompleted.value // Pass the flg_completed value
  };

  await cartStore.fetchMonthlySalesSummaryV1(queryParams).then((res) => {
    rows.value = res.data.data.summary
    sum.value = buildSummarySum(rows.value)
    payment_method_names.value = res.data.data.payment_method_names;
    disableExport.value = false
  });
};

const handleFlgCompletedChange = async () => {
  await fetchSalesSummary(); // Re-fetch the data on change
};

onMounted(async () => {
  await fetchSalesSummary()
  columns.value = generateColumns(true);
})

</script>
<template>
  <q-form style="width: calc(100vw - 50px); overflow-x: hidden;">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar class="text-primary q-pa-none" style="flex-wrap: wrap;">
        <q-toolbar-title class="title2 bold text-grey-900">
          集計
        </q-toolbar-title>
        <!-- Radio buttons for flg_completed -->
        <div class="q-ml-md q-gutter-md row">
          <MtFormRadiobtn v-model="flgCompleted" label="全て" val="" @update:modelValue="handleFlgCompletedChange" />
          <MtFormRadiobtn v-model="flgCompleted" label="完了会計のみ" :val="1"
            @update:modelValue="handleFlgCompletedChange" />
          <MtFormRadiobtn v-model="flgCompleted" label="未完のみ" :val="0" @update:modelValue="handleFlgCompletedChange"
            class="q-mr-md" />
        </div>

        <!-- Calendar month selection and navigation -->
        <div class="q-ml-auto">
          <div class="flex justify-between">
            <q-btn
              @click="onChangeDate('prev')"
              padding="2px"
              flat
              unelevated
              icon="chevron_left"
              style="border: 1px solid #9e9e9e" />
            <MtInputForm
              outlined
              class="search-field q-mx-xs"
              type="text"
              v-model="selectedMonthLabel"
              iconAppend="calendar_month"
              readonly />
            <q-btn
              @click="onChangeDate('next')"
              :disabled="isNextCalendarMonthDisabled"
              padding="2px"
              flat
              unelevated
              icon="chevron_right"
              style="border: 1px solid #9e9e9e" />
          </div>
        </div>
      </q-toolbar>

    </MtModalHeader>
    <q-card-section ref="pdfContent" class="content responsive-view" style="overflow-x: scroll;">
      <div class="header-info">
        <div class="left">
          <span class="title">{{ '領収 / 月計' }}</span>
          <span class="title q-ml-md">{{ selectedMonth.replace('-', '/') + '月分' }}</span>
        </div>
        <div class="right">
          <span class="title">{{ clinicStore.getClinic.name_clinic_display }}</span>
        </div>
      </div>
      <hr class="bold-line">

      <MtTable2
        :columns="columns"
        :rows="rows"
        :rowsBg="true"
        flat
        style="max-height: 70vh"
        no-data-message="登録がありません。"
        no-result-message="該当のデータが見つかりませんでした">

        <template v-slot:thead="{ columns }">
          <th
            v-for="(col, index) in columns"
            :key="col.name"
            :class="['text-' + (col.align || 'left')]">
            <div style="display: inline-flex; align-items: center; white-space: nowrap;">
              <span>{{ col.label }}</span>

              <q-icon
                v-if="col.tooltip"
                name="info"
                size="14px"
                color="primary"
                class="q-ml-xs cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <div v-html="col.tooltip" style="padding: 15px; white-space: normal;" />
                </q-popup-proxy>
              </q-icon>
            </div>
          </th>
          <!-- <div style="display: inline-flex; align-items: center; white-space: nowrap;">
              <span>{{ col.label }}</span>
              <q-icon
                v-if="col.name === 'total_sales_amount'"
                name="info"
                size="14px"
                color="primary"
                class="q-ml-xs cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <div style="padding: 15px; white-space: normal;">
                    1日すべての会計の診療金額の合計です。<br />
                    診療金額（税込）= 診療金額（税抜）+ 消費税 - 売上外
                  </div>
                </q-popup-proxy>
              </q-icon>
              <q-icon
                v-else-if="col.name === 'total_valid_payments'"
                name="info"
                size="14px"
                color="primary"
                class="q-ml-xs cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <div style="padding: 15px; white-space: normal;">
                    1日すべての会計の入金額の合計です。<br />
                    ※ この入金額は「前受金」の入金も含まれています。
                  </div>
                </q-popup-proxy>
              </q-icon>
            </div>
          </th>
        -->
        </template>

        <template v-slot:row="{ row }">
          <td
            v-for="(col, index) in columns"
            :key="index">
            <div v-if="col.field == 'date_cart'" key="date_cart" @click="openDailySummary(row['date_cart'])"
              class="cursor-pointer" auto-width>
              <div class="body1 row regular ">
                <span>
                  {{
                  row['date_cart'] ? getDateByFormat(row['date_cart']) : null
                  }}
                </span>
              </div>
            </div>
            <div v-else auto-width>
              <div class="body1 regular text-right">
                {{ row[col.field] == 0 ? '' : (typeof row[col.field] === 'number' ? formattedPrice(row[col.field]) :
                row[col.field]) }}
              </div>
            </div>
          </td>
        </template>
      </MtTable2>

      <div class="bg-white q-pa-md" v-if="rows.length > 0">
        <!-- #7 未収金額 -->
        <div class="row q-mb-xs items-center">
          <div class="col-auto text-bold">
            累計未収金:
            <q-icon
              name="info"
              size="14px"
              color="primary"
              class="q-ml-xs cursor-pointer">
              <q-popup-proxy transition-show="scale" transition-hide="scale">
                <div style="padding: 15px; white-space: normal;">
                  選択した年月までに発生した未収金の累計額です。
                </div>
              </q-popup-proxy>
            </q-icon>
          </div>
          <div class="col-auto q-ml-sm">
            {{ formattedPrice(sum.total_account_receivable) }} 円
          </div>
        </div>

        <!-- #8 未収精算金額 -->
        <div class="row q-mb-xs items-center">
          <div class="col-auto text-bold">
            累計未収精算:
            <q-icon
              name="info"
              size="14px"
              color="primary"
              class="q-ml-xs cursor-pointer">
              <q-popup-proxy transition-show="scale" transition-hide="scale">
                <div style="padding: 15px; white-space: normal;">
                  選択した年月までの未収金の回収累計額です。
                </div>
              </q-popup-proxy>
            </q-icon>
          </div>
          <div class="col-auto q-ml-sm">
            {{ formattedPrice(sum.total_paid_ar) }} 円
          </div>
        </div>

        <!-- #9 未収繰越金額 -->
        <div class="row q-mb-xs items-center">
          <div class="col-auto text-bold">
            未収繰越:
            <q-icon
              name="info"
              size="14px"
              color="primary"
              class="q-ml-xs cursor-pointer">
              <q-popup-proxy transition-show="scale" transition-hide="scale">
                <div style="padding: 15px; white-space: normal;">
                  選択した年月までの未収金の累計から回収済み金額を引いた繰越残高です。
                </div>
              </q-popup-proxy>
            </q-icon>
          </div>
          <div class="col-auto q-ml-sm">
            {{ formattedPrice(sum.total_carried_over_ar) }} 円
          </div>
        </div>

        <!-- #10 前受金繰越金額 -->
        <div class="row q-mb-xs items-center">
          <div class="col-auto text-bold">
            前受繰越:
            <q-icon
              name="info"
              size="14px"
              color="primary"
              class="q-ml-xs cursor-pointer">
              <q-popup-proxy transition-show="scale" transition-hide="scale">
                <div style="padding: 15px; white-space: normal;">
                  選択した年月までの前受金の未使用・未返金分の繰越残高です。<br />
                  ※前受金の繰越金額 = 前受金総額 − 消し込まれた前受金 − 返金された前受金
                </div>
              </q-popup-proxy>
            </q-icon>
          </div>
          <div class="col-auto q-ml-sm">
            {{ formattedPrice(sum.total_carried_over_upfront_received) }} 円
          </div>
        </div>
      </div>
    </q-card-section>

    <q-card-section class="q-bt bg-white">
      <div class="text-center">
        <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn class="q-ml-md" color="primary" unelevated @click="exportCSV()">
          <q-icon name="description" class="q-mr-sm" />
          CSVダウンロード
        </q-btn>
        <q-btn class="q-ml-md" color="primary" unelevated @click="exportCSV(true)">
          <q-icon name="description" class="q-mr-sm" />
          売上/入金まとめ
        </q-btn>
        <q-btn class="q-ml-md" color="primary" :disable="disableExport" unelevated @click="exportPDF()">
          <q-icon name="picture_as_pdf" class="q-mr-sm" />
          PDFダウンロード
        </q-btn>
        <q-btn class="q-ml-md" outline :disable="disableExport" @click="copytoclipboard()">
          <q-icon name="content_copy" class="q-mr-sm" />
          コピー
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style scoped>
:deep(.q-table thead tr) {
  height: 36px;
  color: #000;
}

:deep(.q-table tbody td) {
  height: auto;
  color: #000
}

.print-content * {
  font-size: 12px !important;
}

@media screen and (max-width: 820px) {
  .responsive-view {
    height: calc(100vh - 200px)
  }
}

.custom-col {
  flex: 0 0 calc(100% / 9 / 2);
  max-width: calc(100% / 9 / 2);
  text-align: center;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.left {
  display: flex;
  gap: 16px;
}

.right {
  text-align: right;
  flex-grow: 1;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.bold-line {
  border: 2px solid #000;
  margin-top: 8px;
  margin-bottom: 16px;
}
</style>
