<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ScheduleDetailModal'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import useEmployeeStore from '@/stores/employees'
import useCustomerStore from '@/stores/customers'
import useBookingTransactionStore from '@/stores/booking-transaction'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
// @ts-ignore
import MtModalHeader from '@/components/MtModalHeader.vue'
// @ts-ignore
import MtInputForm from '@/components/form/MtInputForm.vue'

const props = defineProps<{
  selectedDate: string
  selectedTime?: string
  selectedEmployee?: string
  selectedClinic: number
  startTime?: string
  endTime?: string
  slotData?: any
  onRefresh?: () => void
  bookingItemData?: any
  itemServiceId?: number
}>()

const emit = defineEmits(['close', 'confirm', 'refresh'])

// Initialize stores
const employeeStore = useEmployeeStore()
const customerStore = useCustomerStore()
const bookingTransactionStore = useBookingTransactionStore()

// Get reactive data from stores
const { getEmployees } = storeToRefs(employeeStore)

// Local state
const loading = ref(false)
const selectedEmployeeId = ref(props.selectedEmployee || '')
const error = ref('')
const localSelectedTime = ref(props.selectedTime || '')

// For service detail creation
const customerId = ref('')
const petId = ref('')
const memo = ref('')
const loadingCustomers = ref(false)
const loadingPets = ref(false)

// Computed properties
const employeeOptions = computed(() => {
  // If we have booking item data, filter employees from bookable_employee_list
  if (props.bookingItemData?.bookable_employee_list?.length) {
    const employees = getEmployees.value || []
    
    return props.bookingItemData.bookable_employee_list
      .filter((item: any) => item?.booking_item_employee?.flg_booking_available_employee === 1)
      .map((item: any) => {
        const employeeId = item.booking_item_employee?.id_employee_book_id
        if (!employeeId) return null
        
        const employee = employees.find((emp: any) => emp.id_employee === employeeId)
        
        return {
          label: employee ? employee.name_display : `Employee ${employeeId}`,
          value: String(employeeId)
        }
      })
      .filter(Boolean) // Remove null values
  }
  
  // Fallback to showing all employees for the clinic if no booking item data
  return getEmployees.value.map((employee: any) => ({
    label: employee.name_display,
    value: String(employee.id_employee)
  }))
})

const selectedEmployeeName = computed(() => {
  if (!selectedEmployeeId.value) return ''
  const employee = getEmployees.value.find(
    (emp: any) => String(emp.id_employee) === selectedEmployeeId.value
  )
  return employee?.name_display || ''
})

// Customer options
const customerOptions = ref<{label: string, value: string}[]>([])

// Pet options based on selected customer
const petOptions = ref<{label: string, value: string}[]>([])

// Add a displayInfo computed property to show the scheduled information
const displayInfo = computed(() => {
  // Priority: props.selectedTime > startTime/endTime > localSelectedTime
  let timeDisplay = props.selectedTime
  
  if (!timeDisplay && props.startTime && props.endTime) {
    timeDisplay = `${props.startTime} - ${props.endTime}`
  }
  
  if (!timeDisplay) {
    timeDisplay = localSelectedTime.value
  }
  
  return {
    date: props.selectedDate,
    time: timeDisplay || '',
    employee: selectedEmployeeName.value,
    // Use data from new API V2 format
    currentBookings: props.slotData?.current_bookings || 0,
    availableCapacity: props.slotData?.available_capacity || 0,
    slotInfo: props.slotData?.slot_info || 
      (props.slotData?.current_bookings !== undefined && props.slotData?.available_capacity !== undefined ? 
        `(${props.slotData.current_bookings}/${props.slotData.current_bookings + props.slotData.available_capacity})` : undefined),
    availableStatus: props.slotData?.available_status || 'UNKNOWN'
  }
})

// Check if slot has available capacity
const hasAvailableCapacity = computed(() => {
  // Check if slot can be booked based on new API V2 format
  if (props.slotData?.can_book !== undefined) {
    return props.slotData.can_book === true
  }
  
  // Fallback check using available capacity
  if (props.slotData?.available_capacity !== undefined) {
    return props.slotData.available_capacity > 0
  }
  
  return false // Default to false if no capacity info available
})


// Methods
const closeModal = () => {
  emit('close')
}

// Load customer options
const loadCustomers = async () => {
  try {
    loadingCustomers.value = true
    await customerStore.fetchCustomers({ id_clinic: props.selectedClinic })
    
    // Format customer options
    const customers = customerStore.getCustomers
    customerOptions.value = customers.map((customer: any) => ({
      label: `${customer.name_customer_display || ''}`,
      value: customer.id_customer
    }))
  } catch (err: any) {
    error.value = err.message || 'Failed to load customers'
  } finally {
    loadingCustomers.value = false
  }
}

// Load pets based on selected customer
const loadPets = async () => {
  if (!customerId.value) {
    petOptions.value = []
    petId.value = ''
    return
  }

  try {
    loadingPets.value = true
    
    // Get customer with pets
    await customerStore.selectCustomer(customerId.value, true)
    const customerWithPets = customerStore.getCustomer
    
    if (customerWithPets && customerWithPets.pets) {
      petOptions.value = customerWithPets.pets.map((pet: any) => ({
        label: `${pet.code_pet || ''} - ${pet.name_pet || ''}`,
        value: pet.id_pet
      }))
    } else {
      petOptions.value = []
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load pets'
  } finally {
    loadingPets.value = false
  }
}


const handleSubmit = async () => {
  if (!selectedEmployeeId.value || !customerId.value || !petId.value) {
    error.value = '必須項目を入力してください。'
    return
  }


  // Check if slot has available capacity
  if (!hasAvailableCapacity.value) {
    error.value = 'この時間帯は満席です。他の時間を選択してください。'
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    // Get booking item ID from slot data or props
    const bookingItemId = props.slotData?.id_booking_item
    
    if (!bookingItemId) {
      throw new Error('予約アイテムIDが見つかりません。')
    }

    // Prepare datetime values in ISO 8601 format
    let startDateTime: string
    let endDateTime: string
    
    if (props.startTime && props.endTime) {
      // If specific start and end times are provided
      startDateTime = bookingTransactionStore.formatDateTimeForAPI(props.selectedDate, props.startTime)
      endDateTime = bookingTransactionStore.formatDateTimeForAPI(props.selectedDate, props.endTime)
    } else if (props.selectedTime) {
      // Parse the selected time range
      const timeRange = props.selectedTime.split(' - ')
      if (timeRange.length === 2) {
        startDateTime = bookingTransactionStore.formatDateTimeForAPI(props.selectedDate, timeRange[0])
        endDateTime = bookingTransactionStore.formatDateTimeForAPI(props.selectedDate, timeRange[1])
      } else {
        throw new Error('時間範囲が不正です。')
      }
    } else {
      throw new Error('開始時間と終了時間が指定されていません。')
    }

    // Prepare booking transaction request using new API structure
    const bookingTransactionRequest = {
      id_clinic: props.selectedClinic,
      id_customer: parseInt(customerId.value),
      id_pet: parseInt(petId.value),
      id_request: undefined, // Optional
      name_item_service: props.bookingItemData?.booking_item?.name_item_service,
      code_customer: undefined, // Optional
      booking_slots: [{
        id_booking_item: bookingItemId,
        id_employee_doctor: parseInt(selectedEmployeeId.value),
        id_employee_staff: undefined, // Optional
        datetime_service_start: startDateTime,
        datetime_service_end: endDateTime,
        quantity: 1,
        type_booking: 1, // Default to BLOCK_DURATION, could be made configurable
        memo_service: memo.value || ''
      }],
      event_type: 'CREATE',
      update_availability: true
    }
    
    console.log('Creating booking transaction:', bookingTransactionRequest)
    
    // Create booking transaction using new API
    const result = await bookingTransactionStore.createBookingTransaction(bookingTransactionRequest)
    
    if (result && result.success) {
      console.log('Booking created successfully:', result.data)
      mtUtils.autoCloseAlert('予約が正常に作成されました。')
      
      // Call onRefresh callback if provided
      if (props.onRefresh) {
        props.onRefresh()
      }
      
      // Emit refresh event to trigger schedule board refetch
      emit('refresh')
      
      closeModal()
    } else {
      throw new Error(result?.message || '予約の作成に失敗しました。')
    }
  } catch (err: any) {
    console.error('Booking creation error:', err)
    
    // Handle new API error format
    if (err.response?.data) {
      const apiError = err.response.data
      if (apiError.errors && typeof apiError.errors === 'object') {
        // Handle structured validation errors
        const errorMessages = Object.values(apiError.errors).flat().join(', ')
        error.value = `予約エラー: ${errorMessages}`
      } else if (apiError.message) {
        error.value = apiError.message
      } else {
        error.value = '予約の作成に失敗しました。'
      }
    } else {
      error.value = err.message || '予約の作成に失敗しました。'
    }
  } finally {
    loading.value = false
  }
}


// Watch for customer selection changes
watch(customerId, async (newValue: string) => {
  if (newValue) {
    await loadPets()
  } else {
    petOptions.value = []
    petId.value = ''
  }
})


// Watch for prop changes
watch(() => props.selectedTime, (newValue) => {
  if (newValue !== localSelectedTime.value) {
    localSelectedTime.value = newValue || ''
  }
})

// Initialize component
onMounted(async () => {
  if (props.selectedEmployee) {
    selectedEmployeeId.value = props.selectedEmployee
  }
  
  // If selectedTime was provided, set it as the local time
  if (props.selectedTime) {
    localSelectedTime.value = props.selectedTime
  }
  
  // Load employees for the clinic if not already loaded
  if (getEmployees.value.length === 0) {
    await employeeStore.fetchEmployees({ id_clinic: props.selectedClinic })
  }
  
  // Load customers on mount
  await loadCustomers()
})
</script>

<template>
  <q-form @submit.prevent="handleSubmit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">予約詳細</q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="q-mt-md q-px-xl content">
      <!-- Loading State -->
      <div v-if="loading" class="text-center q-pa-md">
        <q-spinner-dots color="primary" size="40" />
      </div>

      <!-- Error Message -->
      <q-banner v-if="error" class="bg-red-1 text-red q-mb-md" rounded>
        {{ error }}
      </q-banner>

      <div class="row q-col-gutter-md">
        <!-- Left Column -->
        <div class="col-6">
          <!-- Employee Selection or Display -->
          <div class="q-mb-md">
            <div class="body1 regular q-mb-sm">担当者</div>
            <template v-if="props.selectedEmployee">
              <div class="bg-grey-2 q-pa-md rounded-borders">{{ selectedEmployeeName }}</div>
            </template>
            <template v-else>
              <q-select
                v-model="selectedEmployeeId"
                :options="employeeOptions"
                label="担当者を選択"
                outlined
                dense
                emit-value
                map-options
                :loading="loading"
              />
            </template>
          </div>

          <!-- Date Display -->
          <div class="q-mb-md">
            <MtInputForm
              type="text"
              :model-value="props.selectedDate"
              label="予約日"
              readonly
              outlined
              dense
            />
          </div>

          <!-- Time Display -->
          <div class="q-mb-md">
            <MtInputForm
              type="text"
              :model-value="displayInfo.time"
              label="予約時間"
              readonly
              outlined
              dense
            />
          </div>

          <!-- Capacity Information -->
          <div v-if="displayInfo.slotInfo" class="q-mb-md">
            <div class="body1 regular q-mb-sm">予約状況</div>
            <div class="bg-grey-2 q-pa-md rounded-borders">
              <div class="text-body2">
                <span class="text-weight-medium">{{ displayInfo.slotInfo }}</span>
                <span class="text-grey-7 q-ml-sm">(予約済み/利用可能)</span>
              </div>
              <div v-if="!hasAvailableCapacity" class="text-negative q-mt-xs">
                <q-icon name="warning" class="q-mr-xs" />
                満席
              </div>
              <div v-else class="text-positive q-mt-xs">
                <q-icon name="check_circle" class="q-mr-xs" />
                予約可能
              </div>
            </div>
          </div>
          
        </div>

        <!-- Right Column -->
        <div class="col-6">
          <!-- Customer Selection -->
          <div class="q-mb-md">
            <div class="body1 regular q-mb-sm">顧客 <span class="text-red">*</span></div>
            <q-select
              v-model="customerId"
              :options="customerOptions"
              label="顧客を選択"
              outlined
              dense
              emit-value
              map-options
              :loading="loadingCustomers"
              :rules="[val => !!val || '顧客は必須です']"
              use-input
              fill-input
              hide-selected
              input-debounce="300"
            >
              <template v-slot:no-option>
                <q-item>
                  <q-item-section class="text-grey">
                    顧客が見つかりません
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>
          
          <!-- Pet Selection -->
          <div class="q-mb-md">
            <div class="body1 regular q-mb-sm">ペット <span class="text-red">*</span></div>
            <q-select
              v-model="petId"
              :options="petOptions"
              label="ペットを選択"
              outlined
              dense
              emit-value
              map-options
              :loading="loadingPets"
              :disable="!customerId || petOptions.length === 0"
              :rules="[val => !!val || 'ペットは必須です']"
            >
              <template v-slot:no-option>
                <q-item>
                  <q-item-section class="text-grey">
                    ペットが見つかりません
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>
        </div>
        
        <!-- Memo Field - Full Width -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">メモ</div>
          <q-input
            v-model="memo"
            label="メモ"
            outlined
            dense
            type="textarea"
            rows="3"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Footer -->
    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn 
          unelevated 
          color="primary" 
          class="q-ml-md" 
          type="submit"
          :loading="loading"
          :disable="!selectedEmployeeId || loading || !!error || !customerId || !petId"
        >
          <span>確認</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.modal-btn {
  padding: 16px 0;
}
</style> 