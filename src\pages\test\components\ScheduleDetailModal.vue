<script lang="ts">
import { defineComponent } from 'vue'
import aahMessages from '@/utils/aahMessages'

export default defineComponent({
  name: 'ScheduleDetailModal'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import useWorkScheduleStore from '@/stores/work-schedules'
import useEmployeeStore from '@/stores/employees'
import useBookingItemStore from '@/stores/booking-items'
import useServiceDetailStore from '@/stores/service-details'
import useCustomerStore from '@/stores/customers'
import usePetStore from '@/stores/pets'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
// @ts-ignore
import MtModalHeader from '@/components/MtModalHeader.vue'

const props = defineProps<{
  selectedDate: string
  selectedTime?: string
  selectedEmployee?: string
  selectedClinic: number
  selectedTypeWeekday?: number
  selectedBookingItem?: number
}>()

const emit = defineEmits(['close', 'confirm'])

// Initialize stores
const workScheduleStore = useWorkScheduleStore()
const employeeStore = useEmployeeStore()
const bookingItemStore = useBookingItemStore()
const serviceDetailStore = useServiceDetailStore()
const customerStore = useCustomerStore()
const petStore = usePetStore()

// Get reactive data from stores
const { getEmployees } = storeToRefs(employeeStore)
const { getCurrentBookingItem } = storeToRefs(bookingItemStore)
const { getCreateServiceDetailFromCalendarResult } = storeToRefs(serviceDetailStore)
const { getCustomerListOptions } = storeToRefs(customerStore)

// Local state
const loading = ref(false)
const selectedEmployeeId = ref(props.selectedEmployee || '')
const error = ref('')
const localSelectedTime = ref(props.selectedTime || '')

// For service detail creation
const customerId = ref('')
const petId = ref('')
const memo = ref('')
const loadingCustomers = ref(false)
const loadingPets = ref(false)

// Computed properties
const employeeOptions = computed(() => [
  { label: '未選択', value: '' }, // Unselected option
  ...getEmployees.value.map((employee: any) => ({
    label: employee.name_display,
    value: String(employee.id_employee)
  }))
])

const selectedEmployeeName = computed(() => {
  if (!selectedEmployeeId.value) return ''
  const employee = getEmployees.value.find(
    (emp: any) => String(emp.id_employee) === selectedEmployeeId.value
  )
  return employee?.name_display || ''
})

// Customer options
const customerOptions = ref<{label: string, value: string}[]>([])

// Pet options based on selected customer
const petOptions = ref<{label: string, value: string}[]>([])

// Available time slots based on work schedule
const availableTimeSlots = ref<string[]>([])

// Calculate datetime from date and time slot
const getDateTimePair = (date: string, timeSlot: string): { start: string, end: string } => {
  // Ensure output format: YYYY-MM-DD hh:mm:ss
  const [startTime, endTime] = timeSlot.split(' - ')
  // Remove any 'T' and use space
  const startDateTime = `${date} ${startTime}`
  const endDateTime = `${date} ${endTime}`
  
  return {
    start: startDateTime,
    end: endDateTime
  }
}

// Methods
const closeModal = () => {
  emit('close')
}

// Load customer options
const loadCustomers = async () => {
  try {
    loadingCustomers.value = true
    await customerStore.fetchCustomers({ id_clinic: props.selectedClinic })
    
    // Format customer options
    const customers = customerStore.getCustomers
    customerOptions.value = customers.map((customer: any) => ({
      label: `${customer.name_customer_display || ''}`,
      value: customer.id_customer
    }))
  } catch (err: any) {
    error.value = err.message || 'Failed to load customers'
  } finally {
    loadingCustomers.value = false
  }
}

// Load pets based on selected customer
const loadPets = async () => {
  if (!customerId.value) {
    petOptions.value = []
    petId.value = ''
    return
  }

  try {
    loadingPets.value = true
    
    // Get customer with pets
    await customerStore.selectCustomer(customerId.value, true)
    const customerWithPets = customerStore.getCustomer
    
    if (customerWithPets && customerWithPets.pets) {
      petOptions.value = customerWithPets.pets.map((pet: any) => ({
        label: `${pet.code_pet || ''} - ${pet.name_pet || ''}`,
        value: pet.id_pet
      }))
    } else {
      petOptions.value = []
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load pets'
  } finally {
    loadingPets.value = false
  }
}

const checkEmployeeAvailability = async () => {
  if (!selectedEmployeeId.value) return

  loading.value = true
  error.value = ''

  try {
    let params: any = {
      id_employee: Number(selectedEmployeeId.value),
      type_weekday: props.selectedTypeWeekday,
      id_booking_item: props.selectedBookingItem
    }
    
    if (props.selectedTypeWeekday === 99) {
      params.date_booking_special = props.selectedDate
    }
    
    // Fetch work schedules for the selected employee and date
    const response = await workScheduleStore.fetchWorkSchedules(
      props.selectedClinic,
      params
    )

    const schedules = workScheduleStore.getEmployeeWorkSchedules(Number(selectedEmployeeId.value))
    
    if (!schedules || schedules.length === 0) {
      error.value = '選択した日付に勤務予定がありません。'
      availableTimeSlots.value = []
      return
    }

    // Extract available time slots from work schedules
    availableTimeSlots.value = schedules.map(schedule => {
      const slots = []
      if (schedule.time_workschedule_start && schedule.time_workschedule_end) {
        slots.push(`${schedule.time_workschedule_start} - ${schedule.time_workschedule_end}`)
      }
      if (schedule.time_workschedule_start2 && schedule.time_workschedule_end2) {
        slots.push(`${schedule.time_workschedule_start2} - ${schedule.time_workschedule_end2}`)
      }
      if (schedule.time_workschedule_start3 && schedule.time_workschedule_end3) {
        slots.push(`${schedule.time_workschedule_start3} - ${schedule.time_workschedule_end3}`)
      }
      return slots
    }).flat()

  } catch (err: any) {
    error.value = err.message || 'スケジュールの確認に失敗しました。'
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!selectedEmployeeId.value || !localSelectedTime.value || !props.selectedBookingItem) {
    error.value = '必須項目を入力してください。'
    return
  }

  try {
    // Get the item service ID from the current booking item
    const itemServiceId = getCurrentBookingItem.value?.item_service?.id_item_service
    
    if (!itemServiceId) {
      throw new Error('商品サービスIDが見つかりません。')
    }

    // New service detail creation
    if (!customerId.value || !petId.value) {
      error.value = '顧客とペットの情報は必須です。'
      return
    }
    
    // Parse the selected time range
    const { start: startDateTime, end: endDateTime } = getDateTimePair(props.selectedDate, localSelectedTime.value)
    
    const serviceDetailData = {
      id_clinic: props.selectedClinic,
      id_item_service: itemServiceId,
      datetime_service_start: startDateTime,
      datetime_service_end: endDateTime,
      id_pet: petId.value,
      id_customer: customerId.value,
      id_employee_doctor: selectedEmployeeId.value,
      memo_service: memo.value || ''
    }
    
    await serviceDetailStore.createServiceDetailFromCalendar(serviceDetailData)

    mtUtils.autoCloseAlert(aahMessages.success)
    
    closeModal()
  } catch (err: any) {
    error.value = err.message || err.data || '予約の作成に失敗しました。'
  }
}

// Watch for employee selection changes
watch(selectedEmployeeId, async (newValue: string) => {
  if (newValue) {
    await checkEmployeeAvailability()
  } else {
    availableTimeSlots.value = []
    error.value = ''
  }
})

// Watch for customer selection changes
watch(customerId, async (newValue: string) => {
  if (newValue) {
    await loadPets()
  } else {
    petOptions.value = []
    petId.value = ''
  }
})

// Watch for prop changes
watch(() => props.selectedTime, (newValue) => {
  if (newValue !== localSelectedTime.value) {
    localSelectedTime.value = newValue || ''
  }
})

// Initialize component
onMounted(async () => {
  if (props.selectedEmployee) {
    selectedEmployeeId.value = props.selectedEmployee
    await checkEmployeeAvailability()
  }
  
  // Load customers on mount
  await loadCustomers()
})
</script>

<template>
  <q-form @submit.prevent="handleSubmit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">予約詳細</q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="q-mt-md q-px-xl content">
      <!-- Loading State -->
      <div v-if="loading" class="text-center q-pa-md">
        <q-spinner-dots color="primary" size="40" />
      </div>

      <!-- Error Message -->
      <q-banner v-if="error" class="bg-red-1 text-red q-mb-md" rounded>
        {{ error }}
      </q-banner>

      <div class="row q-col-gutter-md">
        <!-- Employee Selection or Display -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">担当者</div>
          <template v-if="props.selectedEmployee">
            <div class="bg-grey-2 q-pa-md rounded-borders">{{ selectedEmployeeName }}</div>
          </template>
          <template v-else>
            <q-select
              v-model="selectedEmployeeId"
              :options="employeeOptions"
              label="担当者を選択"
              outlined
              dense
              emit-value
              map-options
              :loading="loading"
              class="q-mb-md"
            />
          </template>
        </div>

        <!-- Date Display -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">予約日</div>
          <div class="bg-grey-2 q-pa-md rounded-borders">{{ props.selectedDate }}</div>
        </div>

        <!-- Time Slots -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">利用可能な時間帯</div>
          <div v-if="availableTimeSlots.length > 0" class="bg-grey-2 q-pa-md rounded-borders">
            <q-option-group
              v-model="localSelectedTime"
              :options="availableTimeSlots.map(slot => ({
                label: slot,
                value: slot
              }))"
              type="radio"
            />
          </div>
          <div v-else-if="!loading && !error" class="bg-grey-2 q-pa-md rounded-borders text-grey">
            時間帯を選択してください
          </div>
        </div>

        <!-- Customer Selection -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">顧客 <span class="text-red">*</span></div>
          <q-select
            v-model="customerId"
            :options="customerOptions"
            label="顧客を選択"
            outlined
            dense
            emit-value
            map-options
            :loading="loadingCustomers"
            :rules="[val => !!val || '顧客は必須です']"
            use-input
            fill-input
            hide-selected
            input-debounce="300"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  顧客が見つかりません
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        
        <!-- Pet Selection -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">ペット <span class="text-red">*</span></div>
          <q-select
            v-model="petId"
            :options="petOptions"
            label="ペットを選択"
            outlined
            dense
            emit-value
            map-options
            :loading="loadingPets"
            :disable="!customerId || petOptions.length === 0"
            :rules="[val => !!val || 'ペットは必須です']"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  ペットが見つかりません
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        
        <!-- Memo Field -->
        <div class="col-12 q-mb-md">
          <div class="body1 regular q-mb-sm">メモ</div>
          <q-input
            v-model="memo"
            label="メモ"
            outlined
            dense
            type="textarea"
            rows="3"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Footer -->
    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn 
          unelevated 
          color="primary" 
          class="q-ml-md" 
          type="submit"
          :disable="!selectedEmployeeId || !localSelectedTime || loading || !!error || !customerId || !petId"
        >
          <span>確認</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.modal-btn {
  padding: 16px 0;
}
</style> 