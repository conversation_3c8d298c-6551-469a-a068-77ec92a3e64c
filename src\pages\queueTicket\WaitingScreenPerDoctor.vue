<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import QtIntroAndWaitingScreen from '@/pages/queueTicket/QtIntroAndWaitingScreen.vue'
import useQueueTicketStore from '@/stores/queue_ticket'
import useEmployeeStore from '@/stores/employees'
import { storeToRefs } from 'pinia'
import { orderBy, groupBy } from 'lodash'

import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import {
  QueueTicketType,
  EmployeeType
} from '@/types/types'
import { useQueueTicketUtils } from './queueTicketUtils'

const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  REFRESH_INTERVAL,
  CLEAR_TICKET_CALLING_SECONDS,
  QUEUE_STATUS,
  clearTimeoutAndIntervals,
  qtToBeCalledPromise,
  timeoutId,
  intervalId,
  fetchRooms
} = useQueueTicketUtils()

let absentListIntervalId = null
let absentListTimeoutId = null

const DOCTORS_PER_SCREEN = window.innerWidth > 1024 ? 4 : 3
const ABSENT_LIST_TO_CALL_SECONDS = 2 * 60 * 1000
const ABSENT_LIST_TO_HIDE_SECONDS = 1 * 60 * 1000

const currentSlide = ref(0)
const flgAbsentListShowing = ref(false)

// Store setup
const queueTicketStore = useQueueTicketStore()
const employeeStore = useEmployeeStore()
const { getQueueTicketLists } = storeToRefs(queueTicketStore)
const { getAllEmployees } = storeToRefs(employeeStore)

// Computed properties
const activeTickets = computed(() => {
  return getQueueTicketLists.value.filter(
    (ticket) =>
      [
        QUEUE_STATUS.WAITING,
        QUEUE_STATUS.IN_PROGRESS
      ].includes(ticket.type_status_queue_ticket)
  )
})

const allDoctors = computed(() => {
  const doctorsMap = new Map()
  activeTickets.value.forEach((ticket: QueueTicketType) => {
    if(ticket.id_pet.length > 0) {
      const firstPetId = ticket.id_pet[0]
      if(ticket.queue_detail[firstPetId].type_doctor_list.length) {
        const firstDoctorId = ticket.queue_detail[firstPetId].type_doctor_list[0]
        if(!doctorsMap.has(firstDoctorId)) doctorsMap.set(firstDoctorId, { currentlyProcessingTicket: null, waitingTickets: new Set([])})
        const currentDoctor = doctorsMap.get(firstDoctorId)
        if(ticket.process_order === 1 && !currentDoctor.currentlyProcessingTicket) currentDoctor.currentlyProcessingTicket = ticket.number_queue_ticket
        else currentDoctor.waitingTickets.add(ticket.number_queue_ticket)
      }
    }
  })
  return doctorsMap
})

const totalSlides = computed(() => {
  return Math.ceil(allDoctors.value.size / DOCTORS_PER_SCREEN) || 1
})

const absentTickets = computed(() =>
  getQueueTicketLists.value.filter((ticket) => ticket.type_status_queue_ticket === QUEUE_STATUS.ABSENT)
)

// Methods
const getDoctorName = (doctorId: EmployeeType) => {
  return getAllEmployees.value.find((emp: EmployeeType) => emp.value === doctorId)?.nameDisplay
}

// Lifecycle hooks
const setupPolling = async () => {
  await fetchRooms()
  await refreshData()
  intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)

  absentListIntervalId = setInterval(() => {
    flgAbsentListShowing.value = true
    absentListTimeoutId = setTimeout(() => {
      flgAbsentListShowing.value = false
    }, ABSENT_LIST_TO_HIDE_SECONDS)
  }, ABSENT_LIST_TO_CALL_SECONDS)

}

const refreshData = async () => {
  currentSlide.value = (currentSlide.value + 1) % totalSlides.value
  await queueTicketStore.fetchQueueTicketList({ today: true })

  let qtCallingIndex = getQueueTicketLists.value.findIndex((queueTicket) => queueTicket.queue_detail?.flg_qt_calling)
  if(qtCallingIndex !== -1) {

    isQtToBeCalled.value = true
    await qtToBeCalledPromise()

    isQtCalling.value = true
    callingQt.value = getQueueTicketLists.value[qtCallingIndex]
    
    clearTimeoutAndIntervals()

    timeoutId.value = setTimeout(async () => {
      let payload = {
        id_queue_ticket: callingQt.value.id_queue_ticket,
        flg_qt_calling: false
      }
      await mtUtils.callApi(selectOptions.reqMethod.POST, 'queue_ticket_calling/', payload)
      await queueTicketStore.fetchQueueTicketList({ today: true })

      isQtCalling.value = false
      intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
    }, CLEAR_TICKET_CALLING_SECONDS)
  }
}

onMounted(setupPolling)

onUnmounted(() => {
  clearTimeoutAndIntervals()
  if(absentListIntervalId) clearInterval(absentListIntervalId)
  if(absentListTimeoutId) clearTimeout(absentListTimeoutId)
})
</script>

<template>
  <QtIntroAndWaitingScreen />
  <transition name="fade">
    <div class="waiting-screen-container" v-if="!isQtToBeCalled && !isQtCalling">
      <div class="info-text flex items-center justify-center text-center text-weight-bold">まもなくお呼びします</div>
      <div class="flex doctor-row q-px-md" :style="{'height': flgAbsentListShowing ? '65vh' : '70vh'}">
        <template v-for="(slide, index) in totalSlides" :key="index">
          <div
            v-if="currentSlide === index"
            v-for="(doctorId, idx) in Array.from(allDoctors.keys()).slice(index * DOCTORS_PER_SCREEN, (index * DOCTORS_PER_SCREEN) + DOCTORS_PER_SCREEN)"
            class="doctor flex-1"
            :style="{'max-width': `${Math.max(100 / DOCTORS_PER_SCREEN, 33.33)}%`}"
            :key="doctorId"
          >
            <div class="head flex items-center text-white q-pa-xs">
              <span
                class="bg-white q-py-xs q-px-md q-ml-sm index text-weight-medium"
                >{{ (index * DOCTORS_PER_SCREEN) + (idx + 1) }}</span
              >
              <div class="text-center text-weight-medium name flex-1 ellipsis">
                {{ getDoctorName(doctorId) }}
              </div>
            </div>
            <div class="body">
              <div class="qt-number text-weight-bold text-center processing-ticket">
                {{ allDoctors.get(doctorId).currentlyProcessingTicket || '-' }}
              </div>
              <template v-for="(qtNumber, idx) in Array.from(allDoctors.get(doctorId).waitingTickets).slice(0, 4)" :key="idx">
                <div class="qt-number text-weight-bold text-center" :class="idx === 0 ? 'first-ticket' : ''">
                  {{qtNumber}}
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <div class="absent-list q-mx-md" v-if="flgAbsentListShowing">
        <div class="flex justify-between">
          <div class="heading text-weight-bold">お呼び出し済み</div>
          <div class="instruction-text text-weight-bold">受付にお声掛けください。</div>
        </div>
        <div class="flex gap-4">
          <template v-for="ticket in absentTickets" :key="ticket.id_queue_ticket">
            <div class="qt-number text-weight-bold">{{ ticket.number_queue_ticket }}</div>
          </template>
        </div>
      </div>
      <div class="flex justify-center items-center bottom-text text-center text-white text-weight-bold" v-else>
        診療内容によって診察の順番は前後する場合がございます。予めご了承ください。
      </div>
    </div>
  </transition>
</template>
<style lang="scss" scoped>
.waiting-screen-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(180deg, #EBF5F6 0%, #D3E7EA 100%);
  .info-text {
    height: 12vh;
    font-size: clamp(4vh, 6vh, 8vh);
    color: #194677;
  }
  .doctor-row {
    height: 70vh;
    gap: 10px;
    .doctor {
      border: 1px solid #194677;
      border-radius: 5px;
      overflow: hidden;
      border-bottom: 8px solid #194677;
      height: 100%;
      .head {
        background: #194677;
        .index {
          font-size: clamp(2vh, 3vh, 3vh);
          border-radius: 5px;
          color: #000;
        }
        .name {
          font-size: clamp(5vh, 4vh, 6vh);
          line-height: 1;
        }
      }
      .body {
        height: 100%;
        background: #FAFAFA;
        .qt-number{
          line-height: 1;
          padding: 10px 0;
          font-size: clamp(5vh, 8vh, 8vh);
          border-bottom: 1px solid gray;
        }
        .processing-ticket {
          font-size: clamp(5vh, 12vh, 12vh);
          border-bottom: 1px solid gray;
        }
      }
    }
  }
  .bottom-text {
    height: 12vh;
    margin-top: 3vh;
    font-size: clamp(4vh, 2vw, 2vw);
    background: linear-gradient(180deg, #2F6299 0%, #194677 100%);
  }
  .absent-list {
     height: 17vh;
     margin-top: 3vh;
     background: $accent-100;
     border-radius: 12px;
     padding: 10px 20px;
     .heading {
        font-size: clamp(2vh, 3vh, 3vh);
     }
     .instruction-text {
        font-size: clamp(2vh, 2vh, 2vh);
     }
     .qt-number {
       line-height: 1;
       font-size: clamp(2vh, 6vh, 6vh);
     }
  }
}
</style>
