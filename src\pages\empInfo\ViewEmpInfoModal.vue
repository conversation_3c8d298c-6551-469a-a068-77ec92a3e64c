<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, computed } from 'vue'
import { storeToRefs } from 'pinia'

// Directly Imported Components (from the `components` folder)
import MtModalHeader from '@/components/MtModalHeader.vue'
import OptionModal from '@/components/OptionModal.vue'

// Lazy-loaded Modals and Other Components
const UpdateEmpInfoModal = defineAsyncComponent(() => import('./UpdateEmpInfoModal.vue'))
const ZoomImageModal = defineAsyncComponent(() => import('../message/ZoomImageModal.vue'))

// Store imports
import useEmpInfoStore from '@/stores/empInfo'
import useEmployeeStore from '@/stores/employees'

// Utilities
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import { aahUtilsGetEmployeeName, convertLinkInMemo, dateFormat } from '@/utils/aahUtils'

// Types and Enums
import { empInfoType, EmployeeType } from '@/types/types'
import { typeEmpInfo, typeDisplay, typeEmpInfoRead } from '@/utils/enum'

// Props and Emits
const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

const props = defineProps<{
  data: empInfoType,
  attr: Object
}>()

// Stores
const empInfoStore = useEmpInfoStore()
const employeeStore = useEmployeeStore()
const { getEmpInfo } = storeToRefs(empInfoStore)

// Reactive Data
const empInfoReadList = ref([])
const showConfirmationModal = ref(false)
const selectedEmployee = ref(null)

const empInfoData = ref({
  id_emp_info: null,
  title: null,
  type_emp_info: 1,
  type_display: 1,
  type_department: null,
  type_emp_info_read: null,
  id_employee_posted: null,
  datetime_posted: null,
  memo_emp_info: '',
  file_url: null
})

const assignPageData = (data: empInfoType) => {
  for(let key in empInfoData.value) {
    empInfoData.value[key] = data[key]
  }
  if(props.data && props.data.file_path1) {
    empInfoData.value.file_path1_name = getFileName(props.data.file_path1)
  }
  // if(props.data.read_list && props.data.read_list > 0) {
  //   empInfoReadList.value = groupBy(props.data.read_list, 'flg_read')
  // }
}

const getFileName = (filePath: string) => {
  const fileName = filePath.split('/')
  return fileName[fileName.length - 1]
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: '編集',
      name: 'edit',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    },
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
  let selectedOption = menuOptions.find((i) => i.isChanged == true)
  if (selectedOption) {
    if(selectedOption.name == 'edit') {
      mtUtils.popup(UpdateEmpInfoModal, {data: props.data})
    }
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm('本当に削除しますか？', '確認')
        .then((confirmation) => {
          if (confirmation) {
            empInfoStore.destroyEmpInfo(empInfoData.value.id_emp_info).then(() => {
              empInfoStore.fetchEmpInfos({type_display: 3})
              emits('close')
              mtUtils.autoCloseAlert(aahMessages.success)
            })
          }
        })
    }
  }
}

const getEmployeeName = (empId:number) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, String(empId))
}

const getEmployeeProfileImage = (empId:number) => {
  return employeeStore.getEmployees.find((emp) => emp.id_employee === empId)?.image_path1
}

const setRecordAsReaded = async () => {
  let data = {
    id_emp_info: empInfoData.value.id_emp_info,
    id_employee: JSON.parse(localStorage.getItem('id_employee')),
    flg_read: true
  }
  const res:any = await empInfoStore.updateEmpInfoRead(data)
  if(res) {
    props.attr.isConfirmed = true
    closeModal()
  }
}

// Handle employee click to show confirmation modal
const handleEmployeeClick = (employee: any) => {
  selectedEmployee.value = employee
  showConfirmationModal.value = true
}

// Handle confirmation modal close
const closeConfirmationModal = () => {
  showConfirmationModal.value = false
  selectedEmployee.value = null
}

// Handle employee confirmation (mark as read)
const confirmEmployeeRead = async () => {
  if (!selectedEmployee.value) return

  let data = {
    id_emp_info: empInfoData.value.id_emp_info,
    id_employee: selectedEmployee.value.id_employee,
    flg_read: true
  }

  try {
    const res = await empInfoStore.updateEmpInfoRead(data)
    if (res) {
      // Close confirmation modal
      closeConfirmationModal()

      // Refresh the emp info data to show latest status
      await empInfoStore.selectEmpInfo(empInfoData.value.id_emp_info)

      // Call setRecordAsReaded to update the main modal
      await setRecordAsReaded()
    }
  } catch (error) {
    console.error('Error updating employee read status:', error)
    mtUtils.autoCloseAlert('エラーが発生しました')
  }
}

const getEmployeeDetail = (employee: EmployeeType) => {
  return {
    name_display: aahUtilsGetEmployeeName(employeeStore.getAllEmployees, employee.id_employee.toString()),
    image_path1: employeeStore.getEmployees.find((i) => i.id_employee == employee.id_employee)?.image_path1
  }  
}

const employeesWithDisplayOrder = computed(() => {
  // 1. For efficiency, create a lookup map from the employee store.
  const employeeDetailsMap = new Map(
    employeeStore.getEmployees.map(emp => [emp.id_employee, emp])
  );

  // 2. Map over your primary list and then sort the result.
  return getEmpInfo.value.emp_info_read
    .map(employee => {
      // Find the corresponding full details from the map
      const details = employeeDetailsMap.get(employee.id_employee);

      // Return a new object with all original properties plus the new one.
      return {
        ...employee, // Copy all original properties from the employee object
        display_order: details ? details.display_order : null, // Add display_order, use null as a fallback
        name_display: details ? details.name_display : null,
        image_path1: details ? details.image_path1 : null
      };
    })
    .sort((a, b) => {
      // 3. Sort the array by `display_order`.
      // This handles cases where display_order might be null or undefined.
      if (a.display_order === null || a.display_order === undefined) return 1;
      if (b.display_order === null || b.display_order === undefined) return -1;
      return a.display_order - b.display_order;
    });
});

const openImageViewModal = async (file: File) => {
  await mtUtils.imageViewPopup(ZoomImageModal, {
    files: file,
    singleImage: true
  })
}

onMounted(() => {
  if(props.data) {
    assignPageData(props.data)
  }
})

</script>
<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
      {{ 
        typeEmpInfo.find((emp) => {
          return emp.value === empInfoData.type_emp_info
        })?.label
      }}      
    </q-toolbar-title>
    <q-btn flat round  @click="openMenu">
      <q-icon size="xs" name="more_horiz" />
    </q-btn>
  </MtModalHeader>
  <q-card-section class="q-px-lg content">
      <div class="q-pa-md">
        <div class="title2">
          {{ empInfoData.title }}
        </div>
        <div class="flex items-center q-mt-sm">
          <div class="flex items-center">
            <img v-if="getEmployeeProfileImage(empInfoData.id_employee_posted)" :src="getEmployeeProfileImage(empInfoData.id_employee_posted)" class="q-mr-sm emp-info-image" />
            <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
            <span class="text-grey-500">{{ getEmployeeName(empInfoData.id_employee_posted) }}</span>
          </div>
          <div class="">
            <span class="caption1 regular text-grey-500 q-mr-xs q-ml-md">連絡日:</span>
            <span class="text-grey-500">{{ dateFormat(empInfoData.datetime_posted, 'YYYY年MM月DD日  HH:mm') }}</span>
          </div>
        </div>
        <div class="row q-col-gutter-md q-mt-md">
          <div class="body1 col-10 regular q-pt-md" v-html="convertLinkInMemo(empInfoData.memo_emp_info)" />
          <div class="col-2 flex items-start">
            <template v-if="props.data.file_path1">
              <img 
                v-if="props.data.file_path1.endsWith('.png') || props.data.file_path1.endsWith('.jpg')"
                :src="props.data.file_url"
                style="max-width: 100%; cursor: pointer; object-fit: contain;"
                @click="openImageViewModal(props.data.file_url)"
              />
              <template v-else-if="props.data.file_path1.endsWith('.pdf') || props.data.file_path1.endsWith('.pdf')">
                <q-icon
                  name="receipt_long"
                  size="20px"
                  color="red-10"
                  class="q-mr-sm"
                />
                {{empInfoData.file_path1_name}}
              </template>
            </template>
          </div>
        </div>
      </div>
      <q-separator class="q-my-md q-px-lg" />
      <div class="row q-px-md q-mt-lg" v-if="getEmpInfo.emp_info_read.length">
      <div class="row q-col-gutter-md q-mb-md" v-if="getEmpInfo?.emp_info_read?.filter((data) => { return data.flg_read })?.length">
          <div class="col" >
            <q-chip dense class="emp-chip-tosca" text-color="black">
              <div class="flex-1 justify-center text-center">確認済</div>
            </q-chip>
            <div class="flex items-center q-mt-xs">
              <span
                class="q-mb-xs flex items-center cursor-pointer employee-item"
                v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return data.flg_read })"
                :key="idx"
                @click="handleEmployeeClick(item)"
              >
                <img class="q-mr-xs emp-info-image" v-if="item.image_path1" :src="item.image_path1" />
                <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
                <span class="q-mr-md">{{item.name_display}}</span>
              </span>
            </div>
          </div>
        </div>
        <div class="row q-col-gutter-md q-mb-md" v-if="getEmpInfo?.emp_info_read?.filter((data) => { return !data.flg_read })?.length">
          <div class="col">
            <q-chip dense class="bg-accent-500 emp-chip" text-color="black">
              <div class="flex-1 justify-center text-center">未読</div>
            </q-chip>
            <div class="flex items-center q-mt-xs">
              <span
                class="flex items-center q-mb-xs cursor-pointer employee-item"
                v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return !data.flg_read })"
                :key="idx"
                @click="handleEmployeeClick(item)"
              >
                <img class="q-mr-xs emp-info-image" v-if="item.image_path1" :src="item.image_path1" />
                <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
                <span class="q-mr-md ">{{item.name_display}}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
     <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn color="primary" class="text-white text-grey-800" @click="setRecordAsReaded()">
          <q-icon name="check_circle_outline" class="q-mr-sm"/>
          <span>確認しました</span>
        </q-btn>
      </div>
    </q-card-section>

    <!-- Employee Confirmation Modal -->
    <q-dialog v-model="showConfirmationModal" persistent>
      <q-card class="confirmation-modal">
        <q-card-section class="q-pa-lg">
          <div class="text-center">
            <div class="text-h6 text-grey-700 q-mb-md">
              Emp Info Detail confirmation modal
            </div>

            <div class="flex items-center justify-center q-mb-lg">
              <div class="employee-avatar q-mr-sm">
                <span class="avatar-number">1</span>
              </div>
              <span class="text-h5 text-grey-900">
                {{ selectedEmployee?.name_display }}さん
              </span>
            </div>

            <div class="text-h6 text-grey-900 q-mb-xl">
              院内連絡確認しましたか？
            </div>

            <div class="row q-gutter-md justify-center">
              <q-btn
                outline
                color="grey-7"
                size="lg"
                class="confirmation-btn-close"
                @click="closeConfirmationModal"
              >
                <span class="close-number">3</span>
                閉じる
              </q-btn>

              <q-btn
                color="grey-9"
                size="lg"
                class="confirmation-btn-confirm"
                @click="confirmEmployeeRead"
              >
                <q-icon name="check" class="q-mr-sm" color="white" />
                <span class="confirm-number">2</span>
                確認しました
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
</template>

<style lang="scss" scoped>
.emp-chip {
  margin-left: -2px;
  width: 76px;
}
.emp-chip-tosca {
  background-color: $tosca;
  margin-left: -2px;
  width: 76px;
}

/* Employee item hover effect */
.employee-item {
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
  }
}

/* Confirmation Modal Styles */
.confirmation-modal {
  min-width: 400px;
  max-width: 500px;
}

.employee-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-number {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.confirmation-btn-close {
  position: relative;
  min-width: 120px;
  padding: 12px 24px;
}

.confirmation-btn-confirm {
  position: relative;
  min-width: 160px;
  padding: 12px 24px;
}

.close-number {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.confirm-number {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
</style>