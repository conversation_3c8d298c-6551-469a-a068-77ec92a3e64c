<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import useTextTemplateStore from '@/stores/text-template'
import { storeToRefs } from 'pinia'
import { event_bus } from '@/utils/eventBus'
import mtUtils from '@/utils/mtUtils'
import FabricMemoCarteModal from './FabricMemoCarteModal.vue'
import { CustomerType, PetType } from '@/types/types'
import useIllnessHistoryStore from '@/stores/illness-history'
import { typeSchemaImageCategory } from '@/utils/enum'
import UpdateShemeImageTemplateModal from '@/pages/master/textTemplate/UpdateShemeImageTemplateModal.vue'
import { LocalStorage } from 'quasar'
import MtCarousel from '@/components/MtCarousel.vue'
import { sortBy } from 'lodash'


const illnessHistoryStore = useIllnessHistoryStore()

const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}
const props = withDefaults(
  defineProps<{
    isDirectSubmit: boolean
    customerSelected: CustomerType
    petSelected?: PetType
  }>(),
  {
    isDirectSubmit: false,
    customerSelected: () => {
      return {} as CustomerType
    },
    petSelected: () => {
      return {} as PetType
    }
  }
)

const templateStore = useTextTemplateStore()
const { getTemplates } = storeToRefs(templateStore)

const useTemplate = async (template) => {
  
  if(template) {
    templateStore.selectTemplate(template.id_text_template) 
  }

  if (props.isDirectSubmit) {
    mtUtils.fullHeightPopup(FabricMemoCarteModal, {
      id_memo_carte: null,
      id_customer: props.customerSelected?.id_customer,
      id_pet: props.petSelected?.id_pet,
      isDirectSubmit: props.isDirectSubmit,
      id_pet_illness_history: [illnessHistoryStore.getIllnessHistory?.id_pet_illness_history],
      imageUrl: template && template?.img_file_path_template ? template.img_file_path_template : ''
    }).then(() => {
      
      if(!template) {
        return
      }
      let temp = []
      if(LocalStorage.has('recently_used_templates')) {
        temp = LocalStorage.getItem('recently_used_templates')
      }
      if(temp.includes(template.id_text_template)) {
        return
      }
      temp.push(template.id_text_template)
      LocalStorage.set('recently_used_templates', temp)
      recentlyUsed.value.length = 0
      recentlyUsed.value.push(...temp)
    })
  }
  closeModal()
}

const onFileChange = async (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = target.files

  if (!files?.length) return

  if (files.length) {
    const fileList: File[] = Array.from(files)
    let imageUrl: string | ArrayBuffer

    fileList.forEach((file) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        imageUrl = event.target?.result;

        if (!props.isDirectSubmit) {
          event_bus.emit('addImageUrl', imageUrl)
        }

        if (props.isDirectSubmit) {
          mtUtils.fullHeightPopup(FabricMemoCarteModal, {
            id_memo_carte: null,
            id_customer: props.customerSelected?.id_customer,
            id_pet: props.petSelected?.id_pet,
            isDirectSubmit: props.isDirectSubmit,
            id_pet_illness_history: [illnessHistoryStore.getIllnessHistory?.id_pet_illness_history],
            imageUrl
          })
        }
      };

      reader.readAsDataURL(file);
    })
  }
  closeModal()
  return
}

const recentlyUsed = ref([])
const search = () => {
  templateStore.fetchTemplates({   type_text_template: 100 })
}

onMounted(() => {
  
  search()
  let temp = []
  if(LocalStorage.has('recently_used_templates')) {
    temp = LocalStorage.getItem('recently_used_templates')
  }
  recentlyUsed.value.push(...temp)
})
const typeEtc2Options = ref([...typeSchemaImageCategory])
const selectedTypeEtc2 = ref(null)
const getGroupedTitle = (item) =>{
  return typeSchemaImageCategory.find(({value}) => {
    return value == item
  })?.label
}
const getGroupedSubTitle = (item) =>{
  return typeSchemaImageCategory.find(({value}) => {
    return value == item
  })?.subLabel
}
const getGroupedTemplate = computed(() => {
  const obj = {}
  getTemplates.value
    .forEach(item => {
      let typeEtc2 = item.type_etc2
      if (!typeEtc2) {
        obj[190] = obj[190] ? [...obj[190], item] : [item]
        return
      }
      if(!obj[typeEtc2]) {
        obj[typeEtc2] = []
      }
      typeEtc2.split(',')
        .forEach((typeItem) => {
          typeItem = typeItem.trim()
          let category = typeSchemaImageCategory.find(({value}) => {
            return value == typeItem
          })
          if(category) {
            obj[category.value] = obj[category.value] ? [...obj[category.value], item] : [item]
          }
        })
    })

  Object.keys((obj), (key) => {
    obj[key] = sortBy(obj[key], 'display_order', 'asc')
  })
  
  return obj
})




const getRecentlyUsed = computed(() => {
  return recentlyUsed.value?.map(item => {
     return getTemplates.value.find(template => template.id_text_template == item)
  })?.filter(item => Boolean(item)) || []
})

const scrollContainer = ref()
const moveToSelectedGroup = (item) => {
  selectedTypeEtc2.value = item.value
  let elements = []
  if(window.innerWidth < 1024) {
    elements = document.getElementsByClassName('mobile-group-'+ item.value) || []
  }else {
    elements = document.getElementsByClassName('group-'+ item.value) || []
  }
  Array.from(elements).forEach((element) => {
    const top = element.offsetTop
    scrollContainer.value.setScrollPosition('vertical', top - 50, 400)
  })
}

const scrollUpdate = (value) => {
  const item = typeEtc2Options.value.find((item) => item.value === value)
  if(!item || item.value === selectedTypeEtc2.value) {
    return
  }
  moveToSelectedGroup(item)
}

const getAvailableGroups = computed(() => {
  
  const selectOptions = [];
  getTemplates.value
    .forEach((item) => {
      let origOption = null
      if(!item.type_etc2) {
        origOption = typeSchemaImageCategory.find((t) => t.value === 190)
      } else {
        origOption = typeSchemaImageCategory.find((t) => t.value == item.type_etc2)
      }
      
      if (origOption) {
        const existingOption = selectOptions.find((option) => option.value === origOption.value);
        if (!existingOption) {
          selectOptions.push(origOption);
        }
      }
    })
  selectOptions.sort((a, b) => a.value - b.value)
  return selectOptions
})
</script>

<template>
        <MtModalHeader @closeModal="closeModal">
          <q-toolbar-title class="text-grey-900 title3 bold">
            シェーマ図
          </q-toolbar-title>
        </MtModalHeader>
          <div class="q-pa-md">
            <q-card class="bg-white" style="z-index: 100;">
              <div class="collapsable-container q-py-lg q-pr-lg collapse" style="position: sticky; top: 0px; z-index: 10;">
                <div class="carousel q-px-sm" v-if="getAvailableGroups.length > 0">
                  <MtCarousel :options="getAvailableGroups" @update:model-value="scrollUpdate" v-model="selectedTypeEtc2"  />
                </div>
              </div>
            </q-card>
          </div>
         <q-scroll-area ref="scrollContainer" class="content q-px-md q-pt-none">
           <input
             type="file"
             style="display: none"
             ref="fileInput"
             accept="image/*"
             multiple
             @change="onFileChange($event)"
           />
           <div class=" q-pa-sm q-mt-md"  style="gap: 16px; width: 100%; display: flex; flex-direction: column;">
             <div class="row">
               <div class="col-3 flex items-center justify-start">
                 <span class="text-h5">新規追加</span>
               </div>
               <div class="col-9">
                 <div class="flex justify-start items-center" style="gap: 16px">
                   <q-card v-ripple class="flex justify-center items-center cursor-pointer" style="width: 260px; height: 260px"  @click="$refs.fileInput.click()">
                     <q-card-section class="flex items-center justify-center">
                       <q-icon name="add" size="50px"></q-icon>
                     </q-card-section>
                   </q-card>
                   <q-card v-ripple class="flex justify-center items-center cursor-pointer" style="width: 260px; height: 260px"  @click="useTemplate(null)">
                     <q-card-section class="flex items-center justify-center">
                       <span class="text-h4">空白のスキーマ</span>
                     </q-card-section>
                   </q-card>
                 </div>
               </div>
             </div>
             <div class="row q-gutter-none" v-if="getRecentlyUsed.length > 0">
               <div class="col-3 flex items-center justify-start text-h5">
                 最近使用した
               </div>
               <div class="col-9">
                 <div class="flex justify-start items-center" style="gap: 16px">
                   <q-card v-for="(item, index) in getRecentlyUsed" :key="index" class="grid-item flex align-center justify-center" @click="useTemplate(item)">
                     <q-img
                       loading="lazy"
                       class=" rounded-borders"
                       fit="cover"
                       width="260px"
                       height="260px"
                       img-class="fetch-list-img"
                       aspect-ratio="1"
                       :src="item.img_file_path_template"  alt="Item Image" />

                   </q-card>
                 </div>
               </div>
             </div>
           </div>
           <div class="row-wrapper q-mt-sm desktop-layout q-mb-md" style="flex-grow: unset;" :class="`group-${index}`" v-for="(groupedItems, index) in getGroupedTemplate" :key="index">
             <div class="col-3 section-link">
               <div class="text-h5">{{getGroupedTitle(index)}}</div>
               <div class="text-caption">{{getGroupedSubTitle(index)}}</div>
             </div>
             <div class="col-9">
               <div class=" justify-start items-center" style="gap: 16px; display: flex;flex-direction: row; flex-wrap: wrap ">
                 <q-card v-for="item in groupedItems" class="cursor-pointer" :key="item" style="width: fit-content; height: fit-content" @click="useTemplate(item)">
                   <q-img
                     loading="lazy"
                     class=" rounded-borders"
                     fit="cover"
                     width="260px"
                     height="260px"
                     img-class="fetch-list-img"
                     aspect-ratio="1"
                     :src="item.img_file_path_template"  alt="Item Image" >

                   </q-img>
                 </q-card>
               </div>
             </div>
           </div>
           <!--   mobile   -->
           <div class="q-mt-sm mobile-layout q-mb-md" :class="`mobile-group-${index}`" v-for="(groupedItems, index) in getGroupedTemplate" :key="index">
             <div class="section-link q-mb-sm" >
               <div class="text-h5">{{getGroupedTitle(index)}}</div>
               <div class="text-caption">{{getGroupedSubTitle(index)}}</div>
             </div>
             <div  class="flex justify-center items-center" style="gap: 16px">
               <q-card v-for="(item, index) in groupedItems" class="cursor-pointer" :key="index" style="width: fit-content; height: fit-content" @click="onRowClick(item)">
                 <q-img
                   loading="lazy"
                   class=" rounded-borders"
                   fit="cover"
                   width="175px"
                   height="175px"
                   img-class="fetch-list-img"
                   aspect-ratio="1"
                   :src="item.img_file_path_template" alt="Item Image" >
                 </q-img>
               </q-card>
             </div>
           </div>
           <div class="q-mt-sm flex justify-center items-center" v-if="Object.keys(getGroupedTemplate).length === 0">
             <div class="text-h5">アイテムが見つかりません</div>
           </div>
         </q-scroll-area>
</template>
<style lang="scss" scoped>

.content {
  overflow-y: auto;
  height: calc(100dvh - 170px);
}
.desktop-layout {
  display: none !important;
}

@media (min-width: $breakpoint-sm) {
  .desktop-layout {
    display: flex !important;
  }

  .mobile-layout {
    display: none;
  }
}
.grid-item {
  flex: 0 0 auto;
  flex-basis: calc(100% - 16px) !important;
  cursor: pointer;
  max-height: 260px;
  max-width: 260px;
  aspect-ratio: 300/300;
  @media screen and (min-width: 500px) {
    flex-basis: calc(50% - 16px);
  }
  @media screen and (min-width: 950px) {
    flex-basis: calc(250px - 16px);
  }
  @media screen and (min-width: 1260px) {
    flex-basis: calc(250px - 16px);
  }
  .q-img {
    width: 100%;
    height: 100%;
  }
  .video-container {
    width: 100%;
    position: relative;
  }
  img,
  .video-container,
  video {
    width: 100%;
    border-radius: 8px;
    height: 100%;
    object-fit: cover;
    max-height: 300px;
    aspect-ratio: 300/300;
  }
}

.collapsable-container {
  width: 100%;
  height: fit-content;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: start;
  gap: 16px;
  position: relative;
  transition: height 0.3s ease-out;
  &.collapse {
    height: 0;
  }
}

.close-collapsable-container {
  position: absolute;
  bottom: 0px;
  right: 5px;

  &.close {
    bottom: 0;
    right: 5px;
  }
}

.carousel {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10000;
  overflow-x: auto;
  width: 100%;
}

@media (min-width: $breakpoint-sm) {
  .desktop-layout {
    display: flex !important;
  }

  .mobile-layout {
    display: none;
  }
}

.fetch-list-img {
  max-height: 258px;
  height: 100%;
}
.row-wrapper {
  display: flex;
  width: 100%;
  
  .col-3 {
    all: unset;
    width: 25%;
  }
  .col-9 {
    all: unset;
    width: 75%;
  }
}
</style>
