// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$typography-font-family: 'Noto Sans JP', sans-serif !default;

$body-font-size: 15px !default;

$primary: #424242;
$secondary: #c1a14e;
$accent-900: #9c7c45;
$accent-800: #c1a14e;
$accent-700: #d7b653;
$accent-600: #edcc5a;
$accent-500: #fddd5f;
$accent-400: #ffe274;
$accent-300: #ffe88b;
$accent-200: #ffefaa;
$accent-100: #fff4cb;
$accent-050: #fffbea;

$black: #000000;
$white: #ffffff;
$grey-900: #212121;
$grey-800: #424242;
$grey-700: #616161;
$grey-600: #757575;
$grey-500: #9e9e9e;
$grey-400: #bdbdbd;
$grey-300: #e0e0e0;
$grey-200: #eeeeee;
$grey-100: #f5f5f5;
$grey-050: #fafafa;
$grey-333: #333;

$blue-800: #144c76;
$blue-700: #45589c;
$blue-500: #3f56a7;
$blue-400: #d0ebfe;
$blue-200: #aac2ff;
$blue-100: #cbdaff;

$dark: #1d1d1d;
$dark-page: #121212;

$error: #ffeced;
$light-blue: #3e7fff;
$dark-blue: #033C71;
$pale-blue: #E3EAEF;
$blue: #0057ff;

$positive: #00ad35;
$negative: #be0123;
$info: #31ccec;
$warning: #868800;

$green-100: #e3f9e6;
$green-200: #baefc9;
$green-800: #456f50;
$green-900: #012c0d;

$tosca: #b5e9c1;
$sky-blue: #beccee;
$blush: #eebedb;
$light-blush: #ffecf7;
$disablebtnBackgroundColor: #e8e8e8;
$disablebtnPulldownBackgroundColor: #f3f3f3;

$light-prescription-blue: #e9efff;

$light-shot-blue: #f1e9ff;

$btnBackgroundColor: #333333;
$btnTextColor: #ffffff;
$yellow: #fff851;
$orange: #ffb444;
$red: #ff4769;
$pinkBlush: #e99ca7;
$darkRed: #810000;
$c-purple: #cdb7f2;

// Queue Ticket Colors
$ticket1: #014ca5;
$ticket2: #399f28;
$ticket3: #9747ff;
$ticket4: #69acc9;

$prescriptionBadgeLabel: #a36200;

$h1: (
  size: 26px,
  line-height: 26px
) !default;
$h2: (
  size: 24px,
  line-height: 24px
) !default;
$h3: (
  size: 22px,
  line-height: 22px
) !default;
$h4: (
  size: 20px,
  line-height: 20px
) !default;
$h5: (
  size: 18px,
  line-height: 18px
) !default;
$h6: (
  size: 16px,
  line-height: 16px
) !default;

$pc: 1080px;
$tablet: 768px;

// Loop to create the gap-[n] classes
$max-gap: 20;
@for $i from 1 through $max-gap {
  .gap-#{$i} {
    gap: #{4 * $i}px;
  }
}

$max-z-index: 20;
@for $i from 1 through $max-z-index {
  .z-index-#{$i} {
    z-index: #{$i};
  }
}
