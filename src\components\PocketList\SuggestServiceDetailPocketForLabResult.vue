<script lang="ts" setup>
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import UpdateServiceDetailModal from '@/pages/request/serviceDetail/UpdateServiceDetailModal.vue'
import useServiceDetailStore from '@/stores/service-details'
import useRequestStore from '@/stores/requests'
import mtUtils from '@/utils/mtUtils'
import useEmployeeStore from '@/stores/employees'
import { dateFormat, getCustomerNameWithCode, getEmployeeDisplayName, getPetFirstName } from '@/utils/aahUtils'
import useCategoryStore from '@/stores/categories'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import { useRouter } from 'vue-router'

const employeeStore = useEmployeeStore()
const serviceDetailStore = useServiceDetailStore()
const requestStore = useRequestStore()
const categoryStore = useCategoryStore()
const { getRequest } = storeToRefs(requestStore)
const props = defineProps({ data: Object, refetchAll: { type: Boolean, default: false } })

const employeeName = (value, type) => {
  let employee_name
  switch (type) {
    case 'doctor':
      const docEmployee = employeeStore.getDoctors.find((v) => v.id_employee === value)
      employee_name = getEmployeeDisplayName(docEmployee)
      break

    case 'staff':
      const staEmployee = employeeStore.getNonDoctors.find((v) => v.id_employee === value)
      employee_name = getEmployeeDisplayName(staEmployee)
      break
  }
  return employee_name
}
const categoryName = (value: number) => categoryStore.getAllCategories.find((v) => v.value === value)
const openItemServiceDetailModal = async (item) => {
  serviceDetailStore.setServiceDetail(item)
  await mtUtils.mediumPopup(UpdateServiceDetailModal, {
    data: getRequest.value,
    refetchAll: props.refetchAll
  })
}

const router = useRouter()

const onNumberRequestClick = (value: number) => {
  if (value) {
    const route = router.resolve({
      name: 'RequestDetail',
      params: { id: value }
    })
    window.open(route.href, '_blank')
  }
}
</script>

<template>
  <div v-if="props.data" class="full-width">
    <div class="item_divier_item_service bg-light-blush" @click="openItemServiceDetailModal(props.data)">
      <div class="justify-between items-center q-pb-xs">
        <div class="q-py-xs">
          <div class="row q-col-gutter-md text-grey-800 q-mb-sm">
            <div class="col-9 flex items-center text-center">
              <span v-if="props.data.datetime_service_start == props.data.datetime_service_end">
                {{ dateFormat(props.data.datetime_service_start) }}
              </span>
            </div>
            <div class="col-3">
              <MtFormRadiobtn
                v-model="serviceDetailStore.selectedLabResultServiceDetail"
                :val="props.data"
                class="col-2 q-mr-lg"
                @update:selected="
                  (val) => {
                    serviceDetailStore.setSelectedLabResultServiceDetail(val)
                  }
                "
              />
            </div>
          </div>
          <div class="q-mb-xs">
            <div class="row">
              <span class="title2 bold text-grey-900">
                {{ props.data.name_item_service }}
              </span>
            </div>
            <span v-if="props.data.id_category1" class="text-grey-700 q-mb-xs">
              <span class="q-mr-sm caption2">
                {{ categoryName(props.data.id_category1)?.label }}
                <q-icon color="grey-500" name="arrow_right" size="12px" />
              </span>
              <span class="caption2">
                {{ categoryName(props.data.id_category2)?.label }}
              </span>
            </span>
          </div>

          <div class="flex q-my-xs">
            <span v-if="props.data.id_employee_doctor" class="text-grey-800 q-mr-md q-mb-sm widthToTruncate">
              <span class="field-label1">担当医</span>
              <span class="ellipsis ellipsis-1-lines">{{ employeeName(props.data.id_employee_doctor, 'doctor') }}</span>
            </span>
            <span v-if="props.data.id_employee_staff" class="text-grey-800 q-mr-md widthToTruncate">
              <span class="field-label1">VT</span>
              <span class="ellipsis ellipsis-1-lines">{{ employeeName(props.data.id_employee_staff, 'staff') }}</span>
            </span>
          </div>
          <div class="flex q-mt-xs" @click.stop="onNumberRequestClick(props.data.request.id_request)">
            <span
              v-if="props.data.request && props.data.request.number_request"
              class="text-grey-800 q-mr-md text-blue cursor-pointer"
            >
              {{ props.data.request.number_request }}
            </span>
          </div>
          <div class="flex q-mt-xs">
            <span class="text-grey-800 q-mr-md text-blue cursor-pointer">
              {{ getCustomerNameWithCode(props.data.customer) }}
            </span>
          </div>
          <div class="flex">
            <span class="text-grey-800 q-mr-md text-blue cursor-pointer">
              {{ props.data.pet.code_pet }} {{ getPetFirstName(props.data.pet) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <p v-else class="q-pl-md text-grey-500">治療・サービスはありません</p>
</template>
<style lang="scss" scoped>
.item_divier_item_service {
  cursor: pointer;
  border-radius: 10px;
  padding: 8px 10px 7px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  line-height: 1.4;

  &:hover {
    background-color: rgba(213, 239, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &:active {
    background-color: rgba(236, 248, 255, 0.8);
  }

  .prescription-name {
    max-width: 30vw;

    @media screen and (max-width: 1440px) {
      max-width: 22vw;
    }
  }

  .field-label-free-color {
    padding: 2px 8px;
    border-radius: 4px;
    margin-right: 4px;

    &.chip-blue {
      background: $primary;
    }

    &.chip-red {
      background: $negative;
    }

    &.chip-purple {
      background: $secondary;
    }

    &.chip-green {
      background: $positive;
    }
  }

  .body2 {
    font-size: 14px;
    line-height: 1.3;
  }

  .caption2 {
    font-size: 12px;
    line-height: 1.2;
  }

  .text-grey-700 {
    line-height: 1.25;
  }
}

.widthToTruncate {
  max-width: 30vw;

  @media screen and (max-width: 1100px) {
    max-width: 30vw;
  }

  @media screen and (max-width: 1040px) {
    max-width: 25vw;
  }
}

.pocket-staff-label {
  background: #e1e5eb;
  color: #111;
  padding: 2px 6px;
  border-radius: 4px;
  margin: 2px 3px 2px 0;
}

.c-position {
  top: 0;
  right: 0;
}

.field-label-free-color-small {
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 0.75rem;

  &.chip-blue {
    background: $primary;
  }

  &.chip-red {
    background: $negative;
  }

  &.chip-purple {
    background: $secondary;
  }

  &.chip-green {
    background: $positive;
  }
}
</style>
