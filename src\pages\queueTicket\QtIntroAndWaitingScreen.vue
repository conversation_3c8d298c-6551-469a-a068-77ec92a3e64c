<script setup lang="ts">
import { useQueueTicketUtils } from './queueTicketUtils'
const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  getRoomName,
} = useQueueTicketUtils()
</script>
<template>
   <transition name="fade">
    <div class="flex justify-center items-center ticket-to-call" v-if="isQtToBeCalled">
      <video width="100%" height="100%" autoplay muted playsinline>
        <source src="video/queueticketdetail/queue-ticket-to-call.mp4" type="video/mp4">
      </video>
    </div>
  </transition>
  <transition name="fade">
    <div class="qtcalling-screen-container q-pa-md" v-if="isQtCalling">
      <div class="text-center calling-text text-weight-bold">お呼び出し中</div>
      <div class="text-center ticket-number text-weight-bold">{{ callingQt.number_queue_ticket}}</div>
      <div class="text-center room-number text-weight-bold" v-if="callingQt.queue_detail?.id_room">
        <span class="room-text">ROOM</span> 
        {{ getRoomName(callingQt.queue_detail?.id_room) }}
      </div>
    </div>
  </transition>
</template>
<style src="./WaitingScreens.scss" lang="scss" scoped></style>