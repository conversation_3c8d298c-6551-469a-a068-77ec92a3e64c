<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import UpdateTicketModal from '@/pages/queueTicket/checkIn/UpdateTicketModal.vue'
import mtUtils from '@/utils/mtUtils'
import useClinicStore from '@/stores/clinics'
import QrCodeStream from './QrCodeStream.vue'
import logoDefault from '@/assets/img/login/logo.png'
import useCliCommonStore from '@/stores/cli-common'
import useEmployeeStore from '@/stores/employees'
import {
  openLargeModal
} from './checkIn/checkInUtils'
import { storeToRefs } from 'pinia'
import { typeCheckInCustomer } from '@/stores/queue_ticket'

// クリニックストアの使用
const ClinicStore = useClinicStore()
const cliCommonStore = useCliCommonStore()
const { getClinic } = storeToRefs(ClinicStore)
const clinicName = ref("")
const logo_file_path1 = ref('')
const skipQrNewCustomerFlow = ref<boolean>(true)

const INTERVAL_MS = 1 * 60 * 1000 // 5 minutes
let periodicInterval: ReturnType<typeof setInterval> | null = null

const startPeriodicInterval = () => {
  if (periodicInterval) clearInterval(periodicInterval)
  periodicInterval = setInterval(() => {
    init()
  }, INTERVAL_MS)
}

const stopPeriodicInterval = () => {
  if (periodicInterval) {
    clearInterval(periodicInterval)
    periodicInterval = null
  }
}

// Patch modal openers to pause/resume periodic interval
const patchModalUtils = () => {
  const originalPopup = mtUtils.popup
  mtUtils.popup = async function(...args) {
    stopPeriodicInterval()
    try {
      const result = await originalPopup.apply(this, args)
      return result
    } finally {
      startPeriodicInterval()
    }
  }
  const originalMediumPopup = mtUtils.mediumPopup
  mtUtils.mediumPopup = async function(...args) {
    stopPeriodicInterval()
    try {
      const result = await originalMediumPopup.apply(this, args)
      return result
    } finally {
      startPeriodicInterval()
    }
  }
  const originalSmallPopup = mtUtils.smallPopup
  mtUtils.smallPopup = async function(...args) {
    stopPeriodicInterval()
    try {
      const result = await originalSmallPopup.apply(this, args)
      return result
    } finally {
      startPeriodicInterval()
    }
  }
}

// クリニック名を取得する関数
const fetchClinicName = async () => {
  const id_clinic = localStorage.getItem('id_clinic')
  if (Boolean(id_clinic)) {
    const clinic = await ClinicStore.fetchClinicById(JSON.parse(id_clinic))
    clinicName.value = clinic.name_clinic_display
    logo_file_path1.value = clinic.logo_file_path1
    // @note check enum typeCheckInNewCustomer
    skipQrNewCustomerFlow.value = (clinic.type_checkin_new_customer || 1) === 3
  } else {
    clinicName.value = ""
  }
}

const fetchCliCommonPurposeVisit = async () => {
  await cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [4] }, true)
}

// チェックインが受け入られた時に実行される関数
const checkInAccepted = async () => {
  await mtUtils.mediumPopup(QrCodeStream, null, false, '55%')
  await init()
}

const init = async () => {
  await fetchClinicName()
  await fetchCliCommonPurposeVisit()
  await useEmployeeStore().fetchPreparationEmployees()
}

const addNewCustomer = async () => {
  const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup
  await popupFunction(UpdateTicketModal, {
    flgNewCustomer: true,
    todayQtickets: [],
    skipQrNewCustomerFlow: skipQrNewCustomerFlow.value
  }, true)
}

const allowAddCustomer = computed(() => {
  return getClinic.value && (
    getClinic.value?.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_FOR_NEW_CUSTOMER ||
    getClinic.value?.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_WITH_QR ||
    getClinic.value?.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_WITH_NO_QR
  )
})

// コンポーネントがマウントされたときにクリニック名を取得
onMounted(async () => {
  patchModalUtils()
  await init()
  startPeriodicInterval()
})

onUnmounted(() => {
  stopPeriodicInterval()
})
</script>

<template>
  <MtHeader>
    <q-toolbar class="text-primary q-pa-none">
      <q-toolbar-title class="title2 bold text-grey-900">
        受付
      </q-toolbar-title>
    </q-toolbar>
  </MtHeader>
  <div class="window-height window-width row justify-center items-center checkin-touch-none" @click="checkInAccepted()">
    <div class="col-lg-8 col-sm-12 q-pa-xl">
      <img 
        :src="logo_file_path1 || logoDefault" 
        alt="logo" 
        class="checkin-img-cont q-mb-lg" />
      <p class="checkin-info-text q-px-xl ">『受付する』ボタンを押して、<br/>診察券QRコードを読み取ってください。</p>
      <div class="row justify-center q-pa-md">
        <q-btn flat class="checkin-accept-btn">
          <span>受付する</span>
        </q-btn>
      </div>
      <div class="row justify-center q-pa-md" v-if="allowAddCustomer">
        <q-btn outline class="checkin-new-customer-btn text-weight-bold" @click.stop="addNewCustomer">
          <span>初めての方はこちら</span>
        </q-btn>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
:root {
  --checkin-btn-dark-blue: #033C71;
  --checkin-xx-large-text-size: 100px;
  --checkin-large-text-size: 40px;
  --checkin-regular-text-size: 36px;
  --checkin-medium-text-size: 28px;
  --checkin-small-text-size: 24px;
  --checkin-confirmation-regular-text-size: 32px;
  --checkin-confirmation-qr-code-size: 256px;
  --checkin-confirmation-qr-code-msg-wrapper-width: unset;
  --checkin-confirmation-qr-code-msg-wrapper-pr: 60px;
  --checkin-confirmation-qr-code-msg-wrapper-pl: 215px;
  --checkin-confirmation-pet-img-size: 352px;
  --checkin-content-selection-btn-height: 120px;
  --checkin-first-screen-accept-btn-font-size: 3rem;
  --checkin-first-screen-screen-text-font-size: 2rem;
}

.mobile.platform-ios {
  --checkin-xx-large-text-size: 80px;
  --checkin-large-text-size: 32px;
  --checkin-regular-text-size: 28px;
  --checkin-medium-text-size: 24px;
  --checkin-small-text-size: 20px;
  --checkin-confirmation-regular-text-size: 20px;
  --checkin-confirmation-qr-code-size: 230px;
  --checkin-confirmation-qr-code-msg-wrapper-width: 67%;
  --checkin-confirmation-qr-code-msg-wrapper-pr: 0px;
  --checkin-confirmation-qr-code-msg-wrapper-pl: 130px;
  --checkin-confirmation-pet-img-size: 250px;
  --checkin-content-selection-btn-height: 85px;
  --checkin-first-screen-accept-btn-font-size: 2rem;
  --checkin-first-screen-screen-text-font-size: 1.75rem;
}

@media (max-width: 1528px) {
  :root {
    --checkin-confirmation-regular-text-size: 20px;
    --checkin-confirmation-qr-code-size: 230px;
    --checkin-confirmation-qr-code-msg-wrapper-width: 67%;
    --checkin-confirmation-qr-code-msg-wrapper-pr: 0px;
    --checkin-confirmation-qr-code-msg-wrapper-pl: 130px;
    --checkin-confirmation-pet-img-size: 250px;
    --checkin-first-screen-accept-btn-font-size: 2rem;
    --checkin-first-screen-screen-text-font-size: 1.75rem;
  }
}

@media (max-width: 1280px) {
  :root {
    --checkin-confirmation-regular-text-size: 20px;
    --checkin-confirmation-qr-code-size: 230px;
    --checkin-confirmation-qr-code-msg-wrapper-width: 80%;
    --checkin-confirmation-qr-code-msg-wrapper-pr: 0px;
    --checkin-confirmation-qr-code-msg-wrapper-pl: 130px;
    --checkin-confirmation-pet-img-size: 250px;
    --checkin-first-screen-accept-btn-font-size: 2rem;
    --checkin-first-screen-screen-text-font-size: 1.75rem;
  }
}

.mobile {
  &.platform-ios {
    .content.checkin-feat {
      height: calc(100dvh - 7.44dvh) !important;
    }
    .medium .content.checkin-feat {
      height: calc(100vh - 48px) !important;
    }
  }
}
@media screen and (max-width: 1100px) {
  .medium .content.checkin-feat {
    height: calc(100vh - 48px) !important;
  }
}
@media screen and (min-width: 1100px) {
  .medium .content.checkin-feat {
    height: calc(100vh - 48px) !important;
  }
}

.checkin-feat {
  overflow: hidden;
  // height: 100%;
  .checkin-feat-wrapper {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .qt-wrapper {
      display: flex;
      flex-direction: column;
      padding-top: 40px;
      padding-left: 0px;
      padding-right: 0px;
      flex-grow: 1;
      overflow: hidden;
      .info-content {
        flex-shrink: 1;
        padding-left: 40px;
        padding-right: 40px;
      }
      .info-selection {
        flex-grow: 1;
        overflow-y: auto;
        padding-left: 40px;
        padding-right: 40px;
        align-content: baseline;
        &.content-center {
          align-content: center;
        }
      }
    }
  }
  .outline-btn {
    &:before {
      border-color: var(--checkin-btn-dark-blue);
    }
    &.weighted {
      &:before {
        border-width: 3px;
        border-radius: 20px;
      }
    }
    .q-btn__content {
      color: var(--checkin-btn-dark-blue);
    }
  }
  .top-btn {
    font-size: 32px;
    padding: 18px 20px;
    font-weight: 600;
  }
  .content-selection-btn {
    border-radius: 5px;
    padding: 15px 35px;
    background-color: #E2F9FF !important;
    border: none !important;
    box-shadow: 2px 2px 2px 2px #00000029;
    height: var(--checkin-content-selection-btn-height) !important;
    .q-btn__content {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 15px;
    }
    &.selected {
      background-color: var(--checkin-btn-dark-blue) !important;
      color: #fff;
    }
    .content-selection-btn-content {
      display: flex;
      flex-direction: row;
      justify-content: start;
      align-items: baseline;
      gap: 10px;
      .pet-add-btn {
        color: var(--checkin-btn-dark-blue);
      }
    }
  }
  .pet-overview-row {
    .pet-selection-item {
      background-color: #FFEFAA;
      border-radius: 8px;
      padding: 8px 16px;
      border: 1px solid #00000029;
    }
  }
  .header {
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 24px;
    padding-bottom: 24px;
    align-items: center;
  }
  *,
  .header,
  .info,
  .pet-overview-row,
  .content-selection-btn {
    &.text-large,
    .text-large {
      font-size: var(--checkin-large-text-size);
      .weighted {
        font-weight: 600;
      }
      .normal {
        font-weight: 400 !important;
      }
    }
    &.text-xx-large,
    .text-xx-large {
      font-size: var(--checkin-xx-large-text-size);
      .weighted {
        font-weight: 600;
      }
      .normal {
        font-weight: 400 !important;
      }
    }
    &.text-regular,
    .text-regular {
      font-size: var(--checkin-regular-text-size);
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    &.text-medium,
    .text-medium {
      font-size: var(--checkin-medium-text-size);
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    &.text-small,
    .text-small {
      font-size: var(--checkin-small-text-size);
      &.weighted {
        font-weight: 600;
      }
      &.normal {
        font-weight: 400 !important;
      }
    }
    .pet-avatar {
      width: 70px;
      height: 70px;
      background-color: #E2F9FF;
      border-radius: 50%;
      border: 4px solid #FFFFFF;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
      &:has(.no-border) {
        border: none !important;
      }
    }
  }
  .action-btns {
    padding-left: 40px;
    padding-right: 40px;
    padding-bottom: 24px;
    padding-top: 24px;
    .q-btn {
      width: 40%;
      max-height: 90px;
      font-size: var(--checkin-regular-text-size);
      border-radius: 20px;
      padding: 0px 28px;
      font-weight: 600;
    }
    .cancel {
      background: #FFF !important;
    }
    .next {
      background: var(--checkin-btn-dark-blue);
    }
  }
}
.checkin-img-cont {
  max-height: 180px;
  max-width: 450px;
  display: block;
  margin: 0 auto;
}

.checkin-info-text {
  color: #2e2e2e;
  text-align: center;
  font-family: Noto Sans JP;
  font-size: var(--checkin-first-screen-screen-text-font-size);
  font-style: normal;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: normal;
}

.checkin-accept-btn {
  width: 30rem;
  padding: 1rem 0.75rem;
  border-radius: 0.25rem;
  border: transparent;
  background: var(--checkin-btn-dark-blue);
  box-shadow: 4px 4px 4px 0px rgba(0, 0, 0, 0.25);
  color: #ffffff;
  font-size: var(--checkin-first-screen-accept-btn-font-size);
  line-height: 2.5;
  font-weight: 700;
  border-radius: 20px;
}

.checkin-new-customer-btn {
  font-size: var(--checkin-regular-text-size);
  width: 30rem;
  padding: 1rem 0.75rem;
  border-radius: 20px;
  &:before {
    border-color: var(--checkin-btn-dark-blue);
    border-width: 3px;
  }
  .q-btn__content {
    color: var(--checkin-btn-dark-blue);
  }
}

.checkin-touch-none {
  touch-action: none;
}

@media only screen and (max-width: 1600px) {
  .checkin-img-cont {
    margin-bottom: 3.5rem;
  }
}

@media only screen and (min-width: 1601px) {
  .checkin-img-cont {
    margin-bottom: 3rem;
  }
}
</style>
