<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import mtUtils from '@/utils/mtUtils'
import aahValidations from '@/utils/aahValidations'
import aahMessages from '@/utils/aahMessages'
import { ref, reactive, onMounted, computed } from 'vue'
import { GenericValueLabelType, Common, CliCommon, Category } from '@/types/types'
import { storeToRefs } from 'pinia'
import { forEach, groupBy, mapValues, sortBy } from 'lodash'
import draggable from 'vuedraggable'

import useCategoryStore from '@/stores/categories'
import useLabPrintStore from '@/stores/lab-prints'
import useCommonStore from '@/stores/common'
import useCliCommonStore from '@/stores/cli-common'
import useItemStore from '@/stores/items'
import useLabDeviceStore from '@/stores/lab-devices'
import MtInputForm from '@/components/form/MtInputForm.vue'
import { scrollAreaHeight } from '@/utils/aahUtils'
import UpdateLabGroupNameModal from './UpdateCarteConfigNameModal.vue'
import useCarteConfigStore from '@/stores/carteConfig'
import { typeCarteSource } from '@/utils/enum'
import useItemServiceUnitStore from '@/stores/item-service-units'
import useTextTemplateStore from '@/stores/text-template'

const categoryStore = useCategoryStore()
const carteConfigStore = useCarteConfigStore()
const commonStore = useCommonStore()
const cliCommonStore = useCliCommonStore()
const itemServiceStore = useItemStore()
const itemServiceUnitStore = useItemServiceUnitStore()
const textTemplateStore = useTextTemplateStore()
const labDeviceStore = useLabDeviceStore()
const { getCategoriesLB1, getCategoriesLB2 } = storeToRefs(categoryStore)
const { getCommonDeviceOptionActiveList, getCommonDeviceOptionList } = storeToRefs(commonStore)
const { getAllCliCommonMedConditionOptionList, getCliCommonTypeMemoCarteList } = storeToRefs(cliCommonStore)
const { getAllParentCarteConfig, getCarteConfig } = storeToRefs(carteConfigStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  idCarteConfig?: number,
  callback: Function
}
const props = withDefaults(defineProps<Props>(), {
  callback: () => {}
})

const MED_CONDITION = 1, ITEM_SERVICE_UNIT = 2, SCHEMA_TEXT_TEMPLATE = 3, TEXT_TEMPLATE = 4
const MAX_ROWS_PER_PAGE = 56
const groupArr = [
  {value: 'med_condition', label: 'MED CONDITION'},
  {value: 'lab_set', label: 'LAB SET'},
  {value: 'schema_text_template', label: 'SCHEMA TEXT TEMPLATE'},
  {value: 'text_template', label: 'TEXT TEMPLATE'}
]
const filteredList = ref({
  med_condition: [],
  lab_set: [],
  schema_text_template: [],
  text_template: []
})
const carteConfigOptions = ref([]), carteConfigOptionsDefault = reactive([])
const typeCarteSourceOptions = ref(typeCarteSource)

const search = reactive({
  type_carte_source: MED_CONDITION,
  id_carte_config_ref: null,
})

const petBioRows = [
  { label: '体重', uniqueId: `petbio-1`, code: 1 },
  { label: '体温 T', uniqueId: `petbio-2`, code: 2 },
  { label: '心拍 P', uniqueId: `petbio-4`, code: 4 },
  { label: '呼吸数 R',uniqueId: `petbio-8`, code: 8 }
]

const categoriesLB2DefaultList = reactive<GenericValueLabelType[]>([])
const categoriesLB2List = ref<GenericValueLabelType[]>([])

const commonDeviceDefaultList = reactive<GenericValueLabelType[]>([])
const commonDeviceList = ref<GenericValueLabelType[]>([])

const commonMedConditionDefaultList = reactive<GenericValueLabelType[]>([])
const commonMedConditionList = ref<GenericValueLabelType[]>([])

const deletedItem = ref([] as any)
const allLabs = ref([] as any), 
 selectedLabs = ref([] as any)

const temporarlyMovedRows = ref<Set<number>>(new Set([])),
  selectedLabIds = ref<Set<number>>(new Set([]))

const isEdit = ref(false)  

const getTotalPages = computed(() => {
  const totalLabs = selectedLabs.value.reduce((acc, curr) => acc + (curr.name_button_lab_print ? 0 : 1), 0)
  if(totalLabs > MAX_ROWS_PER_PAGE) {
    const totalPages = Math.floor(totalLabs / MAX_ROWS_PER_PAGE)
    const remainingItems = totalLabs - (totalPages * MAX_ROWS_PER_PAGE)
    return `${totalPages} page ${MAX_ROWS_PER_PAGE}(rows) + ${remainingItems} items`
  }
  return `1 page ${totalLabs}(items)`
})

const addLabToSelectedList = (idx: number) => {
  const carteConfigRecord = allLabs.value[idx]
  if(carteConfigRecord) {
    if (selectedLabs.value.find((v) => 
      (carteConfigRecord.id_cli_common && v.id_cli_common == carteConfigRecord.id_cli_common) ||
      (carteConfigRecord.id_cli_common && v.id_cli_cm_med_review == carteConfigRecord.id_cli_common) ||
      (carteConfigRecord.id_item_service_unit && v.id_item_service_unit == carteConfigRecord.id_item_service_unit) ||
      (carteConfigRecord.id_text_template && v.id_text_template == carteConfigRecord.id_text_template)
    )) return

    selectedLabs.value.push({...carteConfigRecord, flg_delete: 0})
    selectedLabIds.value.add(carteConfigRecord.uniqueId)

    rewriteDisplayOrder()
    updateList()
  }
}

const removeSelectedItem = (element: any) => {
  let selectedItem = {} as any
  if (element.id_cli_common) {
    selectedItem = selectedLabs.value.findIndex((v) => v.id_cli_common == element.id_cli_common)
    if (selectedItem != -1) {
      deletedItem.value.push(selectedLabs.value[selectedItem])
      selectedLabs.value[selectedItem].flg_delete = 1
    }
  } else if (element.id_cli_cm_med_review) {
    selectedItem = selectedLabs.value.findIndex((v) => v.id_cli_cm_med_review == element.id_cli_cm_med_review)
    if (selectedItem != -1) {
      deletedItem.value.push(selectedLabs.value[selectedItem])
      selectedLabs.value[selectedItem].flg_delete = 1
    }
  } else if (element.id_item_service_unit) {
    selectedItem = selectedLabs.value.findIndex((v) => v.id_item_service_unit == element.id_item_service_unit)
    if (selectedItem != -1) {
      deletedItem.value.push(selectedLabs.value[selectedItem])
      selectedLabs.value[selectedItem].flg_delete = 1
    }
  } else if (element.id_text_template) {
    selectedItem = selectedLabs.value.findIndex((v) => v.id_text_template == element.id_text_template)
    if (selectedItem != -1) {
      deletedItem.value.push(selectedLabs.value[selectedItem])
      selectedLabs.value[selectedItem].flg_delete = 1
    }
  }

  rewriteDisplayOrder()
  updateList()
}

const rewriteDisplayOrder = () => {
  let number = 1
  selectedLabs.value = selectedLabs.value?.filter((v) => v.flg_delete === 0).map((v, idx) => {
    return {
      ...v,
      display_order: idx + 1,
      number: number++
    }
  })
}

const resetCarteConfigList = () => {
  allLabs.value.length = 0
  search.id_cli_common = search.code_labcat2 = search.device = null
}

const handleTypeCarteSourceOptions = async (val: number) => {
  resetCarteConfigList()
  if(val === MED_CONDITION) {
    if (getCliCommonTypeMemoCarteList.value.length > 0) {
      allLabs.value = sortBy(
        getCliCommonTypeMemoCarteList.value.map(v => ({
          ...v,
          uniqueId: getUniqueId(v),
          label: v.name_cli_common,
          label_button: v.name_cli_common,
          type_carte_source: MED_CONDITION
        }))
      , 'display_order')
    }
  } else if(val === ITEM_SERVICE_UNIT) {
    await itemServiceUnitStore.fetchItemServiceUnits({ list_test: true })

    itemServiceUnitStore.getItemServiceUnits.forEach(itemService => {
      allLabs.value.push({
        ...itemService,
        id_item_service_unit: itemService.id_item_service_unit,
        label: itemService?.name_service_item_unit,
        label_button: itemService?.name_service_item_unit,
        type_carte_source: ITEM_SERVICE_UNIT
      })
    })
  } else if(val === SCHEMA_TEXT_TEMPLATE) {
    await textTemplateStore.fetchTemplates({ type_text_template: 100 })
    textTemplateStore.getTemplates.forEach(template => {
      allLabs.value.push({
        ...template,
        label: template.memo_template,
        label_button: template.memo_template,
        type_carte_source: SCHEMA_TEXT_TEMPLATE
      })
    })
  } else if(val === TEXT_TEMPLATE) {
    await textTemplateStore.fetchTemplates({ type_text_template: 21 })
    textTemplateStore.getTemplates.forEach(template => {
      allLabs.value.push({
        ...template,
        label: template.memo_template,
        label_button: template.memo_template,
        type_carte_source: SCHEMA_TEXT_TEMPLATE
      })
    })
  }

  allLabs.value = sortBy(allLabs.value.map((v, k) => ({...v, number: k + 1, uniqueId: getUniqueId(v)})), 'display_order')
}

const submit = async () => {
  if(selectedLabs.value.length === 0) {
    mtUtils.autoCloseAlert('Please select at least one lab')
    return
  }

  const updatedValue = [...selectedLabs.value.filter((v) => v.flg_delete === 0), ...deletedItem.value.map((v) => ({...v, flg_delete: 1}))]

  let carteConfigItems = updatedValue
    .map((v) => {
      const labItem = {
        type_config_layer: 2,
        id_carte_config_ref: search.id_carte_config_ref,
        type_carte_source: v.type_carte_source,
        id_cli_cm_med_review: v.id_cli_common,
        id_item_service_unit: v.id_item_service_unit,
        id_text_template: v.id_text_template,
        label_button: v.label_button,
        display_order: v.display_order,
        flg_delete: v.flg_delete,
      }
      if (v.id_carte_config) labItem.id_carte_config = v.id_carte_config

      return labItem
    })

  carteConfigStore.submitCarteConfig(carteConfigItems).then(() => {
    props.callback()
    mtUtils.autoCloseAlert(aahMessages.success)
    closeModal()
  })
}

const itemClicked = (item: any) => {
  if (item.name_button_lab_print && item.uniqueId.includes('group')) {
    mtUtils.smallPopup(UpdateLabGroupNameModal, {
      groupName: item.name_button_lab_print,
      callBack: (newGroupName: string) => {
        const index = selectedLabs.value.findIndex((v) => v.id_lab_print == item.id_lab_print)
        selectedLabs.value[index].name_button_lab_print = newGroupName
      }
    })
  }
}

const getUniqueId = (record) => {
  if(record.id_lab_set) return `labset-${record.id_lab_set}`
  else if(record.id_lab_device) return `labdevice-${record.id_lab_device}`
  else if(record.id_lab_ref) return `labref-${record.id_lab_ref}`
  else if(record.type_pet_bio_info) return `petbio-${record.type_pet_bio_info}`
  else {
    const groupList = selectedLabs.value.filter((v) => v.name_button_lab_print)
    return `group-${groupList.length + 1}`
  }
}

const updateList = () => {
  filteredList.value.med_condition = []
  filteredList.value.lab_set = []
  filteredList.value.schema_text_template = []
  filteredList.value.text_template = []

  sortBy(selectedLabs.value.filter((v) => v.flg_delete === 0), 'display_order').map((v) => {
    if (v.id_cli_cm_med_review || v.id_cli_common) filteredList.value.med_condition.push(v)
    else if (v.id_item_service_unit) filteredList.value.lab_set.push(v)
    else if (v.id_text_template) filteredList.value.text_template.push(v)
  })
}

onMounted( async () => {

  categoryStore.getCategoriesLB2.filter((v) => v.flg_active).forEach((v: Category) => {
    categoriesLB2DefaultList.push({ label: v.name_category, value: v.id_category})
  })
  categoriesLB2List.value = [...categoriesLB2DefaultList]

  if (getCommonDeviceOptionActiveList?.value?.length) {
    getCommonDeviceOptionActiveList.value.forEach((item: Common) => {
      commonDeviceDefaultList.push({ value: item.id_common, label: item.name_common })
    })
    commonDeviceList.value = [...commonDeviceDefaultList]
  }

  carteConfigOptions.value = getAllParentCarteConfig.value.map((v) => ({
    label: v.label_button,
    value: v.id_carte_config
  }))
  carteConfigOptionsDefault.push(...carteConfigOptions.value)

 
  if(props.idCarteConfig) {
    isEdit.value = true
    await carteConfigStore.fetchCarteConfigById(props.idCarteConfig)
    const { child_carte_config_list: carteConfigList } = getCarteConfig.value

    selectedLabs.value = sortBy(carteConfigList.map((carteConfigItem) => {
      const uniqueId = getUniqueId(carteConfigItem)
      let tempCarteConfig = {
        ...carteConfigItem,
        label: carteConfigItem.label_button,
        uniqueId,
        flg_delete: 0
      }
      selectedLabIds.value.add(uniqueId)
      return tempCarteConfig
    }).filter((v) => v.flg_delete === 0), 'display_order')

    search.id_carte_config_ref = props.idCarteConfig

    rewriteDisplayOrder()
    updateList()
  }
  handleTypeCarteSourceOptions(MED_CONDITION)

})
</script>
<template>
<q-form @submit="submit">
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title
      class="row no-wrap items-center text-grey-900 title2 bold"
        >出力検査項目
    </q-toolbar-title>
    <q-btn v-if="isEdit" round flat @click="openMenu" class="q-mx-sm">
      <q-icon size="xs" name="more_horiz" />
     </q-btn>
  </MtModalHeader>
  <q-card-section class="content">
    <div class="row q-col-gutter-md">
      <div class="col-4">
        <MtFilterSelect
          label="紐づける出力ボタン"
          v-model:options="carteConfigOptions"
          v-model:options-default="carteConfigOptionsDefault"
          v-model:selecting="search.id_carte_config_ref"
          required
          :rules="[aahValidations.validationRequired]"
          :disable="isEdit"
        />
      </div>
    </div>
    <div class="row q-col-gutter-md q-mt-sm">
      <div class="col-4">
        <MtFormPullDown
          label="出力データ分類"
          v-model:options="typeCarteSourceOptions"
          v-model:selected="search.type_carte_source"
          @update:selected="handleTypeCarteSourceOptions"
          tabindex="2"
        />
      </div>
    </div>
    <div class="row q-col-gutter-md q-mt-sm">
      <div class="col flex column gap-3">
        <q-scroll-area :style="scrollAreaHeight(60, 110)">
          <template v-if="allLabs.length > 0">
            <template v-for="(row, idx) in allLabs">
            <div>
              <div class="bg-grey-300 q-pa-xs" v-if="row.isParent">
                <span> {{ row.label }} </span>
              </div>
              <div v-else class="bg-accent-200 q-py-xs flex items-center justify-between cursor-pointer q-px-lg" @click="addLabToSelectedList(idx)">
                <span>{{ row?.label || row?.lab?.name_lab }}</span>
                <q-icon name="play_arrow" class="text-grey-900" size="13px"/>
              </div>
            </div>
            </template>
          </template>
        </q-scroll-area>
      </div>
      <div class="col flex column gap-3">
        <q-scroll-area :style="scrollAreaHeight(60, 110)">
          <template v-for="groupItem in groupArr">
            <div v-if="filteredList[groupItem.value].length > 0">
              <div class="bg-grey-300 q-pa-xs">
                <span> {{ groupItem.label }} </span>
              </div>
                <template v-for="(element, index) in filteredList[groupItem.value]" :key="groupItem.value + index">
                  <div>
                    <div
                      class="q-py-xs flex justify-between items-center cursor-pointer q-px-md"
                      :id="element.uniqueId"
                      :class="temporarlyMovedRows.has(element.uniqueId) ? 'bg-accent-200' : (element.name_button_lab_print ? 'bg-grey-100' : 'bg-yellow')">
                      <div>
                        <span class="caption1 bold text-grey-800 q-mr-sm" :class="element.flg_indent ? 'q-pl-md' : ''">{{ (index + 1 || '') + "." }}</span> 
                        <span class="caption1 bold text-grey-900">
                          {{ element.labelEn || element.label }}
                        </span>
                      </div>
                      <div class="flex items-center">
                        <q-icon name="close" @click.stop="removeSelectedItem(element)" class="text-darkred" />
                      </div>
                    </div>
                    <div >
                      <q-separator v-if="(index + 1) % MAX_ROWS_PER_PAGE == 0" class="q-my-sm" />
                    </div>
                  </div>
                </template>
            </div>
          </template>
        </q-scroll-area>
      </div>
    </div>
  </q-card-section>
  <q-card-section class="q-bt q-pt-xs bg-white">
    <div class="row">
      <div class="col-9">
        <div class="text-right modal-btn">
          <q-btn 
            outline 
            class="bg-grey-100 text-grey-800"
            @click="closeModal"
            >
          <span>キャンセル</span>
          </q-btn>
          <q-btn
            class="q-ml-md"
            color="primary"
            type="submit"
            unelevated
            >
            <span>保存</span>
          </q-btn>
        </div>
      </div>
      <div class="col-3">
        <div class="text-right q-mt-sm">
          {{ getTotalPages }}
        </div>
      </div>
    </div>
  </q-card-section>
  </q-form>
</template>
<style lang="scss">
.scale-up {
  transform: scale(1.02);
  transition: transform 0.5s;
}
</style>