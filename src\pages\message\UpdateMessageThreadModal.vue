<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import useMessageStore from '@/stores/message-clinic'
import aahMessages from '@/utils/aahMessages'
import useEmployeeStore from '@/stores/employees'
import mtUtils from '@/utils/mtUtils'
import useCustomerStore from '@/stores/customers'
import aahValidations from '@/utils/aahValidations'
import { aahUtilsGetEmployeeName, concatenate, getDateTimeNow, getFullPetName } from '@/utils/aahUtils'
import useTextTemplateStore from '@/stores/text-template'
import { storeToRefs } from 'pinia'
import { sortBy } from 'lodash'
import AddTextTemplateModal from '../task/AddTextTemplateModal.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import { typeCustomerThread, typeLinkCategory, typeThreadClassification } from '@/utils/enum'
import { CliCommon } from '@/types/types'
import useCliCommonStore from '@/stores/cli-common'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'

const messageStore = useMessageStore()
const employeesStore = useEmployeeStore()
const customerStore = useCustomerStore()
const templateStore = useTextTemplateStore()
const cliCommonStore = useCliCommonStore()
const { getTemplates } = storeToRefs(templateStore)
const employeeId = JSON.parse(localStorage?.getItem('id_employee'))
const clinic = localStorage?.getItem('id_clinic')
const emits = defineEmits(['close'])
const customerList = ref<any>([])
const customerListDefault = reactive<any>([])
const employeesList = ref<any>([])
const employeesListDefault = reactive<any>([])
const petList = ref<any>([])
const petListDefault = reactive<any>([])
const typeDepartments = ref<any>([])
const typeDepartmentsDefault = reactive<any>([])
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))

const data = ref({
  name_thread: null,
  memo_goal: null,
  id_employee_ask: null,
  id_employee_answer: null,
  type_department: null,
  code_customer: '',
  code_pet: '',
  id_pet: '',
  pet_details: null,
  threadClassification: 1,
  linkCategory: 0,
  flg_urgent: false,
  flg_closed: false,
  id_customer: '',
  id_link1: '',
  number_link1: '',
  flg_control_name_thread: true
})
const showPets = ref(false)
const showNumberLink = ref(true)
const closeThread = ref(false)
const isEdit = ref(false)
const isCustomerThread = ref(false)
const setErr = ref('')
const threadForm = ref(null)

const closeModal = () => {
  emits('close')
}

const props = defineProps({
  data: Object,
  id_customer: String,
  id_pet: String
})

const addTemplateModalFlg = ref(false), textTemplatesList = ref([])

String.prototype.toHtmlEntities = function () {
  if (
    (/\p{Emoji}/u.test(this) || /(<([^>]+)>)/gi.test(this)) &&
    (!this.includes('https') || /(<([^>]+)>)/gi.test(this)) &&
    (!this.includes('http') || /(<([^>]+)>)/gi.test(this)) &&
    (!this.includes('www.') || /(<([^>]+)>)/gi.test(this))
  ) {
    return this.replace(/[^a-z0-9\s]/gmu, (s) => '&#' + s.codePointAt(0) + ';')
  } else {
    return this
  }
}
const handleEmpName = (empId: number) => {
  if (!empId) return ''
  const id = empId.toString()
  return aahUtilsGetEmployeeName(employeesStore.getAllEmployees, id)
}
const handleSendData = async (threadData: any) => {
  if (props.data?.id_message_thread) {
      let threadDataEdited = {
        name_thread: data.value.flg_control_name_thread
          ? handleAutoTitle()
          : data.value.name_thread,
        memo_goal: data.value.memo_goal,
        type_thread: data.value.threadClassification,
        type_link1:
          data.value.linkCategory !== 0 ? data.value.linkCategory : null,
        id_customer: data.value.id_customer,
        pets: data.value.pets,
        code_customer: data.value.id_customer,
        code_pet: data.value.id_pet,
        flg_pinned: false,
        flg_emr_pinned: false,
        id_employee_ask: data.value.id_employee_ask,
        name_employee_ask: handleEmpName(data.value.id_employee_ask),
        type_department: parseInt(data.value.type_department),
        id_employee_answer: data.value.id_employee_answer,
        name_employee_answer: handleEmpName(data.value.id_employee_answer),
        flg_urgent: data.value.flg_urgent,
        id_employee_insert: employeeId,
        number_link1:
          data.value.linkCategory !== 0 ? data?.value?.number_link1 : null,
        id_link1: data?.value?.id_link1,
        type_thread_list: props.data.type_thread_list,
        flg_allowed_access_customer: 1,
        flg_closed: null,
        datetime_closed: null,
        id_message_thread: props.data?.id_message_thread,
        id_clinic: clinic.value
      }
      threadDataEdited.flg_closed = closeThread.value
      if (closeThread.value) {
        threadDataEdited.datetime_closed = getDateTimeNow()
      }
      messageStore
        .updateThreadMessages(props.data?.id_message_thread, threadDataEdited)
        .then(async () => {
          await messageStore.fetchThreadMessages()
          // show alert based on success or failed
          // messageStore.getRecentThreadMessag will assigned by string 'error_update' if the update is error
          if (!messageStore.getIsErrorThreadMessage) {
            mtUtils.autoCloseAlert(aahMessages.success)
            emits('close')
          } else {
            mtUtils.autoCloseAlert(aahMessages.failed)
          }
          departmentToggleError.value = false
        })
  } else {
    await messageStore.submitThreadMessages(threadData).then(async () => {
      await messageStore.fetchThreadMessages({ 'flg_closed': false })
      emits('close')
      await mtUtils.autoCloseAlert(aahMessages.success)
      departmentToggleError.value = false
    })
  }
}
// Computed property for auto-generated title
const autoGeneratedTitle = computed(() => {
  if (!data.value.flg_control_name_thread) return ''

  let autoTitle = ''
  const selectedTypeThread = isCustomerThread?.value
    ? typeCustomerThread.find(
      (item) => item.value === data.value.threadClassification
    )
    : typeThreadClassification.find(
      (item) => item.value === data.value.threadClassification
    )
  let selectedTypeLink1 = ''
  let numberLink1 = ''
  const selectedCustomer = customerStore.getAllCustomers?.find(
    (cus) => cus.value == data.value.id_customer
  )
  const selectedPet = customerStore.getCustomer?.pets?.find(
    (pet: any) => pet.value == data.value.id_pet
  )

  if (data.value.linkCategory !== 0) {
    selectedTypeLink1 = typeLinkCategory.find(
      (item) => item.value === data.value.linkCategory
    )
    numberLink1 = data.value.number_link1
    autoTitle =
      (selectedTypeThread?.label !== undefined
        ? selectedTypeThread?.label
        : '') +
      ' ' +
      (selectedTypeLink1?.label !== undefined ? selectedTypeLink1?.label : '') +
      ' ' +
      (numberLink1 !== undefined ? numberLink1 : '') +
      ' ' +
      (handleEmpName(data.value.id_employee_answer) !== undefined
        ? handleEmpName(data.value.id_employee_answer)
        : '') +
      ' ' +
      (selectedCustomer?.code_customer !== undefined
        ? selectedCustomer?.code_customer
        : '') +
      ' ' +
      (getFullPetName(selectedPet, selectedCustomer) !== undefined
        ? getFullPetName(selectedPet, selectedCustomer)
        : '')
  } else {
    autoTitle =
      (selectedTypeThread?.label !== undefined
        ? selectedTypeThread?.label
        : '') +
      ' ' +
      (handleEmpName(data.value.id_employee_answer) !== undefined
        ? handleEmpName(data.value.id_employee_answer)
        : '') +
      ' ' +
      (selectedCustomer?.code_customer !== undefined
        ? selectedCustomer?.code_customer
        : '') +
      ' ' +
      (getFullPetName(selectedPet, selectedCustomer) !== undefined
        ? getFullPetName(selectedPet, selectedCustomer)
        : '')
  }

  return autoTitle.trim()
})

// Legacy function for backward compatibility
const handleAutoTitle = () => {
  return autoGeneratedTitle.value
}

// Watch for changes in auto-generated title and update data.name_thread
watch(autoGeneratedTitle, (newTitle) => {
  if (data.value.flg_control_name_thread) {
    data.value.name_thread = newTitle
    // Reset validation when auto-title updates
    nextTick(() => {
      threadForm.value?.resetValidation()
    })
  }
}, { immediate: true })

// Watch for flg_control_name_thread changes
watch(() => data.value.flg_control_name_thread, (isAutoTitle) => {
  if (isAutoTitle) {
    data.value.name_thread = autoGeneratedTitle.value
    // Reset validation when auto-title is enabled
    nextTick(() => {
      threadForm.value?.resetValidation()
    })
  } else {
    data.value.name_thread = ''
  }
})

// Watch for data.type_department changes
watch(() => data.value.type_department, (value) => {
  if (value) {
    departmentToggleError.value = false
  }
})

// Watch for data.value.threadClassification changes
watch(() => data.value.threadClassification, (value) => {
  if (!data.value.memo_goal && data.value.threadClassification === 1) {
    data.value.memo_goal = '依頼されたタスクの報告します。\n【内容】\n\nご確認お願い致します。';
  } else {
    data.value.memo_goal = '';
  }
}, { immediate: true }); // <--- Add this option here

// Computed validation rules for thread name
const threadNameValidationRules = computed(() => {
  if (data.value.flg_control_name_thread) {
    // When auto-title is enabled, no validation needed as title is auto-generated
    return []
  } else {
    // When auto-title is disabled, require manual input
    return [aahValidations.validationRequired]
  }
})

const departmentToggleError = ref(false); 
const submitnewThread = async () => {
  // First validate the form
  const isFormValid = await threadForm.value?.validate()
  if (!isFormValid) {
    return
  }

  if (!data.value.type_department) {
    departmentToggleError.value = true
    return
  }

  let threadData = {
    name_thread: data.value.flg_control_name_thread
      ? handleAutoTitle()
      : data.value.name_thread,
    memo_goal: data.value.memo_goal,
    type_thread: data.value.threadClassification,
    type_link1: data.value.linkCategory !== 0 ? data.value.linkCategory : null,
    id_customer: data.value.id_customer,
    pets: data.value.id_pet ? [data.value.id_pet] : null,
    code_customer: data.value.id_customer,
    code_pet: data.value.id_pet,
    flg_pinned: false,
    flg_emr_pinned: false,
    id_employee_ask: data.value.id_employee_ask,
    name_employee_ask: handleEmpName(data.value.id_employee_ask),
    type_department: parseInt(data.value.type_department),
    id_employee_answer: data.value.id_employee_answer,
    name_employee_answer: handleEmpName(data.value.id_employee_answer),
    flg_urgent: data.value.flg_urgent,
    id_employee_insert: employeeId,
    number_link1: data?.value?.number_link1,
    id_link1: data?.value?.id_link1,
    id_clinic: clinic?.value,
    type_thread_list: 1,
    flg_allowed_access_customer: 1,
    flg_closed: 0,
    datetime_closed: null,
    flg_read: false
  }


  /**
   * Validates and processes thread data based on link category
   * For linked threads (linkCategory !== 0): Requires number_link1 and basic thread info
   * For unlinked threads (linkCategory === 0): Requires basic thread info with control name or thread name
   */
  if (data.value.linkCategory !== 0) {
    // Case 1: Linked thread - validate required fields for linked messages
    const hasRequiredLinkedFields = data?.value?.number_link1 &&
      threadData?.type_department &&
      threadData?.name_employee_ask &&
      threadData?.memo_goal &&
      (threadData?.name_thread || data.value.flg_control_name_thread) // Accept auto-generated title

    if (hasRequiredLinkedFields) {
      handleSendData(threadData)
    } else {
      setErr.value = '* required field'
      data.value.memo_goal = threadData?.memo_goal
    }
  } else {

    // Case 2: Unlinked thread - validate based on control name flag
    const hasBasicThreadInfo = threadData?.type_department &&
      threadData?.name_employee_ask &&
      threadData?.memo_goal

    const canSendWithControlName = hasBasicThreadInfo && data.value.flg_control_name_thread
    const canSendWithThreadName = hasBasicThreadInfo && !data.value.flg_control_name_thread && threadData?.name_thread

    if (canSendWithControlName || canSendWithThreadName) {
      threadData.id_link1 = null
      handleSendData(threadData)
    } else {
      setErr.value = '* required field'
      data.value.memo_goal = threadData?.memo_goal
    }
  }
}
const handlePetsList = async (value: any) => {
  await customerStore.selectCustomer(value)
  if (value) {
    const selectedCustomer = customerStore?.getCustomer
    if (selectedCustomer) {
      if (
        selectedCustomer.pets &&
        selectedCustomer.pets.length &&
        selectedCustomer.pets.length > 0
      ) {
        petListDefault.length = 0
        selectedCustomer.pets.map((petObj: any) => {
          petListDefault.push({
            name_pet: petObj.name_pet,
            name_kana_pet: petObj.name_kana_pet,
            code_pet: petObj.code_pet,
            value: petObj.id_pet
          })
        })
        petList.value = [...petListDefault]
        if (!isEdit.value && petList.value.length > 0) {
          data.value.id_pet = petList.value[0].value
        }
      }
    }
  } else {
    data.value.id_pet = ''
    data.value.pets = []
    petList.value.length = 0
    petListDefault.length = 0
    showPets.value = false
    data.value.id_customer = ''
  }
  init()
}
const init = () => {
  setErr.value = ''
  if (data.value.id_customer === null || data.value.id_customer === '') {
    data.value.id_pet = ''
    data.value.pets = []
    showPets.value = false
  } else if (data.value.id_customer !== null || data.value.id_customer !== '') {
    showPets.value = true
  }
  if (petListDefault.length < 1) {
    data.value.id_pet = ''
  }
}
function scrollToBottom() {
  setTimeout(() => {
    let element = document.getElementById('threadModal')
    element?.lastElementChild?.scrollIntoView({
      behavior: 'auto',
      block: 'end'
    })
  }, 100)
}
const handleTypeLink = () => {
  if (data.value.linkCategory === null || data.value.linkCategory === 0) {
    showNumberLink.value = false
  } else if (
    data.value.linkCategory !== null ||
    data.value.linkCategory !== 0
  ) {
    showNumberLink.value = true
    data.value.id_link1 = data.value.number_link1
    scrollToBottom()
  }
}
// const handleMemoTempletes = async (templet) => {
//   let confirmMsg = 'このテンプレートを選択しますか?'
//   await mtUtils.confirm(confirmMsg, '確認', 'はい').then((confirmation) => {
//     if (confirmation) {
//       if (templet) {
//         if (data.value.memo_goal?.length > 0) {
//           data.value.memo_goal += '\n' + templet
//         } else {
//           data.value.memo_goal = templet
//         }
//       }
//       mtUtils.autoCloseAlert(aahMessages.success)
//     }
//   })
// }
const handleCloseThread = async () => {
  let confirmMsg =
    'このスレッドをクローズしますか？/nクローズ後はスレッド一覧で非表示になります。'
  await mtUtils.confirm(confirmMsg, '確認', 'はい').then((confirmation) => {
    if (confirmation) {
      if (props.data?.id_message_thread) {
        props.data.flg_closed = true
        props.data.datetime_closed = getDateTimeNow()
        messageStore.updateThreadMessages(
          props.data?.id_message_thread,
          props.data
        )
      }
      mtUtils.autoCloseAlert(aahMessages.success)
    }
  })
}
const handleNameThreadTitle = (value) => {
  if (value === true) {
    data.value.name_thread = autoGeneratedTitle.value
    // Reset any validation errors when auto-title is enabled
    nextTick(() => {
      threadForm.value?.resetValidation()
    })
  } else {
    data.value.name_thread = ''
  }
}
// Change the modal header color when it is closed.
const headerClass = computed(() => {
  if (data.value.flg_closed) {
    return 'inactive-row text-grey-050' // When this record is closed, the header will be grey.
  } else if (data.value.flg_urgent) {
    return 'alert-row text-darkred' // When the modal is under "urgent"
  }
  return '' // Default class
})
const iconName = computed(() => {
  if (data.value.flg_closed) {
    return 'do_disturb_on' // Icon for closed.
  } else if (data.value.flg_urgent) {
    return 'run_circle' // Icon for urgency.
  }
  return '' // Default icon
})
const nonUrgency = computed(() => {
  return !data.value.flg_urgent && !data.value.flg_closed
})

const handleThreadTypeText = () => {
  return data.value.threadClassification == 1
    ? '※ 『報告』：回答者が「確認」できれば終了です。'
    : data.value.threadClassification == 2
      ? '※ 『承認が必要』：回答者による依頼者への「承認」を得られれば終了です。'
      : data.value.threadClassification == 3
        ? '※ 『指示の要求』：回答者が回答する指示内容を依頼者が確認できれば終了です。'
        : data.value.threadClassification == 10
          ? ' ※ 『その他』：スレッドはクローズされるまでオープン状態で維持できます。'
          : data.value.threadClassification == 50
            ? '※ 『処方箋のご予約』'
            : data.value.threadClassification == 60
              ? '※ 『ホテルのご予約』'
              : data.value.threadClassification == 70
                ? '※ 『美容のご予約』'
                : ''
}

const openSearchTemplateModal = async () => {
  if (getTemplates.value.length === 0) {
    await templateStore.fetchTemplates()
  }
  if (
    getTemplates.value &&
    getTemplates.value.length &&
    getTemplates.value.length > 0
  ) {
    textTemplatesList.value = sortBy(getTemplates.value, 'display_order', 'asc')
      .filter((template) => template?.type_text_template === 61)
      .map((template: any) => {
        return {
          title: template.memo_template,
          flg_title: template.flg_title,
          attr: {
            class: template.flg_title ? 'bg-grey-300' : ''
          },
          isSelected: false
        }
      })
    addTemplateModalFlg.value = true
    // let menuOptions: any = sortBy(getTemplates.value, 'display_order', 'asc').filter((template) => template?.type_text_template === 61).map((template: any) => {
    //   return {
    //     title: template.memo_template,
    //     attr: {
    //       class: template.flg_title ? 'bg-grey-300' : ''
    //     },
    //     isSelected: false
    //   }
    // })
    // await mtUtils.smallPopup(OptionModal2, { modelTitle: 'スレッド指示コンテンツのテンプレート', options: menuOptions, data })
    // let selectedOption = menuOptions.find((item: any) => item.isChanged == true)
    // if (selectedOption) {
    //   data.value.memo_goal = data.value.memo_goal ? `${data.value.memo_goal}\n${selectedOption?.title}` : selectedOption?.title
    //   mtUtils.autoCloseAlert(aahMessages.success)
    // }
  }
}

const selectDefaultEmployee = (key: string) => {
  data.value[key] = defaultEmployee
}

const handleUpdateMemo = (value: string) => {
  data.value.memo_goal = value
}

const selectPet = (petValue: string) => {
  if (data.value.id_pet === petValue) {
    // If clicking the same pet, deselect it
    data.value.id_pet = ''
  } else {
    // Select the new pet
    data.value.id_pet = petValue
  }
}

// Define search as a ref holding an object
let search = ref({
  id_customer: data.value.id_customer, // Initial value
  id_pet: data.value.id_pet,           // Initial value
  id_clinic: clinic.value,   // Static value
  flg_complete: 0,
  flg_cancel: 0,
});
// storing list based on category
let numberLinkList = reactive<any>([])
let numberLinkListDefault = reactive<any>([])

// requestlist
import useRequestStore from '@/stores/requests';
const requestStore = useRequestStore();
const { getRequests } = storeToRefs(requestStore);
const requestList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getRequests.value || getRequests.value.length === 0) {
    return [];
  }
  return getRequests.value.map(request => ({
    value: request.number_request, // Assuming number_request is unique and suitable as a value
    label: `${request.number_request} - ${request.title_request}`
  }));
});

// service list
import useServiceDetailStore from '@/stores/service-details'
const serviceDetailStore = useServiceDetailStore()
const { getAllServiceDetails } = storeToRefs(serviceDetailStore)
const serviceList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getAllServiceDetails.value || getAllServiceDetails.value.length === 0) {
    return [];
  }
  return getAllServiceDetails.value.map(service => ({
    value: service.number_service_detail, // Assuming number_service_detail is unique and suitable as a value
    label: `${service.number_service_detail}`
  }));
});

// prescription
import usePrescriptionStore from '@/stores/prescription'
const prescriptionStore = usePrescriptionStore()
const { getAllPrescriptionData } = storeToRefs(prescriptionStore)
const prescriptionList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getAllPrescriptionData.value || getAllPrescriptionData.value.length === 0) {
    return [];
  }
  return getAllPrescriptionData.value.map(prescription => ({
    value: prescription.number_prescription, // Assuming number_service_detail is unique and suitable as a value
    label: `${prescription.number_prescription}`
  }));
});

// memocarte
import useMemoCarteStore from '@/stores/memo-cartes'
const memoCarteStore = useMemoCarteStore()
const { getMemoCartes} = storeToRefs(memoCarteStore)
const memoCarteList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getMemoCartes.value || getMemoCartes.value.length === 0) {
    return [];
  }
  return getMemoCartes.value.map(memo => ({
    value: memo.number_memo_carte, // Assuming number_service_detail is unique and suitable as a value
    label: `${memo.number_memo_carte}`
  }));
});

// task
import useTask from '@/stores/task'
const taskStore = useTask()
const { getTasks } = storeToRefs(taskStore)
const taskList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getTasks.value || getTasks.value.length === 0) {
    return [];
  }
  return getTasks.value.map(task => ({
    value: task.number_task, // Assuming number_service_detail is unique and suitable as a value
    label: `${task.number_task}`
  }));
});

// accounting
import useCartStore from '@/stores/carts'
const cartStore = useCartStore()
const { getCarts } = storeToRefs(cartStore)
const accountingList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getCarts.value || getCarts.value.length === 0) {
    return [];
  }
  return getCarts.value.map(account => ({
    value: account.number_cart, // Assuming number_service_detail is unique and suitable as a value
    label: `${account.number_cart}`
  }));
});

// inject
import useInjectStore from '@/stores/inject'
const injectStore = useInjectStore()
const { getAllInjectData } = storeToRefs(injectStore)
const injectList = computed(() => {
  // Ensure getRequests.value is an array before mapping, or provide a default empty array
  if (!getAllInjectData.value || getAllInjectData.value.length === 0) {
    return [];
  }
  return getAllInjectData.value.map(inject => ({
    value: inject.number_inject, // Assuming number_service_detail is unique and suitable as a value
    label: `${inject.number_inject}`
  }));
});

// Watch for data.value.id_pet changes
watch(() => data.value.id_pet, async (newValue, oldValue) => {
  if (isInitializing.value) return

  search.value.id_pet = newValue
  data.value.number_link1 = ''

  if (newValue && data.value.id_customer && data.value.linkCategory && data.value.linkCategory !== 0) {
    await fetchLinkData(data.value.linkCategory)
  }
}, { immediate: false })

// Watch for data.value.id_customer changes
watch(() => data.value.id_customer, async (newValue, oldValue) => {
  if (isInitializing.value) return
  
  search.value.id_customer = newValue
  data.value.number_link1 = ''
  if(!data.value.id_customer) {
    data.value.linkCategory = 0
  }
  if (newValue && data.value.id_pet && data.value.linkCategory && data.value.linkCategory !== 0) {
    await fetchLinkData(data.value.linkCategory)
  }
}, { immediate: false })

// Flag to track if component is initializing from props
const isInitializing = ref(true)

// Helper function to fetch link data based on category
const fetchLinkData = async (linkCategory: number) => {
  if (!data.value.id_customer || !data.value.id_pet) {
    return
  }

  search.value.id_customer = data.value.id_customer
  search.value.id_pet = data.value.id_pet

  try {
    switch (linkCategory) {
      case 1: // request
        await requestStore.fetchRequests(search.value);
        setNumberLinkList(requestList.value)
        break
      case 2: // service
        await serviceDetailStore.fetchAllServiceDetails({...search.value, id_sp_clinic: clinic});
        setNumberLinkList(serviceList.value)
        break
      case 3: // prescription
        await prescriptionStore.fetchAllPrescriptionData({...search.value, id_sp_clinic: clinic});
        setNumberLinkList(prescriptionList.value)
        break
      case 4: // memo
        await memoCarteStore.fetchMemoCarte(search.value);
        setNumberLinkList(memoCarteList.value)
        break
      case 5: // task
        await taskStore.fetchTask(search.value);
        setNumberLinkList(taskList.value)
        break
      case 6: // account
        await cartStore.fetchCarts(search.value);
        setNumberLinkList(accountingList.value)
        break
      case 7: // inject
        await injectStore.fetchAllInjectData(search.value);
        setNumberLinkList(injectList.value)
        break
      default:
        setNumberLinkList([])
        break
    }
  } catch (error) {
    console.error('Error fetching link data:', error)
    setNumberLinkList([])
  }
}

// Watch for data.value.linkCategory changes
watch(() => data.value.linkCategory, async (newValue, oldValue) => {
  // Skip validation during initialization from props
  if (isInitializing.value) {
    return
  }

  // Only clear number_link1 when user changes category (not during initialization)
  if (oldValue !== undefined && newValue !== oldValue) {
    data.value.number_link1 = ''
  }

  // Only show alert for user-initiated changes (not during props loading)
  if ((!data.value.id_customer || !data.value.id_pet) && newValue !== 0) {
    await mtUtils.alert('データを連携するには、先にオーナー情報とペット情報を選択してください。')
    data.value.linkCategory = 0
    return
  }

  // Fetch data if category is not 0 and we have required data
  if (newValue && newValue !== 0 && data.value.id_customer && data.value.id_pet) {
    await fetchLinkData(newValue)
  }
}, { immediate: false }) // Don't run immediately to avoid race conditions

const setNumberLinkList = (list: Array<any>) => {
  if (numberLinkList.value) {
    numberLinkList.value.length = 0; // Clear the array inside the ref
  }
  if (numberLinkListDefault) { // Check if it's not null/undefined
    numberLinkListDefault.length = 0; // Clear the array
  }

  if (list.length !== 0) {
    numberLinkList.value = list
    numberLinkListDefault.push(...list)
  } else {
    numberLinkList.value.length = 0
    numberLinkListDefault.length = 0
    numberLinkList.value = []
    numberLinkListDefault.value = []
  }
}

const initialData = async () => {
  await Promise.all([
    templateStore.fetchTemplates(),
    cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [1] })
  ])
  employeesList.value.length = 0
  employeesListDefault.length = 0
  employeesList.value = [...employeesStore.getEmployeeListOptions]
  employeesListDefault.push(...employeesStore.getEmployeeListOptions)
  customerList.value.length = 0
  customerListDefault.length = 0
  customerList.value = [...customerStore.getCustomerListOptions]
  customerListDefault.push(...customerStore.getCustomerListOptions)
  typeDepartments.value.length = 0
  typeDepartmentsDefault.length = 0
  typeDepartments.value = [...cliCommonStore.getCliCommonTypeDepartmentList.map((obj: CliCommon) => ({
    label: obj.name_cli_common,
    value: obj.code_func1
  }))]
  typeDepartmentsDefault.push(...typeDepartments.value)
  if (props.data?.id_message_thread) {
    data.value.name_thread = props.data?.name_thread
    data.value.memo_goal = props.data?.memo_goal
    data.value.id_employee_ask = props.data?.id_employee_ask
    data.value.id_employee_answer = props.data?.id_employee_answer
    data.value.id_pet = props.data?.code_pet
    data.value.id_customer = props.data?.id_customer
    data.value.type_department = props.data?.type_department.toString()
    data.value.threadClassification = props.data?.type_thread
    data.value.linkCategory =
      props.data?.type_link1 === null ? 0 : props.data?.type_link1
    data.value.flg_urgent = props.data?.flg_urgent
    data.value.code_customer = props.data?.code_customer
    data.value.code_pet = props.data?.code_pet
    data.value.number_link1 = props?.data?.number_link1
    data.value.id_link1 = props?.data?.id_link1
    isEdit.value = true
    const messageCustomerThread = typeCustomerThread.find(
      (thrd: any) => thrd?.value === props.data?.type_thread
    )
    if (messageCustomerThread) {
      isCustomerThread.value = true
      data.value.flg_control_name_thread = false
    } else {
      isCustomerThread.value = false
    }
    handleTypeLink()
    if (props.data.id_customer) {
      showPets.value = true
    } else {
      showPets.value = false
    }

    // Update search object with the loaded data
    search.value.id_customer = data.value.id_customer
    search.value.id_pet = data.value.id_pet

     // Load link data for localStorage case
    await fetchLinkData(data.value.linkCategory)

    // Load link data if linkCategory is set and we have customer/pet data
    // This is crucial for edit mode - we need to populate the dropdown options first
    if (data.value.linkCategory && data.value.linkCategory !== 0 &&
        data.value.id_customer && data.value.id_pet) {
      await fetchLinkData(data.value.linkCategory)
      // After fetching the list, the selected value (data.value.number_link1) should be preserved
      // and will be displayed correctly in the dropdown
    }
  } else if (props.data) {
    data.value.memo_goal = props.data?.memo_goal
    data.value.id_pet = props.data?.code_pet
    data.value.id_customer = props.data?.id_customer
    data.value.linkCategory = props.data?.category
    data.value.number_link1 = props.data?.id_link1 
    data.value.id_employee_answer = props.data?.id_employee_answer
    data.value.id_employee_ask = employeeId
    handleTypeLink()

    // Update search object with the loaded data
    search.value.id_customer = data.value.id_customer
    search.value.id_pet = data.value.id_pet

    // Load link data if linkCategory is set and we have customer/pet data
    if (data.value.linkCategory && data.value.linkCategory !== 0 &&
        data.value.id_customer && data.value.id_pet) {
      await fetchLinkData(data.value.linkCategory)
    }
  } else {
    if (localStorage.getItem('pageAction') === 'createThread') {
      let messageThreadData = JSON.parse(localStorage.getItem('createThread'))
      if (messageThreadData && Object.keys(messageThreadData)?.length > 0) {
        data.value.id_customer = messageThreadData.id_customer
        data.value.linkCategory = messageThreadData.linkCategory
        data.value.number_link1 = messageThreadData.number_link1
        data.value.memo_goal = messageThreadData.memo_goal
        await handlePetsList(data.value.id_customer)
        data.value.id_pet = messageThreadData.id_pet
        if (data.value.linkCategory !== null || data.value.linkCategory !== 0) {
          showNumberLink.value = true
          data.value.id_link1 = messageThreadData?.id_link1 ?? ''

          // Load link data for localStorage case
          if (data.value.linkCategory && data.value.linkCategory !== 0 &&
              data.value.id_customer && data.value.id_pet) {
            await fetchLinkData(data.value.linkCategory)
          }
        }
      }
      localStorage.removeItem('pageAction')
      localStorage.removeItem('createThread')
    }
    data.value.id_employee_ask = employeeId
    isEdit.value = false
    isCustomerThread.value = false
    init()
  }
  if (props.id_customer && props.id_pet) {
    data.value.id_customer = props.id_customer
    await handlePetsList(props.id_customer)
    data.value.id_pet = props?.id_pet
  } 
}

onMounted(async () => {
  await initialData()

  // Set initialization flag to false after all data is loaded
  // Use nextTick to ensure all reactive updates are complete
  await nextTick()
  isInitializing.value = false
})


</script>

<template>
  <MtModalHeader @closeModal="closeModal" :class="headerClass">
    <q-toolbar-title class="title2 bold">
      <q-icon v-if="!nonUrgency" :name="iconName" class="q-mr-xs" size="24px" />
      <span v-if="data.flg_closed">[ 終了しました ]</span>
      {{ isCustomerThread ? 'オーナー' : '院内' }}
      {{ isEdit ? 'スレッド' : ' 新規 スレッド' }}
    </q-toolbar-title>
  </MtModalHeader>
  <q-form @submit.prevent.stop="submitnewThread" ref="threadForm">
    <q-card-section class="content flex no-wrap q-px-xl bg-f5f5f5">
      <div id="threadModal" class="fit">
      <!-- Section 1 & 2 -->
        <div class="form-container">
          <!-- Section 1: 何を達成しますか？ (What to achieve?) -->
          <div class="form-section form-section-1 q-mb-lg">
            <div class="section-header q-mb-md">
              <h4 class="text-white bg-grey-600 title4">
                {{ isEdit ? '基本設定' : '何を達成しますか？' }}
              </h4>
              <span class="caption1 regular text-grey-700 q-mt-sm q-ml-sm">
                {{
                  isEdit
                    ? '区分・名称・目的は変更できません。'
                    : 'スレッドの目的区分や目的詳細を設定してください。'
                }}
              </span>
            </div>

            <div class="section-content">
              <!-- Thread Classification -->
              <div v-if="!isCustomerThread" class="q-col-gutter-md q-mb-md">
                <div class="row flex items-center">
                  <div class="col-12 btn-toggle-customer">
                    <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 10px'" :disable="isEdit" v-model="data.threadClassification" no-caps unelevated class="outline-btn-toggle"
                    color="white" text-color="primary" :rules="[aahValidations.validationRequired]" :options="isCustomerThread
                        ? typeCustomerThread
                        : typeThreadClassification
                      " />
                  </div>
                </div>
                <div class="caption1 regular text-grey-700">
                  {{ handleThreadTypeText() }}
                </div>
              </div>

              <!-- Thread Name -->
              <!-- <div class="row q-col-gutter-md q-mb-md">
                <div :class="[
                  isEdit && data.name_thread === undefined ? 'col-12' : 'col-9'
                ]">
                  <MtInputForm type="text" v-model="data.name_thread" :filled="data.flg_control_name_thread" :label="data.flg_control_name_thread
                      ? '保存時に自動でタイトルを生成します'
                      : 'スレッド名*'
                    " :required="!data.flg_control_name_thread" :readonly="isEdit || data.flg_control_name_thread" :rules="threadNameValidationRules" />

                </div>
                <div v-if="!isCustomerThread" class="col-auto">
                  <div v-if="data.name_thread !== undefined">
                    <MtInputForm type="checkbox" :items="[{ label: '自動タイトル' }]" v-model="data.flg_control_name_thread"
                      @update:model-value="handleNameThreadTitle" />
                  </div>
                  <div v-else></div>
                </div>
              </div> -->

              <!-- Thread Purpose -->
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-12">
                  <MtInputForm :rules="[aahValidations.validationRequired]" required type="text" v-model="data.memo_goal" :label="[
                    isEdit
                      ? 'スレッド目的*'
                      : 'スレッド目的（初回の投稿内容になります）*'
                  ]" autogrow class="" :input-style="{ 'min-height': '131px'}" :readonly="isEdit">
                    <template v-if="!isEdit" v-slot:append>
                      <div class="absolute flex items-center" style="margin-left: -84px;">
                        <q-icon color="blue-8" name="add" size="17px" class="cursor-pointer" @click.stop="openSearchTemplateModal">
                          <div class="text-blue-8 title2 text-medium">
                            テンプレート
                          </div>
                        </q-icon>
                      </div>
                    </template>
                  </MtInputForm>
                </div>
              </div>

              <!-- Urgent and Close checkboxes -->
              <div class="row q-col-gutter-md q-mb-xs">
                <div class="col-4">
                  <MtInputForm type="checkbox" :items="[{ label: '至急対応必要' }]" v-model="data.flg_urgent" />
                </div>
              </div>
              <div v-if="isEdit" class="row q-col-gutter-md q-mt-xs">
                <div class="col-4">
                  <MtInputForm type="checkbox" :items="[{ label: '対応完了' }]" v-model="closeThread" />
                </div>
              </div>
            </div>
          </div>

          <!-- Section 2: 誰に質問しますか？ (Who to ask?) -->
          <div v-if="!isCustomerThread" class="form-section form-section-2 q-mb-lg">
            <div class="section-header q-mb-md">
              <h4 class="text-white bg-grey-600 title4">
                {{ isEdit ? '質問者・回答者' : '誰に質問しますか？' }}
              </h4>
              <span class="caption1 regular text-grey-700 q-mt-sm q-ml-sm">
                {{
                  isEdit
                    ? '回答者のみ変更可能です。'
                    : '回答を依頼する担当者を指定してください。'
                }}
              </span>
            </div>

            <div class="section-content">
              <div class="">
                <div class="flex no-wrap items-center" style="flex-wrap: nowrap;">
                  <div class="user-avatar-cirlce">
                    <q-icon name="person" color="white" size="17px"></q-icon>
                  </div>
                  <div class="w-100 q-ml-md">
                    <InputEmployeeOptGroup v-model:selected="data.id_employee_ask" label="質問者名*" :readonly="isEdit"
                      :rules="[aahValidations.validationRequired]" required show-select-default-employee
                      @update:select-default-employee="selectDefaultEmployee('id_employee_ask')" />
                  </div>
                </div>

                <div class="flex justify-center q-mt-lg q-mb-lg">
                  <q-icon name="arrow_downward" style="color: #3C7AD6;" size="19px"></q-icon>
                </div>
                
                <div class="flex no-wrap items-center">
                  <div class="user-avatar-square">
                    <q-icon name="person" color="white" size="17px"></q-icon>
                  </div>
                  <div class="w-100 q-ml-md">
                    <div class="col-12 col-md-4 btn-toggle-departement mb-8px" :class="{ 'btn-toggle-departement-error': departmentToggleError }">
                      <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 10px'" v-model="data.type_department" no-caps unelevated clearable
                      color="white" text-color="primary"
                      lazy-rules required :options="typeDepartments" />
                    </div>
                    <div class="col-12 col-md-4 q-mt-md">
                      <InputEmployeeOptGroup :departmentSelected="data.type_department" v-model:selected="data.id_employee_answer" label="希望回答者 "
                        show-select-default-employee
                        @update:select-default-employee="selectDefaultEmployee('id_employee_answer')" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Section 3: 連携データはありますか？ (Is there linked data?) -->
        <div class="form-section form-section-3">
          <div class="section-header q-mb-md">
            <h4 class="text-white bg-grey-600 title4">
              {{ isEdit ? '連携データ' : '連携データはありますか？' }}
            </h4>
            <span class="caption1 regular text-grey-700 q-mt-sm q-ml-sm">
              {{
                isEdit
                  ? '連携データは更新できます。'
                  : '各種連携データの「番号」を入力してください。'
              }}
            </span>
          </div>

          <div class="section-content">
            <!-- Customer and Pet Selection -->
            <div class="row q-col-gutter-md q-mb-md">
              <div class="col-12 col-md-3">
                <MtFilterSelect v-model:selecting="data.id_customer" v-model:options="customerList"
                  :options-default="customerListDefault" label="診察券番号・オーナー" :disable="props.isCustomerThread"
                  @update:selecting="handlePetsList" />
              </div>
              <div class="col-12">
                <div class="pet-selection-container">
                  <div
                    v-for="pet in petListDefault"
                    :key="pet.value"
                    class="pet-card"
                    :class="{ 'pet-card-selected': data.id_pet == pet.value }"
                    @click="selectPet(pet.value)"
                  >
                    <div class="pet-icon">
                      <MtPetInfoLabel :pet="pet" is-clickable>
                      <template #codePet>
                        {{ pet.code_pet }}
                      </template>
                      <template #kanaFullname>
                        {{ pet.name_kana_pet }}
                      </template>
                      <template #fullname>
                        {{ pet.name_pet }}
                      </template>

                      </MtPetInfoLabel>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Link Category -->
            <div v-if="!isCustomerThread" class="q-mb-md">
              <div class="row items-center q-mb-sm">
                <div class="col-auto">
                  <span class="body1 regular text-grey-700">連携区分 :</span>
                </div>
                <div class="col btn-toggle-departement q-ml-md">
                  <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 10px'" v-model="data.linkCategory" no-caps unelevated
                  color="white" text-color="primary"
                  :options="typeLinkCategory" />
                </div>
              </div>

              <!-- Link Number -->
              <div v-if="data.linkCategory !== 0" class="row q-mt-md">
                <div class="min-w-numberlink">
                  <MtFilterSelect :options-default="numberLinkListDefault" v-model:selecting="data.number_link1" v-model:options="numberLinkList" :rules="[aahValidations.validationRequired]"
                    label="連携番号"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <!-- disabled button only for existing urgent threads, for create new make it always enabled -->
        <q-btn unelevated color="primary" class="q-ml-md" type="submit">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
  <AddTextTemplateModal
    v-model:value="addTemplateModalFlg"
    modelTitle="テンプレート文章を選択"
    :options="textTemplatesList"
    :data="data"
    :single-option="true"
    @update:memo="(value) => handleUpdateMemo(value, typeMemoSelected)"
  />
</template>

<style lang="scss" scoped>
.memoTempleteBox {
  width: 20%;
  max-height: 1420px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
  /* Firefox */
}

.memoTempleteBox::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}

.templeteBorders {
  border: 1px solid #dddddd;
}

.add-template-field {
  .q-field__control {
    align-items: start !important;
  }
}

// Form sections styling
.form-section {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;

  .section-header {
    flex-shrink: 0;
  }

  .section-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.form-container {
  display: flex;
  gap: 20px;
  /* Make sure the container has enough space, e.g., 100% of its parent */
  width: 100%;
}

.form-section-1 {
  /* Calculate: 60% of the total width, minus half of the gap */
  flex: 0 0 calc(60% - 10px); /* 10px is half of the 20px gap */
}

.form-section-2 {
  /* Calculate: 40% of the total width, minus half of the gap */
  flex: 0 0 calc(40% - 10px); /* 10px is half of the 20px gap */
}

:deep(.btn-toggle-customer .q-btn-group > .q-btn-item) {
  border-radius: 5px;
  min-width: 120px!important;
  background-color: #3C7AD6;
}

:deep(.btn-toggle-customer .q-btn-group) {
  flex-wrap: wrap!important;
}

:deep(.mb-8px .q-btn-group > .q-btn-item) {
  margin-bottom: 8px;
}

:deep(.btn-toggle-departement .q-btn-group > .q-btn-item) {
  border-radius: 4px;
  flex-wrap: nowrap!important;
  white-space: nowrap!important;
  margin-right: 8px;
  background-color: #3C7AD6;
}

:deep(.btn-toggle-departement .q-btn-group) {
  flex-wrap: wrap!important;
}

:deep(.btn-toggle-departement .q-btn-group) {
  flex-wrap: wrap!important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="false"]) {
  border: 1px solid #9e9e9e !important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="true"]) {
  border: 1px solid #3C7AD6 !important;
}

// Red border when the q-btn-toggle is in an error state
:deep(.btn-toggle-departement-error .q-btn[aria-pressed="false"]) {
  border-color: red !important;
}

// Red border when the q-btn-toggle is in an error state
:deep(.btn-toggle-departement-selected .q-btn) {
  border: #3C7AD6 1px solid!important;
}

.bg-f5f5f5 {
  background-color: #F5f5f5!important;
}

.outline-btn-toggle {
  outline: 1px solid #3C7AD6;
  padding: 2px;
}

.user-avatar-cirlce {
  width: 20px!important;
  height: 20px!important;
  border-radius: 100px; /* Use 50% for a perfect circle from a square element */
  background-color: black;
  display: flex; /* Enable flexbox */
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
}

.user-avatar-square {
  width: 20px!important;
  height: 20px!important;
  background-color: #3C7AD6;
  display: flex; /* Enable flexbox */
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
}

.pet-selection-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.pet-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  background-color: #eeeeee;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;

  &:hover {
    background-color: #e0e0e0;
  }

  &.pet-card-selected {
    background-color: #fff4cb;
    border-color: #edcc5a;
  }

  .pet-info {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .pet-code {
      font-size: 12px;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }

    .pet-name {
      font-size: 14px;
      font-weight: 500;
      color: #666;
      line-height: 1.2;
    }

    .pet-card-selected & {
      .pet-code {
        color: #8b7355;
      }

      .pet-name {
        color: #8b7355;
      }
    }
  }
}
.categories-radio-input {
  padding: 0px 8px 0px 2px; 
  border-radius: 5px;
  border: 1px solid #9e9e9e!important;
}

.categories-radio-input[aria-checked="true"] {
  border: 1px solid #3C7AD6 !important;
  background-color: rgba(60, 122, 214, 0.3);
}

.categories-radio-input .q-radio__inner--truthy {
  color: #3C7AD6;
}

.min-w-numberlink {
  min-width: 650px;
}
</style>
