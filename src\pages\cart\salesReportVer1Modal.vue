<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'

import useCartStore from '@/stores/carts'
import useClinicStore from '@/stores/clinics'
import useCustomerStore from '@/stores/customers'

import { ref, onMounted, computed } from 'vue'

import dayjs from 'dayjs'
import SalesSummaryTab from './components/summary/SalesSummaryTab.vue'
import CustomerSummaryTab from './components/summary/CustomerSummaryTab.vue'

const cartStore = useCartStore()
const customerStore = useCustomerStore()
const clinicStore = useClinicStore()

const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

// State
const searchData = ref({
  target_date: dayjs().format('YYYY-MM-DD'),
  flg_completed: 1
})

// For Customers Tab
const customerSearchData = ref({
  date_start: '',
  date_end: ''
})

const summary = ref({})
const selectedTab = ref('sales')

const loadedTabs = ref({ sales: false, customers: false })

const customerSummary = ref({})

const fetchSummary = async () => {
  if (loadedTabs.value.sales) return
  const queryParams = { ...searchData.value }
  const res = await cartStore.fetchSalesSummary(queryParams)
  summary.value = res.data.data
  loadedTabs.value.sales = true
}

// Fetch Customer & Pet Summary
const fetchCustomerSummary = async () => {
  if (loadedTabs.value.customers) return
  if (!customerSearchData.value.date_start || !customerSearchData.value.date_end) {
    getDefaultCustomerSearchDates()
  }
  const queryParams = { ...customerSearchData.value }
  const res = await customerStore.fetchCustomerPetSummary(queryParams)
  customerSummary.value = res.data.data
  // customerSummary.value = prepareCustomerPetSummary(res.data.data)
  loadedTabs.value.customers = true
}

// Handle Tab Change (Load Data Only If Not Loaded)
const handleTabChange = () => {
  if (!loadedTabs.value[selectedTab.value]) {
    if (selectedTab.value === 'sales') fetchSummary()
    else if (selectedTab.value === 'customers') fetchCustomerSummary()
  }
}

// Handle Date Change (Reload All Tabs)
const handleDateChange = async () => {
  loadedTabs.value.sales = false
  fetchSummary()
  // Object.keys(loadedTabs.value).forEach((tab) => (loadedTabs.value[tab] = false)) // Reset all tabs
  // if (selectedTab.value === 'sales') fetchSummary()
  // else if (selectedTab.value === 'customers') fetchCustomerSummary()
}

// Handle Completion Status Change
const handleFlgCompletedChange = async () => {
  loadedTabs.value.sales = false
  fetchSummary()
  // Object.keys(loadedTabs.value).forEach((tab) => (loadedTabs.value[tab] = false)) // Reset all tabs
  // if (selectedTab.value === 'sales') fetchSummary()
  // else if (selectedTab.value === 'customers') fetchCustomerSummary()
}

const handleCustomerDateChange = async () => {
  loadedTabs.value.customers = false
  fetchCustomerSummary()
}

const getDefaultCustomerSearchDates = () => {
  const today = dayjs()
  const month = clinicStore.getClinic?.month_business_year_start || 1
  const day = clinicStore.getClinic?.date_business_year_start || 1

  const thisYearStart = dayjs(`${today.year()}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`)
  const usePrevYear = today.isBefore(thisYearStart)
  const startYear = usePrevYear ? today.year() - 1 : today.year()
  const startDate = dayjs(`${startYear}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`)
  const endDate = startDate.add(1, 'year').subtract(1, 'day')

  customerSearchData.value.date_start = startDate.format('YYYY-MM-DD')
  customerSearchData.value.date_end = endDate.format('YYYY-MM-DD')
}

onMounted(async () => {
  await fetchSummary()
})
</script>

<template>
  <div style="width: calc(100vw - 50px); overflow-x: hidden">
    <!-- Modal Header -->
    <MtModalHeader @closeModal="closeModal" class="q-mr-sm">
      <q-toolbar class="text-primary q-pa-none q-mr-sm">
        <q-toolbar-title class="title2 bold text-grey-900">日報</q-toolbar-title>
        <div class="q-gutter-md row">
          <template v-if="selectedTab === 'sales'">
            <MtFormInputDate
              v-model:date="searchData.target_date"
              outlined
              label="会計日"
              type="date"
              autofocus
              @update:date="handleDateChange" />
            <MtFormRadiobtn
              v-model="searchData.flg_completed"
              label="全て"
              val=""
              @update:modelValue="handleFlgCompletedChange" />
            <MtFormRadiobtn
              v-model="searchData.flg_completed"
              label="完了会計のみ"
              :val="1"
              @update:modelValue="handleFlgCompletedChange" />
          </template>

          <template v-else-if="selectedTab === 'customers'">
            <MtFormInputDate
              v-model:date="customerSearchData.date_start"
              outlined
              label="開始日"
              type="date"
              @update:date="handleCustomerDateChange" />
            <MtFormInputDate
              v-model:date="customerSearchData.date_end"
              outlined
              label="終了日"
              type="date"
              @update:date="handleCustomerDateChange" />
          </template>

          <div class="right">
            <span class="title">
              {{ clinicStore.getClinic?.code_clinic }}
              {{ clinicStore.getClinic?.name_clinic_display }}
            </span>
            <span class="caption1 regular">
              出力日:
              {{
              new Date().toLocaleString('ja-JP', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
              })
              }}
            </span>
          </div>
        </div>
      </q-toolbar>
    </MtModalHeader>

    <!-- Content Section -->
    <q-card-section class="q-px-lg content" style="height: calc(100dvh - 100px)">
      <q-tabs v-model="selectedTab" class="text-primary" @update:model-value="handleTabChange">
        <q-tab name="sales" label="売上サマリー" />
        <q-tab name="customers" label="顧客サマリー" />
      </q-tabs>
      <q-tab-panels v-model="selectedTab" class="non-scrolled" animated>
        <q-tab-panel name="sales">
          <!-- Summary Section -->
          <SalesSummaryTab :searchData="searchData" :summary="summary" />
        </q-tab-panel>
        <!-- CUSTOMERS TAB -->
        <q-tab-panel name="customers">
          <CustomerSummaryTab :searchData="searchData" :customerSummary="customerSummary" />
        </q-tab-panel>
      </q-tab-panels>
    </q-card-section>
  </div>
</template>

<style scoped>
.report-background {
  background: #f1f1f1;
  border-radius: 8px;
  border: 1px solid #dfdfdf;
}
.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.left {
  display: flex;
  gap: 8px; /* Adjust gap between the first two fields */
}

.right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.title {
  font-weight: bold;
  font-size: 16px;
}

.summary-section {
  border-radius: 4px;
}

.table-header {
  background-color: #f5f5f5;
  font-weight: bold !important;
}

.date-col {
  padding: 8px;
}

.cell-content {
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
}
.font-13px {
  font-size: 13px;
}

.font-15px {
  font-size: 15px;
}

.digit-highlight-yellow {
  font-size: 15px;
  background-color: rgba(255, 255, 0, 0.3);
  padding: 0 0.3em;
  display: inline-block;
}

.digit-highlight-blue {
  font-size: 15px;
  background-color: rgba(108, 255, 255, 0.3);
  padding: 0 0.3em;
  display: inline-block;
}

.digit-highlight-green {
  font-size: 15px;
  background-color: rgba(43, 255, 0, 0.3);
  padding: 0 0.3em;
  display: inline-block;
}

.digit-highlight-red {
  font-size: 15px;
  background-color: rgba(230, 149, 255, 0.3);
  padding: 0 0.3em;
  display: inline-block;
}

.label-grey {
  font-size: 13px;
  color: #444;
  padding-right: 10px;
}

.chart-title {
  text-align: center;
  color: #333;
  font-size: 16px;
  margin-top: 30px;
  margin-bottom: 5px;
}
</style>
