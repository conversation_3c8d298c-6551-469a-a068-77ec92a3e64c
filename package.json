{"name": "aah", "private": true, "version": "0.0.0", "scripts": {"start": "vite --open", "local": "vite serve --mode local", "dev": "vite serve --mode dev --port 3001 --host", "stg": "vite serve --mode stg", "prod": "vite serve --mode prod", "build:dev": "vite build --mode dev && git rev-parse --short HEAD > dist/version.txt", "build:stg": "vite build --mode stg && git rev-parse --short HEAD > dist/version.txt", "build:prod": "vue-tsc --noEmit && vite build --mode prod && git rev-parse --short HEAD > dist/version.txt", "preview": "vite preview", "lint": "eslint --ext .js,.vue src", "postinstall": "patch-package"}, "dependencies": {"@liripeng/vue-audio-player": "1.6.2", "@pdf-lib/fontkit": "^1.1.1", "@quasar/extras": "1.16.7", "@quasar/quasar-ui-qcalendar": "^4.0.0-beta.19", "axios": "^0.27.2", "chart.js": "^4.4.7", "dayjs": "^1.11.7", "dompurify": "^3.1.0", "embla-carousel-vue": "^8.6.0", "embla-carousel-wheel-gestures": "^8.0.2", "encoding-japanese": "^2.2.0", "fabric": "^5.3.0", "html2pdf.js": "^0.9.3", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "libphonenumber-js": "^1.12.7", "lodash": "^4.17.21", "nanoid": "^4.0.2", "patch-package": "^8.0.0", "pinia": "^2.0.28", "pinia-plugin-persistedstate": "^3.2.0", "quasar": "2.13.0", "set-interval-async": "^3.0.3", "spark-md5": "^3.0.2", "vue": "3.5.13", "vue-advanced-cropper": "^2.8.9", "vue-chartjs": "^5.3.2", "vue-draggable-resizable": "^3.0.0", "vue-qrcode-reader": "^5.5.1", "vue-router": "4.2.5", "vuedraggable": "^4.1.0", "wanakana": "^5.3.1", "zipson": "^0.2.12", "zxcvbn": "^4.4.2"}, "devDependencies": {"@quasar/vite-plugin": "1.3.0", "@types/encoding-japanese": "^2.2.1", "@types/lodash": "^4.14.191", "@types/node": "^17.0.45", "@typescript-eslint/eslint-plugin": "^6.21.0", "@vitejs/plugin-vue": "^2.3.4", "@vue/eslint-config-airbnb": "^8.0.0", "components": "^0.1.0", "eslint": "^8.57.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.26.0", "sass": "1.32.12", "typescript": "^5.4.5", "vite": "^2.9.15", "vite-plugin-compression": "^0.5.1", "vite-plugin-html-env": "^1.2.8", "vue-tsc": "^0.34.17"}}