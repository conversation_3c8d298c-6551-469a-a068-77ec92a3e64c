<script lang="ts" setup>
import { defineAsyncComponent,onMounted, ref, computed } from 'vue'

// Components
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
// Utilities
import mtUtils from '@/utils/mtUtils'
import { formatDate, getDateToday, getDaysBefore, getCustomerLabelColor, getPetImageUrl, handleImageError } from '@/utils/aahUtils'

// Stores
import useCommonStore from '@/stores/common'
import useServiceDetailStore from '@/stores/service-details'
import useCategoryStore from '@/stores/categories'


import ViewPetDetailModal from '../../master/customerPet/ViewPetDetailModal.vue'
import UpdateServiceDetailModal from '@/pages/request/serviceDetail/UpdateServiceDetailModal.vue'
import { DmPrintMode, GenerateCustomersCallbackParams } from '@/pages/master/customerPet/SelectDmPrintTemplates.vue'
import { regularAttributes, regularAttributesWithCustom } from '@/utils/pdfAttributes/regular'
import _ from 'lodash'
import useCustomerStore from '@/stores/customers'
import { GenerateCustomersCallback } from '@/pages/master/customerPet/SelectDmPrintTemplates.vue'


const SelectDmPrintTemplates = defineAsyncComponent(
  () => import('@/pages/master/customerPet/SelectDmPrintTemplates.vue')
)


// Store nitialization
const commonStore = useCommonStore()
const serviceDetailStore = useServiceDetailStore()
const categoryStore = useCategoryStore()
const customerStore = useCustomerStore()



const props = withDefaults(defineProps<{ isSearch: boolean }>(), {
  isSearch: false
})

const emits = defineEmits(['close'])

function closeModal() {
  emits('close')
}

const tableRef = ref(null)

const columns = ref([
  {
    name: 'number_service_detail',
    label: '治療サービス番号',
    field: 'number_service_detail'
  },
  {
    name: 'datetime_service',
    label: '実施日',
    field: 'datetime_service',
    align: 'left',
    style: 'width:18%'
  },
  {
    name: 'customer_name',
    label: 'オーナー',
    field: 'customer_name',
    align: 'left',
    style: 'width: 10%;'
  },
  {
    name: 'pet_name',
    label: 'ペット名',
    field: 'pet_name',
    align: 'left',
    style: 'width: 13%'
  },
  {
    name: 'name_item_service',
    label: '治療サービス明細',
    field: 'name_item_service',
    align: 'left',
    style: 'width: 20%'
  },
  {
    name: 'name_category1',
    label: '大分類',
    field: 'name_category1',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'name_category2',
    label: '中分類',
    field: 'name_category2',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'flg_complete',
    label: '完了',
    field: 'flg_complete',
    align: 'left',
    style: 'width: 5%'
  }
])

const search = ref({
  datetime_service_start: getDaysBefore(7),
  datetime_service_end: getDateToday(),
  number_service_detail: null,
  name_item_service: null,
  id_customer: null,
  id_pet: null,
  flg_complete: false,
  id_category1: "100272",
  id_category2: "100523",
  id_disease: null
})

const nextPage = ref(null)
const dmSelectionState = ref<'selecting' | 'none'>('none')

const dmSelectionCount = computed(() => {
  return getAllServiceDetails.value.filter((v) => v.checked).length
})

const downloadDmLabel = computed(() => {
  return dmSelectionState.value === 'selecting' ?
    (dmSelectionCount.value === 0 ? '印刷をキャンセル' : '印刷を続ける') : 'ハガキ印刷'
})

const clearSearch = () => {
  search.value.datetime_service_start = null
  search.value.datetime_service_end = null
}

const searchData = async () => {
  if (typeof search.value.date_range_list != 'string') {
    search.value.date_range_list = JSON.stringify(search.value.date_range_list)
  }

  await Promise.all([
    useCommonStore().fetchPreparationCommonList({ code_common: [1] }),
    serviceDetailStore.fetchAllServiceDetails({
      ...search.value,
      page_size: 350
    })
  ])
  getAllServiceDetails.value = serviceDetailStore.getAllServiceDetails.filter((v) => {
    return v.code_category2.includes('CO5')
  }).map((v) => ({
    ...v,
    checked: false
  }))
  handleToggleDmSelection(true)
}

const onRowClick = async (row: any) => {
  await serviceDetailStore.fetchServiceDetailById(row.id_service_detail)
  await mtUtils.mediumPopup(UpdateServiceDetailModal)
}

const openDetailPet = async (row: any) => {
  await mtUtils.popup(ViewPetDetailModal, {
    id_customer: row.id_customer,
    id_pet: row.id_pet,
    code_customer: row.code_customer,
    code_pet: row.code_pet,
    tab: row.tab,
    fromPage: '',
    pageTitle: ''
  })
  await search()
}

const categoryName = (value: any) =>
  categoryStore.getAllCategories.find((v) => v.value === value)?.label

const getAllServiceDetails = ref([])

const downloadCompleteDmPdf = async (rows: any) => {
  const customerToFetch = rows.filter((v: any) => v.id_customer && v.checked).map((v: any) => v.id_customer)
  const customerWithAddressAndTelRespose = customerToFetch.length > 0 ? await useCustomerStore().fetchCustomersWithAdressesAndTel({
    ids: customerToFetch.join(',')
  }) : null
  await mtUtils.smallPopup(SelectDmPrintTemplates, {
    mode: 'mixedSource.injectList' as DmPrintMode,
    callBack: () => {
      const generateCustomers: GenerateCustomersCallback = (params: GenerateCustomersCallbackParams) => {
        const { data } = params
        let laterAddOnMappedAttributes: Record<string, any>[] = []
        const customersData = data.filter((ccData: Record<string, any>) => {
          // Check if at least one property has a value
          return Object.values(ccData).some(value => 
            value !== undefined && 
            value !== null && 
            value !== ''
          );
        }).map((ccData: Record<string, any>, index: number) => {
          const clinicData = JSON.parse(localStorage.getItem('clinic') || '{}')
          const petData = ccData?.pet ?? {}
          // @note - to avoid BE error as it does not needed 
          if(_.has(petData, 'type_title_pet_customer_updated')) {
            delete petData.type_title_pet_customer_updated
          }
          const customerWithAddressAndTel = customerWithAddressAndTelRespose?.data?.customers ?? null
          if (customerWithAddressAndTel && customerWithAddressAndTel.length > 0 && ccData?.customer) {
            const customer = customerWithAddressAndTel.find((v: any) => v.id_customer == ccData?.customer?.id_customer)
            if(customer) {
              ccData.customer = customer
            }
          }
          return {
            id_clinic: clinicData,
            id_pet: petData ?? {},
            id_customer: ccData?.customer ?? {},
            booking: {}
          }
        })
        return {
          rowData: customersData,
          addOnAttributes: laterAddOnMappedAttributes,
          addOnAttributeMapping: regularAttributes.concat(regularAttributesWithCustom)
        }
      }
      return {
        rowList: rows.filter((v: any) => v.checked),
        generateCustomers
      }
    },
    type_prints: [1]
  })
}

const handleDownloadDmPdf = async () => {
  const hideSelection = () => {
    dmSelectionState.value = 'none'
    columns.value.splice(0, 1)
  }
  if (dmSelectionState.value === 'selecting') {
    if(dmSelectionCount.value === 0) {
      hideSelection()
      return
    }
    await mtUtils
      .confirm(`${dmSelectionCount.value}件のDMをダウンロードしますか？`,
        '印刷テンプレートを続行',
        '実行',
        null,
        null,
        null,
        {},
        true
    )
      .then((confirmation) => {
        if (confirmation) {
          downloadCompleteDmPdf(getAllServiceDetails.value)
        }
      }).then(() => {
        hideSelection()
      })
  } else {
    dmSelectionState.value = 'selecting'
    columns.value.unshift({
      name: 'checkbox',
      label: '',
      field: 'checked', 
      style: 'width:5%;',
      overLoad: true
    })
  }
}

function handleToggleDmSelection(value: any) {
  getAllServiceDetails.value = getAllServiceDetails.value.map((r: any) => ({ ...r, checked: value }))
}

const getCustomerInfoLabelProps = (row) => {
  return {
    code: row.customer?.code_customer,
    fullKanaName: `${row.customer?.name_kana_family} ${row.customer?.name_kana_first}`,
    fullName: `${row.customer?.name_family} ${row.customer?.name_first}`,
    colorType: row.customer?.type_customer_color,
  }
}

onMounted(async () => {
  await searchData()
})
</script>

<template>
  <div>
    <MtModalHeader @closeModal="closeModal">
      <div class="row gap-2 items-center flex-1">
        <q-toolbar-title class="text-grey-900 title2 bold prescription-title">
          猶予対応一覧
        </q-toolbar-title>
        <MtFormInputDate
          v-model:date="search.datetime_service_start"
          autofocus
          label="接種日：Start"
          outlined
          type="date"
          class="col-2"
        />
        <MtFormInputDate
          v-model:date="search.datetime_service_end"
          label="接種日：end"
          outlined
          type="date"
          class="col-2"
        />
        <!-- <q-btn outline @click="openSearchModal">
          検索条件
        </q-btn> -->
        <q-btn
          outline
          unelevated
          @click="clearSearch()"
        >
          <span> クリア </span>
        </q-btn>
        <q-btn
          color="primary"
          :label="downloadDmLabel"
          @click="handleDownloadDmPdf"
        />
        <!-- <q-btn
          outline
          class="q-pl-sm q-pr-md"
          @click="downloadCSV"
        >
          <q-icon name="download" class="text-grey-700 q-mr-xs" />
          <span> CSV </span>
        </q-btn> -->
        <q-btn
          tabindex="3"
          color="grey-800"
          text-color="white"
          class="q-mr-sm"
          unelevated
          @click="searchData"
        >
          <q-icon name="search" size="20px" />
          <span class="hide-tablet">
            検索
          </span>
        </q-btn>
      </div>
    </MtModalHeader>
    <q-scroll-area
      class="separate-scrollbar"
    >
    <MtTable2
        ref="tableRef"
        :columns="columns"
        :rows="getAllServiceDetails"
        :style="{ height: 'calc(100vh - 110px)' }"
        flat
        :rowsBg="true"
        @checked="handleToggleDmSelection"
    >
      <template v-slot:row="{ row }">
        <td
          class="cursor-pointer"
          v-for="(col, index) in columns"
          :key="index"
          @click="onRowClick(row)"
          :class="{
            flg_cancel_row: row.flg_cancel,
            flg_complete_row: row.flg_complete
          }"
        >
          <div
            v-if="col.field == 'checked' && dmSelectionState === 'selecting'"
            key="checked"
            auto-width
          >
            <MtFormCheckBox 
              v-model:checked="row.checked"
            />
          </div>
          <div
            v-if="col.field == 'number_service_detail'"
            auto-width
            key="number_service_detail"
          >
            {{ row.number_service_detail }}
          </div>
          <div
            v-if="col.field == 'datetime_service'"
            auto-width
            key="datetime_service"
          >
            <div class="row no-wrap">
              {{ formatDate(row.datetime_service_start) }} ~
              {{ formatDate(row.datetime_service_end) }}
            </div>
          </div>

          <div
            v-if="col.field == 'name_category1'"
            key="name_category1"
            auto-width
          >
            <div class="row no-wrap">
              {{ row.name_category1 }}
            </div>
          </div>
          <div
            v-if="col.field == 'name_category2'"
            key="name_category2"
            auto-width
          >
            <div class="row no-wrap">
              {{ row.name_category2 }}
            </div>
          </div>
          <div
            v-if="col.field == 'customer_name'"
            auto-width
            key="customer_name"
          >
            <div class="column">
              <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(row)" show-customer-code />
            </div>
          </div>
          <div v-if="col.field == 'pet_name'" auto-width key="pet_name">
            <div
              @click.stop="openDetailPet(row)"
              v-if="row.pet"
            >
              <MtPetInfoLabel :pet="row.pet" :show-pet-image="false" />
            </div>
          </div>
          <div
            v-if="col.field == 'name_item_service'"
            auto-width
            key="name_item_service"
          >
            <div class="row no-wrap">
              {{ row.name_item_service }}
            </div>
          </div>
          <div v-if="col.field == 'id_category1'" auto-width key="id_category1">
            {{ categoryName(row.id_category1) }}
          </div>
          <div v-if="col.field == 'id_category2'" key="id_category2">
            {{ categoryName(row.id_category2) }}
          </div>
          <div v-if="col.field == 'flg_complete'" class="text-green">
            <q-icon v-if="row[col.field]" size="24px" name="check_circle" />
          </div>
        </td>
      </template>
    </MtTable2>
    </q-scroll-area>
  </div>
</template>

<style lang="scss" scoped>
.separate-scrollbar {
  height: calc(100vh - 110px);
  width: 100%;
  max-width: 100%;

  :deep(.q-scrollarea__content) {
    max-height: unset !important;
  }
}

.hide-tablet {
  @media screen and (max-width: 1280px) {
    display: none;
  }
}
</style>
