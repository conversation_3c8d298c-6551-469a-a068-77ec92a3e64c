import { defineStore } from 'pinia'
// import { api } from '@/boot/axios'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

export const useServiceGroupStore = defineStore('service_group', {
  state: () => ({
    service_groups: [],
    service_group: null,
    recentServiceGroup: null,
    serviceGroupItems: [],
    pagination: {
      currentPage: 1,
      pageSize: 20,
      nextPage: '',
      previousPage: '',
      itemCount: 0
    }
  }),

  getters: {
    getServiceGroups: (state) => state.service_groups,
    getServiceGroup: (state) => state.service_group,
    getRecentServiceGroup: (state) => state.recentServiceGroup,
    getPageCount: (state) => Math.ceil(state.pagination.itemCount / state.pagination.pageSize),
    getPagination: (state) => state.pagination
  },

  
  // Temporary turn off the presist before stable
  // persist: true,

  actions: {

    selectServiceGroup (id: string | number | null = null) {
      this.service_group = id ? this.service_groups.find((v: any) => v.id_service_group === id) : null
    },

    setPagination(pagination) {
      this.pagination = pagination
    },

    resetServiceGroupList() {
      this.service_groups = []
      this.pagination = {
        currentPage: 1,
        pageSize: 20,
        nextPage: '',
        previousPage: '',
        itemCount: 0
      }
    },

    // refactored all fetch api function to follow code rules of calling api via mtUtils.callApi() function
    async fetchServiceGroups (data = null) {
      try {
        const response: any = await mtUtils.callApi(selectOptions.reqMethod.GET, `/mst/service_groups`, data, true)
        this.service_groups = response.data

        this.pagination = {
          ...this.pagination,
          itemCount: response.count,
          nextPage: response.next ?? '',
          previousPage: response.previous ?? ''
        }
        console.log(response)
      } catch(error) {
        mtUtils.alert(error.message, "Error!")
      }
    },

    async submitServiceGroup (data: object) {
      try {
        const response: any = await mtUtils.callApi(selectOptions.reqMethod.POST, '/mst/service_groups', data)
        this.recentServiceGroup = response
      } catch(error) {
        mtUtils.alert(error.message, "Error!")
      }
    },

    async updateServiceGroup (id: number | string, data: object) {
      try {
        const response: any = await mtUtils.callApi(selectOptions.reqMethod.PUT, `/mst/service_groups/${id}`, data)
        this.recentServiceGroup = response
      } catch(error) {
        mtUtils.alert(error.message, "Error!")
      }
    },

    async destroyServiceGroup (id: number | string) {
      try {
        await mtUtils.callApi(selectOptions.reqMethod.DELETE, `/mst/service_groups/${id}`)
      } catch(error) {
        mtUtils.alert(error.message, "Error!")
      }
    }
  }
})

export default useServiceGroupStore
