import {
  CliCommon,
  EmployeeType
} from '@/types/types'
export const showSelectedPurposes = (petId: number, purposeByPetMap: Map<number, Set<number>>, purposeList: CliCommon[]) => {
  const petPurposes = purposeByPetMap.get(petId)
  if(petPurposes) {
    const selectedPurposes = purposeList
     .filter((v: CliCommon) => petPurposes.has(v.id_cli_common))
     .map((v: CliCommon) => v.label)
        
     return selectedPurposes.join(', ')
  }
  return ''
}

export const showSelectedDoctors = (petId: number, employeeByPetMap: Map<number, Set<number>>, allEmployees: EmployeeType[]) => {
  const doctorsByPet = employeeByPetMap.get(petId)
  if(doctorsByPet) {
    const selectedDoctors = allEmployees
      .filter((v: EmployeeType) => doctorsByPet.has(v.id_employee as number))
      .map((v: EmployeeType) => v.name_family + ' ' + v.name_first)
    
    return selectedDoctors.join(', ')
  }
  return ''
}

export const getSelectPurpose = (petId: number, selectedPurposes: Map<number, Set<number>>, purposeList: CliCommon[]) => { 
  const selectedPurposeIds = selectedPurposes.get(petId)
  if(selectedPurposeIds) {

    const filteredPurposes_ = purposeList.filter(
      (purpose) => selectedPurposeIds.has(purpose.id_cli_common)
    )

    // Extract `text1` values, split by comma, and filter out empty strings
    const list = filteredPurposes_
      .map((ps) => ps?.text1)
      .filter((item) => item !== '')
      .flatMap((item) => item.split(','))

    // Remove duplicates and update `filteredOccupation`
    return [...new Set(list)]
  }
}

export const getSelectPurposeByEmployee = (petId: number, selectedPurposes: number[], purposeList: CliCommon[]) => { 
  if(selectedPurposes && selectedPurposes.length > 0) {

    const filteredPurposes_ = purposeList.filter(
      (purpose) => selectedPurposes.includes(purpose.id_cli_common)
    )

    // Extract `text1` values, split by comma, and filter out empty strings
    const list = filteredPurposes_
      .map((ps) => ps?.text1)
      .filter((item) => item !== '')
      .flatMap((item) => item.split(','))

    // Remove duplicates and update `filteredOccupation`
    return [...new Set(list)]
  }
}

export const openLargeModal = () => {
  return true
  // return window.innerWidth < 1300
}