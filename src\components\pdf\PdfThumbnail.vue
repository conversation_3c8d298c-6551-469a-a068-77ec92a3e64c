<script setup>
import { ref, onMounted } from 'vue'

const pdfCanvas = ref(null)
const props = defineProps({
  pdfPath: String
})

const loadPdfJsScript = () => {
  return new Promise((resolve, reject) => {
    if (window.pdfjsLib) {
      resolve()
      return
    }
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js'
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  });
};

const renderPdfThumbnail = async (pdfUrl, canvas) => {
  await loadPdfJsScript()
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js'

  const pdf = await pdfjsLib.getDocument(pdfUrl).promise
  const page = await pdf.getPage(1)

  const scale = 1
  const viewport = page.getViewport({ scale })

  const context = canvas.getContext('2d')
  canvas.width = viewport.width
  canvas.height = viewport.height

  await page.render({ canvasContext: context, viewport }).promise
}

onMounted(async () => {
  if (props.pdfPath) {
    await renderPdfThumbnail(props.pdfPath, pdfCanvas.value)
  }
})
</script>
<template>
  <div>
    <canvas ref="pdfCanvas" style="width: 100%; max-height: 250px; border: 1px solid #ccc;"></canvas>
  </div>
</template>

