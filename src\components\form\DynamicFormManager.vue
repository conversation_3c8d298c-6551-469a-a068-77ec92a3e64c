<template>
  <div class="dynamic-form-manager">
    <DynamicFormBuilder
      v-model="parsedFormFields"
      @update:model-value="handleFormFieldsUpdate"
    />
  </div>
</template>

<script>
import { defineComponent, ref, watch, onMounted } from 'vue';
import DynamicFormBuilder from './DynamicFormBuilder.vue';

// Function to generate UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export default defineComponent({
  name: 'DynamicFormManager',
  components: {
    DynamicFormBuilder
  },
  props: {
    /**
     * JSON string from the API or JSON object representing the form fields
     * Expected format from API:
     * [
     *   {
     *     "key": "vaccinationStatus",
     *     "type": "radio",
     *     "label": "ワクチン接種は済んでいますか？",
     *     "required": true,
     *     "options": [
     *       {
     *         "value": "completed",
     *         "label": "接種済"
     *       }
     *     ]
     *   }
     * ]
     */
    modelValue: {
      type: [String, Array],
      default: () => "[]"
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const parsedFormFields = ref([]);

    // Parse the incoming value (either string or array)
    const parseValue = (val) => {
      if (!val) return [];
      
      // If it's a string, try to parse it as JSON
      if (typeof val === 'string') {
        try {
          const parsed = JSON.parse(val);
          // Ensure each field has a key - add UUID if missing
          return ensureKeys(parsed);
        } catch (e) {
          console.error('Failed to parse form fields JSON:', e);
          return [];
        }
      }
      
      // If it's already an array, ensure keys exist
      if (Array.isArray(val)) {
        return ensureKeys(val);
      }
      
      return [];
    };

    // Function to ensure each field has a unique key
    const ensureKeys = (fields) => {
      if (!Array.isArray(fields)) return [];
      
      return fields.map(field => {
        // If the field has no key or empty key, generate a UUID
        if (!field.key || field.key.trim() === '') {
          return { ...field, key: generateUUID() };
        }
        return field;
      });
    };

    // Parse the initial value when component is mounted
    onMounted(() => {
      parsedFormFields.value = parseValue(props.modelValue);
    });

    // Watch for changes in the modelValue prop
    watch(() => props.modelValue, (newVal) => {
      parsedFormFields.value = parseValue(newVal);
    });

    // When form fields are updated, emit the updated value
    const handleFormFieldsUpdate = (updatedFields) => {
      // Ensure all fields have keys
      const fieldsWithKeys = ensureKeys(updatedFields);
      
      // If your API expects a JSON string, use the commented line instead
      // const value = JSON.stringify(fieldsWithKeys);
      
      // If your API expects an array object:
      const value = fieldsWithKeys;
      
      emit('update:modelValue', value);
    };

    return {
      parsedFormFields,
      handleFormFieldsUpdate
    };
  }
});
</script> 