<script lang="ts" setup>
import { ref } from 'vue'

import MtModalHeader from '@/components/MtModalHeader.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'

import useCartDetailStore from '@/stores/cart-details'

const props = defineProps({ id_cart_detail_list: Array, id_cart: String  })

const data = ref({
  id_employee_sales: null
})

const loading = ref(false)

const defaultEmployee = JSON.parse(
  localStorage.getItem('id_employee')
) as string

const emits = defineEmits(['close'])

const closeModal = () => {
  emits('close')
}

const selectDefaultEmployee = () => {
  data.value.id_employee_sales = defaultEmployee
}
const submit = async () => {
  const payload = {
    id_employee_sales: data.value.id_employee_sales,
    id_cart_detail_list: props.id_cart_detail_list,
    id_cart: props.id_cart
  }

  loading.value = true
  await useCartDetailStore().bulkUpdateEmployeeSales(payload)
  loading.value = false
  closeModal()
  return
}
</script>
<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="text-grey-900 title2 boldtitle2">
      売上担当者の一括指定
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section class="content q-px-xl">
    <div class="col-6">
      <InputEmployeeOptGroup
        v-model:selected="data.id_employee_sales"
        department-selected=""
        show-select-default-employee
        @update:select-default-employee="selectDefaultEmployee"
      />
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal()">
        <span>閉じる</span>
      </q-btn>
      <q-btn class="q-ml-md" color="primary" unelevated @click="submit()" :loading=loading :disabled=loading>
        <span>保存</span>
      </q-btn>
    </div>
  </q-card-section>
</template>

<style lang="scss" scoped></style>