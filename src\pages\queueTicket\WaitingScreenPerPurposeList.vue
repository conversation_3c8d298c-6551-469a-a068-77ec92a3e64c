<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import QtIntroAndWaitingScreen from '@/pages/queueTicket/QtIntroAndWaitingScreen.vue'

import useQueueTicketStore from '@/stores/queue_ticket'
import { storeToRefs } from 'pinia'

import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import { useQueueTicketUtils } from './queueTicketUtils'
import {
  QueueTicketType
} from '@/utils/aahUtils'

const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  REFRESH_INTERVAL,
  CLEAR_TICKET_CALLING_SECONDS,
  QUEUE_STATUS,
  clearTimeoutAndIntervals,
  qtToBeCalledPromise,
  timeoutId,
  intervalId,
  fetchRooms
} = useQueueTicketUtils()

const queueTicketStore = useQueueTicketStore()
const { getQueueTicketLists } = storeToRefs(queueTicketStore)

const absentTickets = computed(() => {
  return getQueueTicketLists.value.filter((ticket: QueueTicketType) => 
    ticket.type_status_queue_ticket === QUEUE_STATUS.ABSENT
  )
})

const activeTickets = computed(() => {
  return getQueueTicketLists.value.filter(
    (ticket) =>
      [
        QUEUE_STATUS.WAITING,
        QUEUE_STATUS.IN_PROGRESS
      ].includes(ticket.type_status_queue_ticket)
  )
})

const poOneTickets = computed(() => {
  let queueTickets = []
  queueTickets = activeTickets.value.filter((ticket: QueueTicketType) => ticket.process_order === 1).slice(0, 8)
  for(let i = queueTickets.length + 1; i <= 8; i++) {
    queueTickets.push({})
  }
  return queueTickets
})

const refreshData = async () => {
  await queueTicketStore.fetchQueueTicketList({ today: true })

  let qtCallingIndex = getQueueTicketLists.value.findIndex((queueTicket) => queueTicket.queue_detail?.flg_qt_calling)
  if(qtCallingIndex !== -1) {

    isQtToBeCalled.value = true
    await qtToBeCalledPromise()

    isQtCalling.value = true
    callingQt.value = getQueueTicketLists.value[qtCallingIndex]
    
    clearTimeoutAndIntervals()

    timeoutId.value = setTimeout(async () => {
      let payload = {
        id_queue_ticket: callingQt.value.id_queue_ticket,
        flg_qt_calling: false
      }
      await mtUtils.callApi(selectOptions.reqMethod.POST, 'queue_ticket_calling/', payload)
      await queueTicketStore.fetchQueueTicketList({ today: true })

      isQtCalling.value = false
      intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
    }, CLEAR_TICKET_CALLING_SECONDS)
  }
}

const setupPolling = async () => {
  await fetchRooms()
  await refreshData()
  intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
}

onMounted(setupPolling)

onUnmounted(() => {
  clearTimeoutAndIntervals()
})
</script>
<template>
  <QtIntroAndWaitingScreen />
  <transition name="fade">
    <div class="waiting-screen-container" v-if="!isQtToBeCalled && !isQtCalling">
      <div class="row q-col-gutter-md tickets-section q-px-md">
        <div class="col-8" style="padding-top: 0;">
          <div class="purpose-section">
            <div class="info-text flex justify-center items-center text-center text-white text-weight-bold">まもなくお呼びします</div>
            <div class="tickets-grid">
              <template v-for="(ticket, idx) in poOneTickets" :key="idx">
                <div class="grid-item flex justify-center items-center text-center ">
                  <div class="number-qt text-weight-bold">{{ ticket?.number_queue_ticket }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div class="col-4" style="padding-top: 0;">
          <div class="absents-section">
            <div class="info-text flex justify-center items-center text-center text-white text-weight-bold">受付にお声掛けください</div>
            <div class="content-sec">
              <div class="text-center text-weight-bold absence-text q-my-md">ご不在</div>
              <template v-for="ticket in absentTickets.slice(0, 6)" :key="ticket.id_queue_ticket">
                <div class="item flex justify-center items-center">
                  <div class="number-qt text-center text-weight-bold">{{ ticket.number_queue_ticket }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center bottom-text text-center text-white text-weight-bold">
        診療内容によって診察の順番は前後する場合がございます。予めご了承ください。
      </div>
    </div>
  </transition>
</template>
<style lang="scss" scoped>
.waiting-screen-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(180deg, #EBF5F6 0%, #D3E7EA 100%);
  gap: 20px;
  .tickets-section {
    margin-top: 2vh;
    height: 80vh;
    .purpose-section {
      border-radius: 10px;
      border-bottom: 8px solid #194677;
      border-left: 1px solid #194677;
      border-right: 1px solid #194677;
      .info-text {
        font-size: clamp(4vh, 6vh, 8vh);
        letter-spacing: 10px;
        background-color: #194677;
        height: 10vh;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
      .tickets-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        .grid-item {
          height: 17.5vh;
          border-bottom: 1px solid #194677;
          &:nth-child(even) {
            border-left: 1px solid #194677;
          }
          .number-qt {
            line-height: 1;
            font-size: clamp(10vh, 12vh, 12vh);
          }
        }
      }
    }
    .absents-section {
      border-radius: 10px;
      border-bottom: 8px solid $grey-900;
      background: $grey-200;
      height: 100%;
      .info-text {
        font-size: clamp(4vh, 4vh, 6vh);
        background-color: $grey-900;
        height: 10vh;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
      .content-sec {
        .absence-text {
          line-height: 1;
          font-size: clamp(3vh, 4vh, 4vh);
        }
        .item {
          background: #fff;
          height: 10vh;
          & + .item {
            border-top: 1px solid gray;
          }
          .number-qt {
            line-height: 1;
            font-size: clamp(3vh, 8vh, 8vh);
          }
        }
      }
    }
  }
  .bottom-text {
    margin-top: 2vh;
    height: 12vh;
    font-size: clamp(4vh, 2vw, 2vw);
    background: linear-gradient(180deg, #2F6299 0%, #194677 100%);
  }
}
</style>