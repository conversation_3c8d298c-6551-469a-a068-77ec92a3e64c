import axios from 'axios'

export let secretKey: string | null = null
export let codeClinic: string | null = null

export const setSecretKey = (key: string) => {
  secretKey = key
}

export const setCodeClinic = (code_clinic: string) => {
  codeClinic = code_clinic
}

const koekaruApi = axios.create({
  baseURL: import.meta.env.VITE_KOEKARU_API_URL,
  withCredentials: false
})

koekaruApi.interceptors.request.use((config) => {
  config.headers!['Access-Control-Allow-Headers'] = '*'
  config.headers!['secret-key'] =
    secretKey || import.meta.env.VITE_KOEKARU_SECRET_KEY
  config.headers!['code-clinic'] = codeClinic || import.meta.env.VITE_CLINIC_CODE
  return config
})

export const getInstance = async (code_clinic?: string) => {
  try {
    setCodeClinic(code_clinic || import.meta.env.VITE_CLINIC_CODE)
    let newSecretKey = await koekaruApi.post('/get-resource', {
      code_clinic: codeClinic
    })
    setSecretKey(newSecretKey.data.data.secret_key)
  } catch (error) {
    console.error('Error getting Koekaru API instance:', error)
    throw error
  }
}

export default koekaruApi
