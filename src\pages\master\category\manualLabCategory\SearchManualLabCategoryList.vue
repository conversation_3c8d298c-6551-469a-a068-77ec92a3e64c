<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import UpdateManualLabCategoryModal from './UpdateManualLabCategoryModal.vue'
import mtUtils from '@/utils/mtUtils'
import useCategoriesStore from '@/stores/categories'
import { useRouter, useRoute } from 'vue-router'
import { setPageTitle } from '@/utils/pageTitleHelper'
import { menuHelperContents } from '@/utils/menuHelperContents'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'

const categoryStore = useCategoriesStore()
const route = useRoute()
const router = useRouter()

const pageName = ref('手入力検査名')

const categoryName = ref('')
const categoryLabel = ref('')
const flg_for_lab = ref(false)
const categoryList = ref([])
const flg_category = ref('')

const catList = ref([])

const onRowClick = async (data: any) => {
  let updatedFlg = { value: false }
  await mtUtils.smallPopup(UpdateManualLabCategoryModal, { data, categoryLabel: categoryLabel.value, updatedFlg })
  // categoryList.value = categoryStore.getCategories
  if (updatedFlg.value) {
    search()
  }
}

const openAddModal = async () => {
  let updatedFlg = { value: false }
  const data = {
    flg_for_lab: flg_for_lab.value
  }
  await mtUtils.smallPopup(UpdateManualLabCategoryModal, { categoryLabel: categoryLabel.value, data, updatedFlg })
  search()
  //categoryList.value = categoryStore.getCategories
}

const setCategoryOption = async (value: any) => {
  switch (value) {
    default:
      categoryLabel.value = '手入力検査名'
      flg_category.value = 'flg_for_lab'
      flg_for_lab.value = true
      break
  }

  search()
}

const search = async () => {
  catList.value = []
  await categoryStore.fetchCategories({
    name_category: categoryName.value,
    flg_for_lab: flg_for_lab.value,
    all_data: true
  })

  categoryList.value = categoryStore.getCategories

  var result = transformToTree(categoryList.value)
  catList.value = result
  const category = flg_category.value.split('_')

  router.replace({ query: { type: category[category.length - 1] } })
}

const transformToTree = (arr: any) => {
  const nodes: Record<string, any> = {};
  const tree: any[] = [];

  arr.forEach((obj: any) => {
    const { id_category: id, id_category_parent: parentId } = obj;
    nodes[id] = nodes[id] || { ...obj, children: [] };
    Object.assign(nodes[id], obj);

    if (parentId) {
      nodes[parentId] = nodes[parentId] || { children: [] };
      nodes[parentId].children.push(nodes[id]);
    } else {
      tree.push(nodes[id]);
    }
  });

  const filterTree = (node: any) => {
    node.children = node.children.filter(filterTree);
    return !(node.code_category === 'LB1' && node.id_category === 1);
  };

  return tree.filter(filterTree);
}

const openHelpMenu = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: menuHelperContents.employeeList.title,
    content: menuHelperContents.employeeList.content,
  })
}

onMounted(async () => {
  setCategoryOption(route.query.type ? `flg_for_${route.query.type}` : null)
  categoryList.value = categoryStore.getCategories
  iniCategoryList()
  setPageTitle(pageName.value)
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          {{ pageName }}
        </q-toolbar-title>
        <q-btn dense flat round @click="openHelpMenu">
          <q-icon size="24px" name="help_outline" />
        </q-btn>
        <q-btn
          @click="openAddModal()"
          unelevated
          color="grey-800"
          text-color="white"
          class="q-ml-sm"
        >
          <q-icon size="16px" name="add" />手入力検査名
        </q-btn>
      </q-toolbar>
    </MtHeader>
    <div
      class="row items-center q-px-xl q-pb-xs q-mb-md bg-white full-width"
      :class="i === 0 ? 'q-mt-lg' : ''"
      v-for="(category, i) in catList"
      :key="i"
    >
      <div
        class="children flex items-center q-mt-md"
        v-if="category.children && category.children.length > 0"
      >
        <q-btn
          v-for="(child, n) in category.children"
          unelevated
          color="grey-200"
          text-color="grey-900"
          no-caps
          class="q-ml-md q-mb-md border-outline-600"
          :class="{ 'bg-grey-600 text-white': !child.flg_active }"
          :key="n"
          @click="onRowClick(child)"
          >{{ child.name_category }}
        </q-btn>
      </div>
      <div v-else class="text-grey-500 q-mt-sm">未設定です。</div>
    </div>
  </q-page>
</template>
