<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import mtUtils from '@/utils/mtUtils'
import OptionModal from '@/components/OptionModal.vue'
import useCategoryStore from '@/stores/categories'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import MtModalHeader from '@/components/MtModalHeader.vue'
import { storeToRefs } from 'pinia'
import { CliCommon } from '@/types/types'
import useCliCommonStore from '@/stores/cli-common'

const props = defineProps({
  categoryLabel: String,
  data: Object,
  isNew: { default: false, required: true },
  updatedFlg: { default: false, required: false }
})

const categoryForm = reactive({
  id_category_parent: '2',
  code_category: '',
  name_category: null,
  type_category_layer: '',
  memo_category: null,
  flg_for_medicine: false,
  flg_for_service: false,
  flg_for_disease: false,
  flg_for_task: false,
  flg_for_food: false,
  flg_for_item: false,
  flg_for_other: false,
  flg_for_lab: false,
  flg_approval_required: false,
  flg_active: false,
  flg_disable: false,
  display_order: null,
  type_department: null,
  id_category: null,
  id_clinic: null
})

const emits = defineEmits(['close'])
const categoryStore = useCategoryStore()
const cliCommonStore = useCliCommonStore()
const parentCategoriesOption: any = ref([])
const parentCategoriesDefault: any = reactive([])
const parentRawCategories: any = ref([])
const { getCliCommonTypeDepartmentList } = storeToRefs(cliCommonStore)
const typeDeptList = ref([])
const myForm = ref(null)
const isEdit = ref(false)

const getCategories = computed(() => categoryStore.getAllCategories)

function assignPageData(data: any) {
  if (data) {
    for (let key in data) {
      categoryForm[key] = data[key]
    }
  }
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]

  await mtUtils.littlePopup(OptionModal, { options: menuOptions })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm(aahMessages.delete_ask, aahMessages.delete)
        .then((confirmation) => {
          if (confirmation) {
            categoryStore
              .destroyCategory(categoryForm.id_category)
              .then(async () => {
                await mtUtils.autoCloseAlert(aahMessages.success)
                await categoryStore.fetchPreparationCategories()
                props.updatedFlg.value = true
                emits('close')
              })
          }
        })
    }
  }
}

const submit = () => {
  if (props.data && props.data.id_category) {
    categoryStore
      .updateCategory(categoryForm.id_category, categoryForm)
      .then(() => {
        // categoryStore.fetchCategories({});
        // categoryStore.fetchPreparationCategories()
        props.updatedFlg.value = true
        emits('close')
        mtUtils.autoCloseAlert(aahMessages.success)
      })
  } else {
    categoryForm.id_clinic = JSON.parse(localStorage.getItem('id_clinic'))
    categoryStore.submitCategory(categoryForm).then(() => {
      // await categoryStore.fetchCategories({});
      // await categoryStore.fetchPreparationCategories()
      props.updatedFlg.value = true
      emits('close')
      mtUtils.autoCloseAlert(aahMessages.success)
    })
  }
}

const closeModal = () => {
  emits('close')
}

const isParent = () => {
  return categoryForm.type_category_layer == '1'
}

const handleDisplayOrder = (value: string | number) => {
  if (!value) categoryForm.display_order = null
}

const handleDisableBtn = () => {
  if (isParent()) {
    if (!categoryForm.flg_for_medicine && !categoryForm.flg_for_service && !categoryForm.flg_for_disease &&
      !categoryForm.flg_for_task && !categoryForm.flg_for_food && !categoryForm.flg_for_item && !categoryForm.flg_for_other) {
      return true
    } else {
      return false
    }
  } else {
    false
  }
}

const isIntersecting = (item, propsFlgs) => {
  const flgs = {
    flg_for_lab: item.flg_for_lab
  };

  for (let key in flgs) {
    if (flgs[key] && propsFlgs[key]) {
      return true;
    }
  }
  return false;
};

const getAvailableCategoryCD = async () => {
  try {
    const data = await categoryStore.getCategoryCDCode();
    categoryForm.code_category = data.code_category
  } catch (error) {
    console.error('Failed to fetch category:', error);
  }
}

onMounted(async () => {
  typeDeptList.value = getCliCommonTypeDepartmentList.value.map((obj: CliCommon) => ({
    label: obj.name_cli_common,
    value: obj.code_func1
  }))
  categoryStore.fetchPreparationCategories()
  parentRawCategories.value = getCategories.value

  const propsFlgs = {
    flg_for_lab: props.data.flg_for_lab
  }

  const parent = getCategories.value.filter(
    (item: any) =>
    item?.parentCategory == 1 && item?.value != categoryForm.id_category
  ).filter((data) => isIntersecting(data, propsFlgs));

  parentCategoriesOption.value.length = 0
  parentCategoriesDefault.length = 0
  parentCategoriesOption.value = [...parent]
  parentCategoriesDefault.push(...parentCategoriesOption.value)

  if (!props?.data?.id_category) {
    // Create Case
    isEdit.value = false
    categoryForm.type_category_layer = '2'
    categoryForm.id_category_parent = '2'
    assignPageData(props.data)
    await getAvailableCategoryCD()
  } else {
    isEdit.value = true
    assignPageData(props.data)
  }
})

</script>

<template>
  <q-form ref="myForm" @submit="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        {{ props.categoryLabel }}
      </q-toolbar-title>
      <q-btn v-if="props.data" flat round @click="openMenu" class="q-mx-sm">
        <q-icon name="more_horiz" size="xs" />
      </q-btn>
    </MtModalHeader>
    <q-card-section class="q-px-lg">
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-lg-6 col-md-6 col-sm-6">
          <MtInputForm
            v-model="categoryForm.code_category"
            readonly
            required
            label="手入力検査CD"
            type="text"
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
          <MtInputForm
            v-model="categoryForm.flg_active"
            :items="[{ label: '使用中' }]"
            type="checkbox"
          />
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <MtInputForm
            v-model="categoryForm.name_category"
            required
            autofocus
            :rules="[aahValidations.validationRequired]"
            label="手入力検査名 *"
            type="text"
          />
        </div>
      </div>
      <div class="row q-col-gutter-md q-mb-lg">
        <div class="col-12">
          <MtInputForm
            v-model="categoryForm.memo_category"
            label="メモ"
            type="text"
            autogrow
          />
        </div>
      </div>
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-lg-6 col-md-6 col-sm-6">
          <MtInputForm
            v-model="categoryForm.display_order"
            label="表示順序（0~999; 小を上位表示）"
            type="text"
            @update:modelValue="handleDisplayOrder"
          />
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn class="q-ml-md" color="primary" type="submit" unelevated :disable="handleDisableBtn()">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
