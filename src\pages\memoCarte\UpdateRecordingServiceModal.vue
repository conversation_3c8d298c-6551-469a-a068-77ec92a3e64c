<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import { useConversationStore } from '@/stores/Conversation'
import { event_bus } from '@/utils/eventBus'
import { PetType } from '@/types/types'
import { useRecording } from './useRecording'

const props = withDefaults(
  defineProps<{
    id_employee: string
    datetime_memo_carte: string
    id_customer: string
    id_pet: string
    id_request: string
    number_request: string
    petSelected: PetType
    popup: any
  }>(),
  {
    popup: {}
  }
)

const emits = defineEmits(['close'])

const conversationStore = useConversationStore()

const { recordConfig } =
  useRecording()

const closeModal = () => {
  emits('close')
}


const countdown = ref(false)
const countdownTime = ref(3)



onMounted(async () => {
  countdown.value = true
  const interval = setInterval(() => {
    countdownTime.value -= 1
    if (countdownTime.value === 0) {
      clearInterval(interval)
      conversationStore.setFlgRecording(true)
      conversationStore.setCurrentMic(recordConfig.value.microphone)
      props.popup.recordingStarted = true
      closeModal()
      event_bus.emit('recording-start')
      countdown.value = false
      countdownTime.value = 3
    }
  }, 1000)
})
</script>

<template>
  <q-form class="">
    <MtModalHeader @closeModal="">
      <div class="full-width"></div>
    </MtModalHeader>
    <q-card-section class="content q-px-md">
      <div class="flex row q-gutter-sm justify-center items-center">
        <q-btn round outline fab size="xl" class="bg-white text-primary flex row q-pa-lg" :disable="true"
          style="width: 120px; height: 118px">
          <div class="flex column items-center justify-center">
            <div class="text-grey-900 text-weight-bold" style="font-size: 46px">
              {{ countdownTime }}
            </div>
          </div>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.mobile.platform-ios .content {
  height: auto !important;
}
</style>
